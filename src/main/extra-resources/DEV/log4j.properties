log4j.rootLogger=error, stdout,file

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{ISO8601} [%15.15t] %-5p %c.%M(%L): %x - %m%n

# Direct log messages to a log file
log4j.appender.file=org.apache.log4j.RollingFileAppender
# Add -Dappdata=/path/to/your/appdata to Eclipse Tomcat server setting
log4j.appender.file.File=/tmp/focus.log
log4j.appender.file.MaxFileSize=100MB
log4j.appender.file.MaxBackupIndex=1
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{ISO8601} [%15.15t] %-5p %c.%M(%L): %x - %m%n

# app
log4j.logger.ca.utoronto.med.dc=ERROR

#log4j.category.org.springframework.beans.factory=DEBUG
log4j.logger.org.springframework=ERROR
log4j.logger.org.springframework.jdbc=ERROR
log4j.logger.org.springframework.security=ERROR
log4j.logger.org.springframework.web=ERROR

#hibernate
log4j.logger.org.hibernate=ERROR
log4j.logger.org.hibernate.SQL=ERROR
log4j.logger.org.hibernate.jdbc=ERROR
log4j.logger.org.hibernate.type=ERROR

# DbUnit
log4j.logger.org.dbunit=ERROR