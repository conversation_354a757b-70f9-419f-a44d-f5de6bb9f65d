<?xml version="1.0" encoding="UTF-8"?>
<persistence version="1.0"
	xmlns="http://java.sun.com/xml/ns/persistence" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/persistence http://java.sun.com/xml/ns/persistence/persistence_1_0.xsd">

	<persistence-unit name="projectPU" transaction-type="RESOURCE_LOCAL">
		<provider>org.hibernate.ejb.HibernatePersistence</provider>
		<jta-data-source>jdbc/projectDS</jta-data-source>
		<properties>
			<property name="hibernate.dialect" value="org.hibernate.dialect.SQLServerDialect" />
			<property name="hibernate.show_sql" value="false" />
			<property name="hibernate.format_sql" value="false" />
			<property name="hibernate.use_sql_comments" value="false" />
			<!-- Never enable this for production projects 
			<property name="hibernate.hbm2ddl.auto" value="update" />
			 -->
			 
			<property name="hibernate.ejb.interceptor"
				value="ca.utoronto.med.dc.focus.persistence.audit.AuditLogInterceptor" />
				
			<property name="hibernate.ejb.event.post-insert"
	           	value="org.hibernate.ejb.event.EJB3PostInsertEventListener,org.hibernate.envers.event.AuditEventListener" />
 			<property name="hibernate.ejb.event.post-update"
    	       	value="org.hibernate.ejb.event.EJB3PostUpdateEventListener,org.hibernate.envers.event.AuditEventListener" />
 			<property name="hibernate.ejb.event.post-delete"
        	   	value="org.hibernate.ejb.event.EJB3PostDeleteEventListener,org.hibernate.envers.event.AuditEventListener" />
 			<property name="hibernate.ejb.event.pre-collection-update"
           		value="org.hibernate.envers.event.AuditEventListener" />
 			<property name="hibernate.ejb.event.pre-collection-remove"
           		value="org.hibernate.envers.event.AuditEventListener" />
 			<property name="hibernate.ejb.event.post-collection-recreate"
           		value="org.hibernate.envers.event.AuditEventListener" />
			
		</properties>
	</persistence-unit>
</persistence>  


