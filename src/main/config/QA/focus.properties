# === QA PROPERTIES == #
# === Build Version === #
build-version=${project.version}

# === ActiveMQ === #
jms.enabled=${activemq.enabled}

# === JMS === #
mq.queue.prefix=${activemq.queue.prefix}
mq.brokerURL=tcp://localhost:61616

# === Security === #
security.developmentMode=${project.security.developmentMode}

# === Import Feed Service === #
import.enabled=${project.hris.feed.enable}
hris.feed.email.enabled=true
import.feed.path=/home/<USER>/hrisfeed
import.feed.archive.path=/home/<USER>/hrisfeed/archive
import.feed.test.path=src/test/resources/feed/archive/dummy
# sec min hr dom mon dow (0=Sunday)
hris.feed.schedule=0 5 19 * * 1-5
hris.feed.file.archive.window=1
hris.feed.failure.email.list=<EMAIL>
hris.feed.queue.name=FEED.IMPORT.REQUEST.QUEUE
hris.feed.devel=false
feed.error.schedule=0 0 0 * * *

# === Export for AuthMgr === #
# where Focus sends and listens for export requests
insight.request.queue.name=APPOINTMENTS.EXPORT.REQUEST.QUEUE
# where Focus outputs appointments
insight.export.queue.name=APPOINTMENTS.EXPORT.QUEUE

# === Import for UtorAuth Feed === #
# where Focus listens for utor auth feed #
utorauth.feed.focus.name=${activemq.listener.name.utorauth}

# === Import for Phonebook Feed === #
# where Focus listens for Phone Book feed #
phonebook.feed.focus.name=${activemq.listener.name.phonebook}

# === JDBC === #
driver.class.name=net.sourceforge.jtds.jdbc.Driver
database.url=${db.url}
database.username=${db.username}
database.password=${db.password}
initial.size=10
max.active=50
max.idle=25
min.idle=10

# === Mail === #
mailserver.host=appsmtp.utoronto.ca
# mailserver.port=
# mailserver.username=
# mailserver.password=
email.attachment.dir=${standalone.data.dir}/email.attachments
# retry this many times if feed is in progress
email.retries=3
# wait this long between retries
email.wait.milliseconds=5000
# search error log for this string
email.fail.message=EMAIL_TEMPLATE_FAIL
# mq email queues
email.request.queue.name=EMAIL.REQUEST.QUEUE
email.reply.queue.name=EMAIL.REPLY.QUEUE

# === Mail Message ===#
# email.message=Default email message
# email.from=<EMAIL>
email.development=true

# === HRIS Change Log === #
# this will override email.development setting for change log
hrischangelog.email.enable=true
# retry this many times if feed is in progress
hrischangelog.retries=3
# wait this long between retries
hrischangelog.wait.milliseconds=5000
# search error log for this string
hrischangelog.fail.message=HRIS_CHANGE_LOG_REPORT_FAIL
# limit number of profiles to report on
hrischangelog.profiles.max=2000

# === Constraint Violation Messages === #
#

# === HRIS Address Restricted Types === #
hris.address.addressType.restricted=1,8

# === Import Custom Value === #
import.customvalue.dir=${standalone.data.dir}/customvalueimport/

# === Export Personnel Number Directory === #
# sec min hr dom mon dow (0=Sunday)
export.personnelnumber.enable=${personnelnumber.exporter.enabled}
export.personnelnumber.starttime=0 0 23 * * 1-5
export.personnelnumber.sftp.user=med
export.personnelnumber.sftp.host=xchange.auth.utoronto.ca
export.personnelnumber.sftp.file=med.csv

# === Profile Display Default XML File ===
profile.display.defaults.file=DefaultProfileDisplay.xml
