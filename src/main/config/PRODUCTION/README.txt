Installation Instructions

1. Backup production FOCUS database

2. Backup WAR file. Make a temporary copy of the existing war file outside 
   of the webapps directory. Once the deployment is successful the backup copy
   of the war file may be discarded.

3. Undeploy existing focus app using the tomcat manager. 

4. Check that the existing focus directory and war file have been removed from the 
   webapps directory on the server filesystem.

5. Restart Tomcat server from the command line using 
   >$ sudo service tomcat7 restart

6. Migrate DB by running the following script in order:
    a. 1600_schema_migration.sql
    b. 1601_data_migration.sql

7. Deploy new war file found in this directory.
