<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE tiles-definitions PUBLIC
	"-//Apache Software Foundation//DTD Tiles Configuration 2.1//EN" 
	"http://tiles.apache.org/dtds/tiles-config_2_1.dtd">
<tiles-definitions>
	<definition name="lib.styles.definition">
		<put-list-attribute name="stylesheets">
			<add-attribute
				value="&lt;link rel='stylesheet' type='text/css' href='//maxcdn.bootstrapcdn.com/bootstrap/3.2.0/css/bootstrap.min.css' />"
				type="string" />
			<add-attribute
				value="&lt;link rel='stylesheet' type='text/css' href='/${standalone.path}/resources/css/themes/custom-theme/jquery-ui-1.8.14.custom.css' />"
				type="string" />
			<add-attribute
				value="&lt;link rel='stylesheet' type='text/css' href='/${standalone.path}/resources/css/fixedTableHeader.css' />"
				type="string" />
            <add-attribute
                value="&lt;link rel='stylesheet' type='text/css' href='/${standalone.path}/resources/css/contextMenu.css' />"
                type="string" />				
		</put-list-attribute>
	</definition>

	<definition name="libs.definition" extends="lib.styles.definition">
		<put-list-attribute name="javascriptFiles">
			<add-attribute
				value="&lt;script src='//ajax.googleapis.com/ajax/libs/jquery/1.7.1/jquery.min.js'>&lt;/script>"
				type="string" />
			<add-attribute
				value="&lt;script src='//ajax.googleapis.com/ajax/libs/jqueryui/1.8.21/jquery-ui.min.js'>&lt;/script>"
				type="string" />
<!-- 			<add-attribute
				value="&lt;script src='//maxcdn.bootstrapcdn.com/bootstrap/3.2.0/js/bootstrap.min.js'>&lt;/script>"
				type="string" /> -->
			<add-attribute
				value="&lt;script src='/${standalone.path}/resources/js/lib/jquery.fixedheadertable.min.js' >&lt;/script>"
				type="string" />
            <add-attribute
                value="&lt;script src='//code.jquery.com/jquery-migrate-1.2.1.min.js' >&lt;/script>"
                type="string" />
		</put-list-attribute>
	</definition>

	<definition name="base.definition" extends="libs.definition"
		template="/WEB-INF/jsp/layout.jsp">
		<put-attribute name="title" value="" />
		<put-attribute name="header" value="/WEB-INF/jsp/header.jsp" />
		<put-attribute name="body" value="" />
		<put-attribute name="footer" value="/WEB-INF/jsp/footer.jsp" />
		<!-- JAVASCRIPT -->
		<put-list-attribute name="javascriptFiles" inherit="true">
			<add-attribute
				value="&lt;script src='/${standalone.path}/resources/js/lib/jquery.form.js'>&lt;/script>"
				type="string" />
			<add-attribute
				value="&lt;script src='/${standalone.path}/resources/js/lib/jquery.validate.js'>&lt;/script>"
				type="string" />
			<add-attribute
				value="&lt;script src='/${standalone.path}/resources/js/lib/jquery.validate.additional-methods.js'>&lt;/script>"
				type="string" />
			<add-attribute
				value="&lt;script src='/${standalone.path}/resources/js/lib/jquery.json.min.js'>&lt;/script>"
				type="string" />
			<add-attribute
				value="&lt;script src='/${standalone.path}/resources/js/lib/jquery.bgiframe.js'>&lt;/script>"
				type="string" />
			<add-attribute
				value="&lt;script src='/${standalone.path}/resources/js/lib/jquery.ui.disco-messages.js'>&lt;/script>"
				type="string" />
			<add-attribute
				value="&lt;script src='/${standalone.path}/resources/ckeditor/ckeditor.js'>&lt;/script>"
				type="string" />

			<add-attribute
				value="&lt;script src='/${standalone.path}/resources/ckeditor/adapters/jquery.js'>&lt;/script>"
				type="string" />
			<add-attribute
				value="&lt;script src='/${standalone.path}/resources/js/lib/jquery.contextMenu.js'>&lt;/script>"
				type="string" />

			<!-- CUSTOM JS -->
            <add-attribute
                value="&lt;script src='/${standalone.path}/resources/js/focus/home/<USER>'>&lt;/script>"
                type="string" />
            <add-attribute
                value="&lt;script src='/${standalone.path}/resources/js/focus/home/<USER>'>&lt;/script>"
                type="string" />
		</put-list-attribute>
		<put-list-attribute name="stylesheets" inherit="true">
			<add-attribute
				value="&lt;link rel='stylesheet' type='text/css' href='/${standalone.path}/resources/css/focus-${project.version}.css' />"
				type="string" />
		</put-list-attribute>
	</definition>

	<definition name="login" extends="base.definition">
		<put-attribute name="title" value="Login" />
		<put-attribute name="header" value="" />
		<put-attribute name="body" value="/login.jsp" />
		<put-attribute name="footer" value="/login-footer.jsp" />
	</definition>

	<definition name="logout" extends="standalone.definition">
		<put-attribute name="title" value="Logout" />
		<put-attribute name="body" value="/logout.jsp" />
	</definition>

	<definition name="error" extends="base.definition">
		<put-attribute name="title" value="Error" />
		<put-attribute name="body" value="/WEB-INF/jsp/error.jsp" />
	</definition>

	<definition name="home" extends="base.definition">
		<put-attribute name="title" value="Home Page" />
		<put-attribute name="body" value="/WEB-INF/jsp/home/<USER>" />
	</definition>

	<definition name="profile" extends="base.definition">
		<put-attribute name="title" value="Profile Page" />
		<put-attribute name="body" value="/WEB-INF/jsp/profile/profile.jsp" />
	</definition>

	<definition name="tag" extends="base.definition">
		<put-attribute name="title" value="Tag Page" />
		<put-attribute name="body" value="/WEB-INF/jsp/tag/manage-tags.jsp" />
	</definition>

	<definition name="standalone.definition" extends="lib.styles.definition"
		template="/standalone-layout.jsp">
		<put-attribute name="header" value="/standalone-header.jsp" />
		<put-attribute name="title" value="${standalone.name}" />
		<put-attribute name="body" value="" />
		<put-attribute name="footer" value="/standalone-footer.jsp" />
        <put-list-attribute name="stylesheets" inherit="true">
            <add-attribute
                value="&lt;link rel='stylesheet' type='text/css' href='/${standalone.path}/resources/css/dc-style.css' />"
                type="string" />
        </put-list-attribute>
	</definition>

	<definition name="accessDenied" extends="standalone.definition">
		<put-attribute name="title" value="Access Denied" />
		<put-attribute name="body" value="/accessDenied.jsp" />
	</definition>

	<definition name="dcsysadmin.definition" extends="libs.definition"
		template="/WEB-INF/jsp/dcsysadmin/layout.jsp">
		<put-attribute name="title" value="" />
		<put-attribute name="header" value="/WEB-INF/jsp/dcsysadmin/header.jsp" />
		<put-attribute name="main-menu"
			value="/WEB-INF/jsp/dcsysadmin/main-menu.jsp" />
		<put-attribute name="body" value="" />
		<put-attribute name="footer" value="/WEB-INF/jsp/footer.jsp" />

		<put-list-attribute name="stylesheets" inherit="true">
			<add-attribute
				value="&lt;link rel='stylesheet' type='text/css' href='/${standalone.path}/resources/css/focus-admin-${project.version}.css' />"
				type="string" />
		</put-list-attribute>
		<put-list-attribute name="javascriptFiles" inherit="true">
			<add-attribute
				value="&lt;script src='/${standalone.path}/resources/js/lib/jquery.ui.disco-messages.js'>&lt;/script>"
				type="string" />
            <add-attribute
                value="&lt;script src='/${standalone.path}/resources/js/focus/admin/app-${project.version}.min.js'>&lt;/script>"
                type="string" />
            <add-attribute
                value="&lt;script src='//maxcdn.bootstrapcdn.com/bootstrap/3.2.0/js/bootstrap.min.js'>&lt;/script>"
                type="string" /> 
		</put-list-attribute>
	</definition>

	<definition name="dcSysAdminHome" extends="dcsysadmin.definition">
		<put-attribute name="title" value="DC System Admin Home Page" />
		<put-attribute name="body" value="/WEB-INF/jsp/dcsysadmin/home.jsp" />
	</definition>


</tiles-definitions>