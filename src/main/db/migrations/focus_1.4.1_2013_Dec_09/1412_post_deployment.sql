-- Run this script only after deploying Focus v1.4.1 and performing a Feed Import cycle
CREATE TABLE "dbo"."X_DELETED_PROFILES_2013_DEC" (PROFILE_ID numeric(19,0));
--
insert into X_DELETED_PROFILES_2013_DEC (PROFILE_ID) select p.PROFILE_ID
	from profile p 
	join EXTRACTED_FIELDS xf on xf.EXTRACTEDFIELD_ID=p.EXTRACTEDFIELDS_ID 
	join FEED_SOURCE fs on p.FEEDSOURCE_ID=fs.FEEDSOURCE_ID
	where xf.IS_IN_HRIS_FEED = 'false' and fs.SOURCE <> 'HRIS PSEUDO'
--
delete from [ACTION] where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from ACTION_HISTORY where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from ADDRESS where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from ADDRESS_HISTORY where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from APPOINTMENTDETAILS where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from APPOINTMENTDETAILS_HISTORY where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from BASICPAY where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from BASICPAY_HISTORY where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from CONTRACTELEMENT where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from CONTRACTELEMENT_HISTORY where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from AWARD where EDUCATION_ID in (select EDUCATION_ID from EDUCATION e where e.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC));
delete from AWARD_HISTORY where EDUCATION_ID in (select EDUCATION_ID from EDUCATION e where e.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC));
delete from CAREERPROGRESS where EDUCATION_ID in (select EDUCATION_ID from EDUCATION e where e.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC));
delete from CAREERPROGRESS_HISTORY where EDUCATION_ID in (select EDUCATION_ID from EDUCATION e where e.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC));
delete from GRADAPPT where EDUCATION_ID in (select EDUCATION_ID from EDUCATION e where e.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC));
delete from GRADAPPT_HISTORY where EDUCATION_ID in (select EDUCATION_ID from EDUCATION e where e.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC));
delete from POSTSECED where EDUCATION_ID in (select EDUCATION_ID from EDUCATION e where e.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC));
delete from POSTSECED_HISTORY where EDUCATION_ID in (select EDUCATION_ID from EDUCATION e where e.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC));
delete from PROFDESIGNATION where EDUCATION_ID in (select EDUCATION_ID from EDUCATION e where e.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC));
delete from PROFDESIGNATION_HISTORY where EDUCATION_ID in (select EDUCATION_ID from EDUCATION e where e.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC));
delete from EDUCATION where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from EMAIL where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from EMAIL_HISTORY where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from EMAIL_RECORD where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from OTHERUNIVERSITYAPPOINTMENT where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from OTHERUNIVERSITYAPPOINTMENT_HISTORY where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from PROFILE_TAG where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from HRIS_RULE_EVENT where PROFILE_ID in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from STATICGROUP_PROFILE where PROFILE_ID in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from FIELD_VALUE where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from EMAIL where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from TELEPHONE where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from COMMPREF where PROFILE_ID in (SELECT profile_id from X_DELETED_PROFILES_2013_DEC);
delete from profile where profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC);
delete from PERSONALDATA where PERSONALDATA_ID in (select p2.PERSONALDATA_ID from profile p2 where p2.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC))
delete from EXTRACTED_FIELDS where EXTRACTEDFIELD_ID in (select p2.EXTRACTEDFIELDS_ID from profile p2 where p2.profile_id in (select profile_id from X_DELETED_PROFILES_2013_DEC))