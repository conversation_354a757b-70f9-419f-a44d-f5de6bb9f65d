-- DCFOCUS-67: Update FWU Filter data
update DEPARTMENT_FILTER set PROPERTY_NAME = 'extractedFields.isOimeCa' 
    where DEPARTMENT_ID in (
        select DEPARTMENT_ID from DEPARTMENT where NAME in ('OIME', 'Clinical Affairs')
    )
-- DCFOCUS-41
-- create new FEED SOURCE entry
insert into FEED_SOURCE (DESCRIPTION, SOURCE) values ('WebCV Data', 'WEBCV');
-- add parent field def for WebCV ID
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'WebCV Bridge', 'webcvBridge', 0, 0, 0, fs.feedsource_id, null, 1 from FEED_SOURCE fs 
    where fs.source = 'WEBCV';
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'WebCV ID', 'webcvBridge.webcvId', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, 3 from FEED_SOURCE fs, field_def fd 
    where fs.source = 'WEBCV' and fd.path = 'webcvBridge';
-- DCFOCUS-61
-- NEW Extracted Field CONTRACT_CLINICAL_STATUS
-- ADD DEFINITION FOR contractClinicalStatus FIELD 
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Contract Clinical Status', 'extractedFields.contractClinicalStatus', 0, 0, 0, fs.feedsource_id, fielddef_id, 1 from FEED_SOURCE fs, field_def fd 
    where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields';
--
-- ADD Applications for new IS CUSTOM field
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.contractClinicalStatus')
    and fa.token = 'DISPLAY';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.contractClinicalStatus')
    and fa.token = 'SEARCH_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.contractClinicalStatus')
    and fa.token = 'SEARCH_CHILD';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.contractClinicalStatus')
    and fa.token = 'REPORT_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.contractClinicalStatus')
    and fa.token = 'EMAIL';
--
-- DCFOCUS-68 rename extractedFields.isStaff; add extractedFields.isAppointedStaff; add extractedFields.isAppointedStaff
-- CHANGE LABEL for isStaff
update FIELD_DEF set LABEL='Is PM or USWA' where path='extractedFields.isStaff';
-- DCFOCUS-68 continued
-- ADD DEFINITION FOR isActiveFaculty FIELD 
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Is Active Faculty', 'extractedFields.isActiveFaculty', 0, 0, 10, fs.feedsource_id, fielddef_id, 1 from FEED_SOURCE fs, field_def fd 
    where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields';
-- DCFOCUS-68 continued
-- ADD Applications for new IS_ACTIVE_FACULTY field
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isActiveFaculty')
    and fa.token = 'DISPLAY';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isActiveFaculty')
    and fa.token = 'SEARCH_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isActiveFaculty')
    and fa.token = 'SEARCH_CHILD';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isActiveFaculty')
    and fa.token = 'REPORT_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isActiveFaculty')
    and fa.token = 'EMAIL';
-- DCFOCUS-68 continued
-- ADD DEFINITION FOR isAppointedStaff FIELD 
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Is Appointed Staff', 'extractedFields.isAppointedStaff', 0, 0, 10, fs.feedsource_id, fielddef_id, 1 from FEED_SOURCE fs, field_def fd 
    where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields';
-- DCFOCUS-68 continued
-- ADD Applications for new IS_APPOINTED_STAFF field
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isAppointedStaff')
    and fa.token = 'DISPLAY';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isAppointedStaff')
    and fa.token = 'SEARCH_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isAppointedStaff')
    and fa.token = 'SEARCH_CHILD';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isAppointedStaff')
    and fa.token = 'REPORT_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isAppointedStaff')
    and fa.token = 'EMAIL';
--
-- rename extractedFields which are pseudo-booleans
update field_def set path = 'extractedFields.isAdjunctClinical' where path = 'extractedFields.adjunctClinical';
update field_def set path = 'extractedFields.isAdjunctOnly' where path = 'extractedFields.adjunctOnly';
update field_def set path = 'extractedFields.isClinician' where path = 'extractedFields.clinician';
update field_def set path = 'extractedFields.isClta' where path = 'extractedFields.clta';
update field_def set path = 'extractedFields.isFlagged' where path = 'extractedFields.flagged';
update field_def set path = 'extractedFields.isFullTimeClinical' where path = 'extractedFields.fullTimeClinical';
update field_def set path = 'extractedFields.isInHrisFeed' where path = 'extractedFields.inHrisFeed';
update field_def set path = 'extractedFields.isPartTimeClinical' where path = 'extractedFields.partTimeClinical';
update field_def set path = 'extractedFields.isStatusOnly' where path = 'extractedFields.statusOnly';
update field_def set path = 'extractedFields.isTeachingStream' where path = 'extractedFields.teachingStream';
update field_def set path = 'extractedFields.isTenuredOrTenureStream' where path = 'extractedFields.tenuredOrTenureStream';
update field_def set path = 'extractedFields.isVisiting' where path = 'extractedFields.visiting';


