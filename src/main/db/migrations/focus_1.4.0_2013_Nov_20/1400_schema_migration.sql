--
alter table EXTRACTED_FIELDS add IS_FACULTY varchar(255) NULL;
--
-- Create new DEPARTMENT_FILTER table
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
SET ANSI_PADDING ON
GO
CREATE TABLE [dbo].[DEPARTMENT_FILTER](
   [DEPARTMENTFILTER_ID] numeric(19,0) IDENTITY NOT NULL,
   [VALUE_STRING] varchar(255),
   [PROPERTY_NAME] varchar(255),
   [DEPARTMENT_ID] numeric(19,0) NOT NULL,
PRIMARY KEY CLUSTERED 
(
    [DEPARTMENTFILTER_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
ALTER TABLE [dbo].[DEPARTMENT_FILTER] WITH CHECK
ADD CONSTRAINT [FK__DEPARTMENT_FILTER__DEPARTMENT]
FOREIGN KEY ([DEPARTMENT_ID])
REFERENCES [dbo].[DEPARTMENT]([DEPARTMENT_ID])
GO
CREATE INDEX IDX__DEPARTMENT_FILTER__DEPARTMENT_ID ON [dbo].[DEPARTMENT_FILTER]([DEPARTMENT_ID])
GO
CREATE UNIQUE INDEX PK__DEPARTMENTFILTER_ID ON [dbo].[DEPARTMENT_FILTER]([DEPARTMENTFILTER_ID])
GO
CREATE UNIQUE INDEX UQ__DEPARTMENT_FILTER__DEPARTMENT_ID ON [dbo].[DEPARTMENT_FILTER]([DEPARTMENT_ID])
GO
--
/****** Object:  Table [dbo].[FACULTY_WIDE_DATA] ******/
SET ANSI_NULLS ON
GO
--
SET QUOTED_IDENTIFIER ON
GO
--
SET ANSI_PADDING ON
GO
--
CREATE TABLE [dbo].[FACULTY_WIDE_DATA](
    [FACULTYWIDEDATA_ID] [numeric](19, 0) IDENTITY(1,1) NOT NULL,
    [EMAIL_ADDRESS] [varchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
    [FACULTYWIDEDATA_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO
--
SET ANSI_PADDING OFF
--
GO
--
-- ADD FOREIGN KEY TO PROFILE TABLE --
/******  Extracted Fields data for profile ******/
--
alter table [dbo].[PROFILE] add FACULTYWIDEDATA_ID numeric(19, 0)
GO
--
-- INSERT INITIAL FW DATA FOR EXISTING PROFILES
SET IDENTITY_INSERT FACULTY_WIDE_DATA ON
--
insert into FACULTY_WIDE_DATA(FACULTYWIDEDATA_ID) select PROFILE_ID from PROFILE
--
SET IDENTITY_INSERT FACULTY_WIDE_DATA OFF 
--
update PROFILE set FACULTYWIDEDATA_ID = PROFILE_ID
--
ALTER TABLE [dbo].[PROFILE]  WITH CHECK ADD  CONSTRAINT [FK__PROFILE_FACULTYWIDEDATA] FOREIGN KEY([FACULTYWIDEDATA_ID])
REFERENCES [dbo].[FACULTY_WIDE_DATA] ([FACULTYWIDEDATA_ID])
GO
--
ALTER TABLE [dbo].[PROFILE] CHECK CONSTRAINT [FK__PROFILE_FACULTYWIDEDATA]
GO
--
-- Add PRIMARY_DEPT_KEY field to Smart Fields. Allows reliable lookup of Department entity
ALTER TABLE [dbo].[EXTRACTED_FIELDS] add PRIMARY_DEPT_KEY varchar(255) NULL;
-- Add IS_CLINICAL_DEPARTMENT field to DEPARTMENT table
ALTER TABLE [dbo].[DEPARTMENT] add IS_CLINICAL_DEPARTMENT tinyint null;