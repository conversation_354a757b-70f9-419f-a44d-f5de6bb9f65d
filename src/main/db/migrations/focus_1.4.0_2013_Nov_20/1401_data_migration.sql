--------------------------------------------------------------
insert into AUTHORITY (AUTHORITY) values ('ROLE_VIEW_HRIS_PERSONNEL_NUMBER')
--
-- NEW Extracted Field IS_FACULTY
-- ADD DEFINITION FOR hrisChangeLog FIELD 
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is Faculty', 'extractedFields.isFaculty', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd 
    where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields';
-- ADD Applications for new IS_FACULTY field
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isFaculty')
    and fa.token = 'DISPLAY';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isFaculty')
    and fa.token = 'SEARCH_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isFaculty')
    and fa.token = 'SEARCH_CHILD';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isFaculty')
    and fa.token = 'REPORT_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isFaculty')
    and fa.token = 'EMAIL';
--
-- Break personnelNumber away from general HRIS Restricted code
update FIELD_DEF set RESTRICTION_CODE = 3 where PATH = 'personalData.personnelNumber';
-- edit label of smart field to match other booleans
update FIELD_DEF set LABEL='Is Clinician' where path = 'extractedFields.clinician';
--
-- DCFOCUS-49: ADD new FWU Filter data
insert into DEPARTMENT_FILTER (DEPARTMENT_ID, PROPERTY_NAME, VALUE_STRING) 
select dept.DEPARTMENT_ID, 'extractedFields.clinician', 'true' from DEPARTMENT dept where name = 'OIME'
--
insert into DEPARTMENT_FILTER (DEPARTMENT_ID, PROPERTY_NAME, VALUE_STRING) 
select dept.DEPARTMENT_ID, 'extractedFields.clinician', 'true' from DEPARTMENT dept where name = 'Clinical Affairs'
--
insert into DEPARTMENT_FILTER (DEPARTMENT_ID, PROPERTY_NAME, VALUE_STRING) 
select dept.DEPARTMENT_ID, 'extractedFields.isFaculty', 'true' from DEPARTMENT dept where name = 'FoM Research Office'

--DCFOCUS-58: Remove HRIS restricted fields from email templates. --
update FIELD_DEF set RESTRICTION_CODE = 1 where PATH = 'personalData.endDate'

delete FIELDDEFS_FDAPPLICATIONS from FIELDDEFS_FDAPPLICATIONS ff
	join FD_APPLICATION fdapp on fdapp.FDAPPLICATION_ID = ff.FDAPPLICATION_ID
	where ff.FIELDDEF_ID in (
		select fd.FIELDDEF_ID from FIELDDEFS_FDAPPLICATIONS ff  
		inner join FIELD_DEF fd on fd.FIELDDEF_ID = ff.FIELDDEF_ID
		inner join FD_APPLICATION fa on fa.FDAPPLICATION_ID = ff.FDAPPLICATION_ID
		where fa.TOKEN = 'EMAIL' and (fd.RESTRICTION_CODE = 1 or fd.RESTRICTION_CODE = 3)
	) and fdapp.TOKEN = 'EMAIL'
--
-- Set IS_CLINICAL_DEPARTMENT values
-- default is FALSE
update DEPARTMENT set IS_CLINICAL_DEPARTMENT=0;
-- set Clinical Departments to TRUE
update DEPARTMENT set IS_CLINICAL_DEPARTMENT=1 where DEPT_KEY in ('00000177','00000183','00000185','00000178','00000179',
'00000186','00000180','00000181','00000182','00000184','00000187');
-- Adding Faculty Wide Data --
insert into FEED_SOURCE (DESCRIPTION, SOURCE) values ('Faculty Wide Data','FACULTY_WIDE_DATA')
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, fielddatatype_id) 
    select 'Faculty Wide Data', 'facultyWideData', 0, 0, 0, fs.feedsource_id, null from FEED_SOURCE fs
    where fs.source = 'FACULTY_WIDE_DATA';
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Faculty Wide Email', 'facultyWideData.emailAddress', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt 
    where fs.source = 'FACULTY_WIDE_DATA' and fd.path = 'facultyWideData' and fdt.DATATYPE = 'Email';
    
-- Adding Field Applicaiton for Faculty Wide Data --
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('facultyWideData.emailAddress')
    and fa.token = 'SEARCH_PARENT';
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('facultyWideData.emailAddress')
    and fa.token = 'SEARCH_CHILD';
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('facultyWideData.emailAddress')
    and fa.token = 'REPORT_PARENT';
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('facultyWideData.emailAddress')
    and fa.token = 'EMAIL';