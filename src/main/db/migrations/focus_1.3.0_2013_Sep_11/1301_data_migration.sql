-- INITIAL DEFAULT VALUES FOR THE NEW INTERNAL_TYPE COLUMN
UPDATE DEPARTMENT set INTERNAL_TYPE=0 WHERE INTERNAL_TYPE is NULL;
-- Convert existing "FWU" to proper type
update DEPARTMENT set INTERNAL_TYPE=1 where NAME like '%Virtual All Departments%'
--
-- NEW Extracted Field IS_CUSTOM
-- ADD DEFINITION FOR hrisChangeLog FIELD 
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is Custom', 'extractedFields.isCustom', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd 
    where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields';
--
-- ADD Applications for new IS CUSTOM field
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isCustom')
    and fa.token = 'DISPLAY';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isCustom')
    and fa.token = 'SEARCH_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isCustom')
    and fa.token = 'SEARCH_CHILD';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isCustom')
    and fa.token = 'REPORT_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.isCustom')
    and fa.token = 'EMAIL';
--
