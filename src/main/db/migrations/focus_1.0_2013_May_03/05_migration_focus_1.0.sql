-- START EXTRACTED_FIELDS
-- add new columns
alter table EXTRACTED_FIELDS add ADJUNCT_CLINICAL varchar(255);
alter table EXTRACTED_FIELDS add ADJUNCT_ONLY varchar(255);
alter table EXTRACTED_FIELDS add FULL_TIME_CLINICAL varchar(255);
alter table EXTRACTED_FIELDS add PART_TIME_CLINICAL varchar(255);
alter table EXTRACTED_FIELDS add PRIM_APPOINTMENT_TYPE varchar(255);
alter table EXTRACTED_FIELDS add STATUS_ONLY varchar(255);
alter table EXTRACTED_FIELDS add VISITING varchar(255);
alter table EXTRACTED_FIELDS add APPOINTMENT_START_DATE datetime NULL;
alter table EXTRACTED_FIELDS add HOSPITAL_AFFILIATION_START_DATE datetime NULL;
-- create FieldDef entries for new ExtractedFields columns
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is Adjunct Clinical', 'extractedFields.adjunctClinical', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is Adjunct Only', 'extractedFields.adjunctOnly', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is Full-Time Clinical', 'extractedFields.fullTimeClinical', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is Part-Time Clinical', 'extractedFields.partTimeClinical', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Primary Appointment Type', 'extractedFields.primaryAppointmentType', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is Status Only', 'extractedFields.statusOnly', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is Visiting', 'extractedFields.visiting', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Appointment Start Date', 'extractedFields.appointmentStartDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Hospital Affiliation Start Date', 'extractedFields.hospitalAffiliationStartDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
-- END NEW EXTRACTED_FIELDS
-- BEGIN rename EXTRACTED FIELDS
update field_def set label = 'Clinical Appointment Type' where path = 'extractedFields.appointmentType';
-- END rename EXTRACTED FIELDS
-- END EXTRACTED_FIELDS
-- DISABLE extractedFields.appointmentPosition FieldDef
update field_def set selectable=0, non_searchable=1 where path='extractedFields.appointmentPosition'
-- Feed: keep count of non update profiles when importing
alter table FEED add NON_UPDATED numeric(19,0);
-- START REMOVE OLD CONSTRAINTS
/****** Drop Foreign keys ******/
ALTER TABLE [dbo].[PROFILE_GROUP] DROP CONSTRAINT [FK__PROFILE_GROUP__USERS]
GO
ALTER TABLE [dbo].[PROFILE_GROUP] DROP CONSTRAINT [FK__PROFILE_GROUPT__DEPARTMENT]
GO
ALTER TABLE [dbo].[PROFILE_GROUP_CRIT] DROP CONSTRAINT [FK67FD30306DF8B566]
GO
ALTER TABLE [dbo].[PROFILE_GROUP_CRIT] DROP CONSTRAINT [FK67FD3030784C2AC3]
GO
ALTER TABLE [dbo].[PROFILE_GROUP_CRIT] DROP CONSTRAINT [FK67FD303082E98DCF]
GO
ALTER TABLE [dbo].[PROFILEGROUP_PROFILE] DROP CONSTRAINT [FK__PROFILE__PROFILE_ID]
GO
ALTER TABLE [dbo].[PROFILEGROUP_PROFILE] DROP CONSTRAINT [FK__PROFILE_GROUP__PROFILEGROUP_ID]
GO
/****** Drop Indexes ******/
/****** Object:  Index [IDX__PROFILE_GROUP__DEPARTMENT_ID]    Script Date: 2013-03-21 3:22:17 PM ******/
DROP INDEX [IDX__PROFILE_GROUP__DEPARTMENT_ID] ON [dbo].[PROFILE_GROUP]
GO
/****** Object:  Index [IDX__PROFILE_GROUP__USERS]    Script Date: 2013-03-21 3:23:09 PM ******/
DROP INDEX [IDX__PROFILE_GROUP__USERS] ON [dbo].[PROFILE_GROUP]
GO
-- END REMOVE OLD CONSTRAINTS

/****** Email Template: Message queuing ******/
/****** Object:  Table [dbo].[JOB]    Script Date: 04/01/2013 11:27:57 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[JOB](
	[JOB_ID] [numeric](19, 0) IDENTITY(1,1) NOT NULL,
	[CREATEDBY] [varchar](255) NULL,
	[CREATEDON] [datetime] NULL,
	[DELETED] [tinyint] NULL,
	[OPTLOCK] [numeric](19, 0) NULL,
	[UPDATEDBY] [varchar](255) NULL,
	[UPDATEDON] [datetime] NULL,
	[DESCRIPTION] [varchar](255) NOT NULL,
	[PROFILES_INVOLVED] [numeric](19, 0) NULL,
	[STATE] [varchar](255) NULL,
	[END_TIME] [datetime] NULL,
	[START_TIME] [datetime] NULL,
	[USERS_ID] [numeric](19, 0) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[JOB_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[JOB]  WITH CHECK ADD  CONSTRAINT [FK_JOB__USERS] FOREIGN KEY([USERS_ID])
REFERENCES [dbo].[USERS] ([users_id])
GO

ALTER TABLE [dbo].[JOB] CHECK CONSTRAINT [FK_JOB__USERS]
GO


/****** Object:  Table [dbo].[JOB_ERROR]    Script Date: 04/01/2013 11:27:04 ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[JOB_ERROR](
	[JOBERROR_ID] [numeric](19, 0) IDENTITY(1,1) NOT NULL,
	[CREATEDBY] [varchar](255) NULL,
	[CREATEDON] [datetime] NULL,
	[DELETED] [tinyint] NULL,
	[OPTLOCK] [numeric](19, 0) NULL,
	[UPDATEDBY] [varchar](255) NULL,
	[UPDATEDON] [datetime] NULL,
	[DESCRIPTION] [varchar](255) NOT NULL,
	[FIRST_NAME] [varchar](255) NULL,
	[LAST_NAME] [varchar](255) NULL,
	[PROFILE_ID] [numeric](19, 0) NULL,
	[JOB_ID] [numeric](19, 0) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[JOBERROR_ID] ASC
)WITH (PAD_INDEX  = OFF, STATISTICS_NORECOMPUTE  = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS  = ON, ALLOW_PAGE_LOCKS  = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[JOB_ERROR]  WITH CHECK ADD  CONSTRAINT [FK__JOB__JOB_ERROR] FOREIGN KEY([JOB_ID])
REFERENCES [dbo].[JOB] ([JOB_ID])
GO

ALTER TABLE [dbo].[JOB_ERROR] CHECK CONSTRAINT [FK__JOB__JOB_ERROR]
GO

-- NEW TABLES FOR HISTORICAL DATA
create table ACTION_HISTORY (
    ACTIONHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    actionType numeric(19,0) null,
    actionTypeText varchar(255) null,
    changedOn datetime null,
    employmentStatus numeric(19,0) null,
    employmentStatusText varchar(255) null,
    endDate datetime null,
    reasonForAction numeric(19,0) null,
    reasonForActionText varchar(255) null,
    RECORD_NUMBER numeric(19,0) not null,
    startDate datetime null,
    PROFILE_ID numeric(19,0) not null,
    primary key (ACTIONHISTORY_ID)
);

create table ADDRESS_HISTORY (
    ADDRESSHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    addressType varchar(255) null,
    addressTypeText varchar(255) null,
    changedOn datetime null,
    city varchar(255) null,
    country varchar(255) null,
    endDate datetime null,
    postalCode varchar(255) null,
    province varchar(255) null,
    RECORD_NUMBER numeric(19,0) not null,
    secondAddressLine varchar(255) null,
    startDate datetime null,
    streetAndHouseNumber varchar(255) null,
    telephoneNumber varchar(255) null,
    CUSTOMFIELD_ID numeric(19,0) null,
    PROFILE_ID numeric(19,0) not null,
    primary key (ADDRESSHISTORY_ID)
);

create table APPOINTMENTDETAILS_HISTORY (
    APPOINTMENTDETAILSHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    changedOn datetime null,
    companyCode varchar(255) null,
    costCenter numeric(19,0) null,
    employeeGroup varchar(255) null,
    employeeGroupText varchar(255) null,
    employeeSubGroup varchar(255) null,
    employeeSubGroupText varchar(255) null,
    endDate datetime null,
    fund varchar(255) null,
    fundCenter numeric(19,0) null,
    job varchar(255) null,
    jobText varchar(255) null,
    APTDETAILS_ORDER varchar(255) null,
    orgUnit varchar(255) null,
    orgUnitText varchar(255) null,
    payrollArea varchar(255) null,
    payrollAreaText varchar(255) null,
    percentage varchar(255) null,
    persSubArea varchar(255) null,
    persSubAreaText varchar(255) null,
    personnelArea varchar(255) null,
    personnelAreaText varchar(255) null,
    position varchar(255) null,
    positionText varchar(255) null,
    RECORD_NUMBER numeric(19,0) not null,
    startDate datetime null,
    workContract varchar(255) null,
    workContractText varchar(255) null,
    PROFILE_ID numeric(19,0) not null,
    primary key (APPOINTMENTDETAILSHISTORY_ID)
);

create table AWARD_HISTORY (
    AWARDHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    awardHonour numeric(19,0) null,
    awardHonourName varchar(255) null,
    changedOn datetime null,
    endDate datetime null,
    RECORD_NUMBER numeric(19,0) not null,
    startDate datetime null,
    yearConferred numeric(19,0) null,
    EDUCATION_ID numeric(19,0) not null,
    primary key (AWARDHISTORY_ID)
);

create table BASICPAY_HISTORY (
    BASICPAYHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    changedOn datetime null,
    endDate datetime null,
    payScaleArea varchar(255) null,
    payScaleAreaText varchar(255) null,
    payScaleGroup varchar(255) null,
    payScaleLevel varchar(255) null,
    payScaleType varchar(255) null,
    payScaleTypeText varchar(255) null,
    RECORD_NUMBER numeric(19,0) not null,
    startDate datetime null,
    wageType varchar(255) null,
    wageTypeText varchar(255) null,
    PROFILE_ID numeric(19,0) not null,
    primary key (BASICPAYHISTORY_ID)
);

create table CAREERPROGRESS_HISTORY (
    CAREERPROGRESSHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    adjunctVisit varchar(255) null,
    adjunctVisitDescription varchar(255) null,
    changedOn datetime null,
    endDate datetime null,
    facultyRank varchar(255) null,
    facultyRankDate datetime null,
    facultyRankText varchar(255) null,
    RECORD_NUMBER numeric(19,0) not null,
    startDate datetime null,
    tenure varchar(255) null,
    tenureEffectiveDate datetime null,
    tenureReviewDate datetime null,
    tenureText varchar(255) null,
    EDUCATION_ID numeric(19,0) not null,
    primary key (CAREERPROGRESSHISTORY_ID)
);

create table CONTRACTELEMENT_HISTORY (
    CONTRACTELEMENTHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    changedOn datetime null,
    contractType varchar(255) null,
    contractTypeText varchar(255) null,
    endDate datetime null,
    RECORD_NUMBER numeric(19,0) not null,
    startDate datetime null,
    validUntil datetime null,
    PROFILE_ID numeric(19,0) not null,
    primary key (CONTRACTELEMENTHISTORY_ID)
);

create table EMAIL_HISTORY (
    EMAILHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    changedOn datetime null,
    emailAddress varchar(255) null,
    endDate datetime null,
    RECORD_NUMBER numeric(19,0) not null,
    startDate datetime null,
    CUSTOMFIELD_ID numeric(19,0) null,
    PROFILE_ID numeric(19,0) not null,
    primary key (EMAILHISTORY_ID)
);

create table GRADAPPT_HISTORY (
    GRADAPPTHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    changedOn datetime null,
    endDate datetime null,
    graduateUnit varchar(255) null,
    graduateUnitText varchar(255) null,
    primaryGradAppointIndicator varchar(255) null,
    RECORD_NUMBER numeric(19,0) not null,
    restrictionIndicator varchar(255) null,
    startDate datetime null,
    supervisoryLevel varchar(255) null,
    supervisoryLevelText varchar(255) null,
    EDUCATION_ID numeric(19,0) not null,
    primary key (GRADAPPTHISTORY_ID)
);


create table OTHERUNIVERSITYAPPOINTMENT_HISTORY (
    OTHERUNIVERSITYAPPOINTMENTHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    academicRank varchar(255) null,
    academicRankText varchar(255) null,
    appointmentType varchar(255) null,
    appointmentTypeText varchar(255) null,
    careerPathTitle varchar(255) null,
    changedOn datetime null,
    clinContractStatus varchar(255) null,
    clinContractStatusText varchar(255) null,
    cpsoNumber numeric(19,0) null,
    emeritusOrEmeritaRank varchar(255) null,
    endDate datetime null,
    hospitalAddress varchar(255) null,
    hospitalCode varchar(255) null,
    mainIndicator varchar(255) null,
    organizationUnit varchar(255) null,
    organizationUnitText varchar(255) null,
    RECORD_NUMBER numeric(19,0) not null,
    startDate datetime null,
    subtype varchar(255) null,
    subtypeText varchar(255) null,
    PROFILE_ID numeric(19,0) not null,
    primary key (OTHERUNIVERSITYAPPOINTMENTHISTORY_ID)
);

create table POSTSECED_HISTORY (
    POSTSECEDHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    changedOn datetime null,
    educationCode numeric(19,0) null,
    educationCodeText varchar(255) null,
    endDate datetime null,
    instution varchar(255) null,
    instutionText varchar(255) null,
    RECORD_NUMBER numeric(19,0) not null,
    sourceDocument varchar(255) null,
    sourceDocumentText varchar(255) null,
    startDate datetime null,
    verified varchar(255) null,
    yearConfirmed numeric(19,0) null,
    EDUCATION_ID numeric(19,0) not null,
    primary key (POSTSECEDHISTORY_ID)
);

create table PROFDESIGNATION_HISTORY (
    PROFDESIGNATIONHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    changedOn datetime null,
    designationCode varchar(255) null,
    designationCodeText varchar(255) null,
    endDate datetime null,
    RECORD_NUMBER numeric(19,0) not null,
    startDate datetime null,
    yearConferred numeric(19,0) null,
    EDUCATION_ID numeric(19,0) not null,
    primary key (PROFDESIGNATIONHISTORY_ID)
);

create table TELEPHONE_HISTORY (
    TELEPHONEHISTORY_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    areaCode varchar(255) null,
    changedOn datetime null,
    endDate datetime null,
    extension varchar(255) null,
    longDistance tinyint null,
    phoneNumber varchar(255) null,
    phoneType varchar(255) null,
    startDate datetime null,
    telPrimary tinyint null,
    telecomOrg varchar(255) null,
    CUSTOMFIELD_ID numeric(19,0) null,
    PROFILE_ID numeric(19,0) not null,
    primary key (TELEPHONEHISTORY_ID)
);

create index IDX__ACTION_HISTORY__PROFILE_ID on ACTION_HISTORY (PROFILE_ID);

alter table ACTION_HISTORY
    add constraint FK__ACTION_HISTORY__PROFILE
    foreign key (PROFILE_ID)
    references PROFILE;


create index IDX__ADDRESS_HISTORY__PROFILE_ID on ADDRESS_HISTORY (PROFILE_ID);

alter table ADDRESS_HISTORY
    add constraint FK__ADDRESS_HISTORY__PROFILE
    foreign key (PROFILE_ID)
    references PROFILE;

create index IDX__APPOINTMENTDETAILS_HISTORY__PROFILE_ID on APPOINTMENTDETAILS_HISTORY (PROFILE_ID);

alter table APPOINTMENTDETAILS_HISTORY
    add constraint FK__APPOINTMENTDETAILS_HISTORY__PROFILE
    foreign key (PROFILE_ID)
    references PROFILE;


create index IDX__AWARD_HISTORY__EDUCATION_ID on AWARD_HISTORY (EDUCATION_ID);

alter table AWARD_HISTORY
    add constraint FK__AWARD_HISTORY__EDUCATION
    foreign key (EDUCATION_ID)
    references EDUCATION;


create index IDX__BASICPAY_HISTORY__PROFILE_ID on BASICPAY_HISTORY (PROFILE_ID);

alter table BASICPAY_HISTORY
    add constraint FK__BASICPAY_HISTORY__PROFILE
    foreign key (PROFILE_ID)
    references PROFILE;

create index IDX__CAREERPROGRESS_HISTORY__EDUCATION_ID on CAREERPROGRESS_HISTORY (EDUCATION_ID);

alter table CAREERPROGRESS_HISTORY
    add constraint FK__CAREERPROGRESS_HISTORY__EDUCATION
    foreign key (EDUCATION_ID)
    references EDUCATION;


create index IDX__CONTRACTELEMENT_HISTORY__PROFILE_ID on CONTRACTELEMENT_HISTORY (PROFILE_ID);

alter table CONTRACTELEMENT_HISTORY
    add constraint FK__CONTRACTELEMENT_HISTORY__PROFILE
    foreign key (PROFILE_ID)
    references PROFILE;


create index IDX__EMAIL_HISTORY__PROFILE_ID on EMAIL_HISTORY (PROFILE_ID);

alter table EMAIL_HISTORY
    add constraint FK__EMAIL_HISTORY__PROFILE
    foreign key (PROFILE_ID)
    references PROFILE;



create index IDX__GRADAPPT_HISTORY__EDUCATION_ID on GRADAPPT_HISTORY (EDUCATION_ID);

alter table GRADAPPT_HISTORY
    add constraint FK__GRADAPPT_HISTORY__EDUCATION
    foreign key (EDUCATION_ID)
    references EDUCATION;



create index IDX__OTHERUNIVERSITYAPPOINTMENT_HISTORY__PROFILE_ID on OTHERUNIVERSITYAPPOINTMENT_HISTORY (PROFILE_ID);

alter table OTHERUNIVERSITYAPPOINTMENT_HISTORY 
    add constraint FK__OTHERUNIVERSITYAPPOINTMENT_HISTORY__PROFILE 
    foreign key (PROFILE_ID) 
    references PROFILE;


create index IDX__POSTSECED_HISTORY__EDUCATION_ID on POSTSECED_HISTORY (EDUCATION_ID);

alter table POSTSECED_HISTORY
    add constraint FK__POSTSECED_HISTORY__EDUCATION
    foreign key (EDUCATION_ID)
    references EDUCATION;

create index IDX__PROFDESIGNATION_HISTORY__EDUCATION_ID on PROFDESIGNATION_HISTORY (EDUCATION_ID);

alter table PROFDESIGNATION_HISTORY
    add constraint FK__PROFDESIGNATION_HISTORY__EDUCATION
    foreign key (EDUCATION_ID)
    references EDUCATION;

create index IDX__TELEPHONE_HISTORY__PROFILE_ID on TELEPHONE_HISTORY (PROFILE_ID);

alter table TELEPHONE_HISTORY
    add constraint FK__TELEPHONE_HISTORY__PROFILE
    foreign key (PROFILE_ID)
    references PROFILE;

/*
 * OTHERUNIVERSITYAPPOINTMENT_HISTORY
 */
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) values ('Other University Appointment History', 'otherUnivApptsHists.otherUnivApptsHist', 0, 0, 1, 1, null, 1, 0)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Start Date', 'otherUnivApptsHists.otherUnivApptsHist.startDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'End Date', 'otherUnivApptsHists.otherUnivApptsHist.endDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Sub Type', 'otherUnivApptsHists.otherUnivApptsHist.subtype', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Sub Type Text', 'otherUnivApptsHists.otherUnivApptsHist.subtypeText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Appointment Type', 'otherUnivApptsHists.otherUnivApptsHist.appointmentType', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Appointment Type Text', 'otherUnivApptsHists.otherUnivApptsHist.appointmentTypeText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Organization Unit', 'otherUnivApptsHists.otherUnivApptsHist.organizationUnit', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Organization UnitText', 'otherUnivApptsHists.otherUnivApptsHist.organizationUnitText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Emeritus Or Emerita Rank', 'otherUnivApptsHists.otherUnivApptsHist.emeritusOrEmeritaRank', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Main Indicator', 'otherUnivApptsHists.otherUnivApptsHist.mainIndicator', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Academic Rank', 'otherUnivApptsHists.otherUnivApptsHist.academicRank', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Academic Rank Text', 'otherUnivApptsHists.otherUnivApptsHist.academicRankText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Career Path Title', 'otherUnivApptsHists.otherUnivApptsHist.careerPathTitle', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Hospital Code', 'otherUnivApptsHists.otherUnivApptsHist.hospitalCode', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Clinical Contract Status', 'otherUnivApptsHists.otherUnivApptsHist.clinContractStatus', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Clinical Contract Status Text', 'otherUnivApptsHists.otherUnivApptsHist.clinContractStatusText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'CPSO Number', 'otherUnivApptsHists.otherUnivApptsHist.cpsoNumber', 0, 0, 1, 1, fielddef_id, 0, 0, 3 from field_def where path = 'otherUnivApptsHists.otherUnivApptsHist'
 
/*
 * APPOINTMENTDETAILS_HISTORY
 */
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) values ('Appointment Details History', 'appointmentDetailsHists.appointmentDetailsHist', 0, 0, 1, 1, null, 1, 0)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Start Date', 'appointmentDetailsHists.appointmentDetailsHist.startDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'End Date', 'appointmentDetailsHists.appointmentDetailsHist.endDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Personnel Area', 'appointmentDetailsHists.appointmentDetailsHist.personnelArea', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Personnel Area Text', 'appointmentDetailsHists.appointmentDetailsHist.personnelAreaText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Personnel Subarea', 'appointmentDetailsHists.appointmentDetailsHist.persSubArea', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Personnel Subarea Text', 'appointmentDetailsHists.appointmentDetailsHist.persSubAreaText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Org Unit', 'appointmentDetailsHists.appointmentDetailsHist.orgUnit', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Org Unit Text', 'appointmentDetailsHists.appointmentDetailsHist.orgUnitText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Percentage', 'appointmentDetailsHists.appointmentDetailsHist.percentage', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Job', 'appointmentDetailsHists.appointmentDetailsHist.job', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Job Text', 'appointmentDetailsHists.appointmentDetailsHist.jobText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Position', 'appointmentDetailsHists.appointmentDetailsHist.position', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Position Text', 'appointmentDetailsHists.appointmentDetailsHist.positionText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Employee Group', 'appointmentDetailsHists.appointmentDetailsHist.employeeGroup', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Employee Group Text', 'appointmentDetailsHists.appointmentDetailsHist.employeeGroupText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Employee Subgroup', 'appointmentDetailsHists.appointmentDetailsHist.employeeSubGroup', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Employee Subgroup Text', 'appointmentDetailsHists.appointmentDetailsHist.employeeSubGroupText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Payroll Area', 'appointmentDetailsHists.appointmentDetailsHist.payrollArea', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Payroll Area Text', 'appointmentDetailsHists.appointmentDetailsHist.payrollAreaText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Work Contract', 'appointmentDetailsHists.appointmentDetailsHist.workContract', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Work Contract Text', 'appointmentDetailsHists.appointmentDetailsHist.workContractText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Cost Center', 'appointmentDetailsHists.appointmentDetailsHist.costCenter', 0, 0, 1, 1, fielddef_id, 0, 0, 3 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Fund Center', 'appointmentDetailsHists.appointmentDetailsHist.fundCenter', 0, 0, 1, 1, fielddef_id, 0, 0, 3 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Order', 'appointmentDetailsHists.appointmentDetailsHist.order', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Fund', 'appointmentDetailsHists.appointmentDetailsHist.fund', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetailsHists.appointmentDetailsHist'

/*
 * 
 */
alter table APPOINTMENTDETAILS alter column percentage float;
alter table APPOINTMENTDETAILS_HISTORY alter column percentage float;

/*
 * 
 */
insert into FIELD_DATA_TYPE (DATATYPE, DESCRIPTION) values ('Real', 'Real (decimal) Number');

/*
 * 
 */
update field_def
    set fielddatatype_id = fdt.fielddatatype_id 
from 
	FIELD_DATA_TYPE fdt
where 
	path = 'appointmentDetails.appointmentDetail.percentage'
and 
	fdt.DATATYPE = 'Real';
	
update field_def
    set fielddatatype_id = fdt.fielddatatype_id 
from 
	FIELD_DATA_TYPE fdt
where 
	path = 'appointmentDetailsHists.appointmentDetailsHist.percentage'
and 
	fdt.DATATYPE = 'Real';	

/*
 * 
 */
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) 
	select pgo.PROFILEGROUPCRITOP_ID, fdt.FIELDDATATYPE_ID 
		from PROFILE_GROUP_CRIT_OP pgo, FIELD_DATA_TYPE fdt 
		where fdt.DATATYPE = 'Real'
		and pgo.RESTRICTION_OP_NAME = 'eq';
		
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) 
	select pgo.PROFILEGROUPCRITOP_ID, fdt.FIELDDATATYPE_ID 
		from PROFILE_GROUP_CRIT_OP pgo, FIELD_DATA_TYPE fdt 
		where fdt.DATATYPE = 'Real'
		and pgo.RESTRICTION_OP_NAME = 'ne';
		
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) 
	select pgo.PROFILEGROUPCRITOP_ID, fdt.FIELDDATATYPE_ID 
		from PROFILE_GROUP_CRIT_OP pgo, FIELD_DATA_TYPE fdt 
		where fdt.DATATYPE = 'Real'
		and pgo.RESTRICTION_OP_NAME = 'gt';
		
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) 
	select pgo.PROFILEGROUPCRITOP_ID, fdt.FIELDDATATYPE_ID 
		from PROFILE_GROUP_CRIT_OP pgo, FIELD_DATA_TYPE fdt 
		where fdt.DATATYPE = 'Real'
		and pgo.RESTRICTION_OP_NAME = 'lt';
		
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) 
	select pgo.PROFILEGROUPCRITOP_ID, fdt.FIELDDATATYPE_ID 
		from PROFILE_GROUP_CRIT_OP pgo, FIELD_DATA_TYPE fdt 
		where fdt.DATATYPE = 'Real'
		and pgo.RESTRICTION_OP_NAME = 'ge';
		
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) 
	select pgo.PROFILEGROUPCRITOP_ID, fdt.FIELDDATATYPE_ID 
		from PROFILE_GROUP_CRIT_OP pgo, FIELD_DATA_TYPE fdt 
		where fdt.DATATYPE = 'Real'
		and pgo.RESTRICTION_OP_NAME = 'le';
		
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) 
	select pgo.PROFILEGROUPCRITOP_ID, fdt.FIELDDATATYPE_ID 
		from PROFILE_GROUP_CRIT_OP pgo, FIELD_DATA_TYPE fdt 
		where fdt.DATATYPE = 'Real'
		and pgo.RESTRICTION_OP_NAME = 'isNull';
		
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) 
	select pgo.PROFILEGROUPCRITOP_ID, fdt.FIELDDATATYPE_ID 
		from PROFILE_GROUP_CRIT_OP pgo, FIELD_DATA_TYPE fdt 
		where fdt.DATATYPE = 'Real'
		and pgo.RESTRICTION_OP_NAME = 'isNotNull';
		
/*
 *
 */
alter table NOTE add SENSITIVE TINYINT;		

/*
**
** Indexes
**
*/
create index IDX_ADDRESS_HISTORY__PROFILE_ID on ADDRESS_HISTORY (PROFILE_ID)
create index IDX__COMMPREF__EMAIL_ID on COMMPREF (EMAIL_ID)
create index IDX__COMMPREF__ADDRESS_ID on COMMPREF (ADDRESS_ID)
create index IDX__COMMPREF__TELEPHONE_ID on COMMPREF (TELEPHONE_ID)
create index IDX__DYNAMIC_GROUP__DEPARTMENT_ID on DYNAMIC_GROUP (DEPARTMENT_ID)
create index IDX__DYNAMIC_GROUP__USERS on DYNAMIC_GROUP (AUTHUSER_ID)
create index IDX__DYNAMICGROUPCRITERION__DYNAMICGROUP_ID on DYNAMIC_GROUP_CRIT (DYNAMICGROUP_ID)
create index IDX__JOB__USERS_ID on JOB (USERS_ID)
create index IDX__PROFILE_ID_EXTRACTEDFIELDS on PROFILE (EXTRACTEDFIELDS_ID)
create index IDX__STATIC_GROUP__DEPARTMENT_ID on STATIC_GROUP (DEPARTMENT_ID)
create index IDX__STATIC_GROUP__USERS on STATIC_GROUP (AUTHUSER_ID)
