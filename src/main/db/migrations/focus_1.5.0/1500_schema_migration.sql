-- Add IS_MEDICAL_DEPARTMENT field to DEPARTMENT table
ALTER TABLE [dbo].[DEPARTMENT] add IS_MEDICAL_DEPARTMENT tinyint null;
-- Previous Faculty Rank End Date
alter table EXTRACTED_FIELDS add PREVIOUS_FACULTY_RANK_END_DATE date null;
--
CREATE TABLE "dbo"."CUSTOM_APPOINTMENT"
(
   CUSTOMAPPOINTMENT_ID numeric(19,0) identity NOT NULL,
   CREATEDBY varchar(255),
   CREATEDON datetime,
   DELETED tinyint,
   OPTLOCK numeric(19,0),
   UPDATEDBY varchar(255),
   UPDATEDON datetime,
   endDate datetime,
   IS_HEAD tinyint DEFAULT 0,
   IS_PRIMARY tinyint DEFAULT 0,
   startDate datetime,
   DEPARTMENT_ID numeric(19,0) NOT NULL,
   PROFILE_ID numeric(19,0) NOT NULL,
   primary key (CUSTOMAPPOINTMENT_ID)
)
GO
ALTER TABLE "dbo"."CUSTOM_APPOINTMENT"
ADD CONSTRAINT FK__CUSTOMAPPOINTMENT__PROFILE
FOREIGN KEY (PROFILE_ID)
REFERENCES "dbo"."PROFILE"(PROFILE_ID)
GO
ALTER TABLE "dbo"."CUSTOM_APPOINTMENT"
ADD CONSTRAINT FK__CUSTOMAPPOINTMENT__DEPARTMENT
FOREIGN KEY (DEPARTMENT_ID)
REFERENCES "dbo"."DEPARTMENT"(DEPARTMENT_ID)
GO
CREATE INDEX IDX__CUSTOMAPPOINTMENT__DEPARTMENT_ID ON "dbo"."CUSTOM_APPOINTMENT"(DEPARTMENT_ID)
GO
CREATE UNIQUE INDEX PK__CUSTOM_APPPOINTMENT_ID ON "dbo"."CUSTOM_APPOINTMENT"(CUSTOMAPPOINTMENT_ID)
GO
CREATE INDEX IDX__CUSTOMAPPOINTMENT__PROFILE_ID ON "dbo"."CUSTOM_APPOINTMENT"(PROFILE_ID)
GO
