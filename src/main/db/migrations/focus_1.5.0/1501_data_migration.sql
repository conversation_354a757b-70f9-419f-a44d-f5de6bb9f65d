-- Set IS_MEDICAL_DEPARTMENT values
-- default is FALSE
update DEPARTMENT set IS_MEDICAL_DEPARTMENT=0;
-- set Medical Departments to TRUE
update department set IS_MEDICAL_DEPARTMENT=1 where name in(
'Vice-Provost Relations Health Care Inst.',
'Mclaughlin Centre for Molecular Medicine',
'Med: Ofc Of The Dean',
'Vice-Dean Research and Int. Relations',
'Inst of Biomat. & Biomedical Engineering',
'Div of Comparative Med.',
'Wilson Centre For Research In Education',
'Discovery Commons',
'Clinical Sc Div',
'Toronto Western Research Institute',
'Div of Teaching Labs',
'Bant & Best Dept Med Res',
'Dept Of Biochemistry',
'Dept Of Medical Biophysics',
'Dept of Molecular Genetics',
'History of Medicine Program',
'Dept of Nutritional Sciences',
'Dept Of Pharmacology & Vision Sciences',
'Dept Of Physiology',
'Department of Anaesthesia',
'Div of Biomedical Communications',
'Dept of Family & Community Medicine',
'Dept of Medicine',
'Dept of Obstetrics&Gynaecology',
'Dept Of Ophthalmology & Vision Sciences',
'Dept Of Otolaryngology - Head & Neck Sur',
'Dept Of Paediatrics',
'Dept Of Lab. Medicine & Pathobiology',
'Dept Of Psychiatry',
'Dept of Medical Imaging',
'Dept of Occupational Science & Therapy',
'Dept of Surgery',
'Dept of Health Policy, Mgmt & Evaluation',
'Dept Of Speech-Language Pathology',
'Continuing Education, Medicine',
'Admissions & Stud.Affairs',
'Postgraduate Medical Education',
'Microscopy Imaging Laboratory',
'Dept of Immunology',
'Inst Of Medical Science',
'Joint Centre For Bioethics',
'Banting & Best Diabetes Ctr',
'Ofc Of The Dean - Faculty',
'Undergraduate Medical Education',
'Tanz Ctr Res. Neurodegenerative Diseases',
'IMG-Ontario',
'Dept Of Radiation Oncology',
'Cardiovascular Sci. Collaborative Prog',
'Dept Of Physical Therapy',
'Dept Of Rehabilitation Science(Graduate)',
'Medstore',
'Institute For Life Course & Aging',
'Ofc of the Vice-Dean Education',
'MD/PhD Program',
'Division Of Anatomy',
'Ofc Of Vice Dean, Graduate  Affairs',
'CERIS - The Ontario Metropolis Centre',
'Division of Educational Computing',
'Education Deans',
'Rehabilitation Sciences Sector',
'Donnelly Centre',
'Heart & Stroke/R.Lewar Ctr of Excellence',
'Structural Genomics Consortium',
'Level 3 Facility',
'Mclaughlin-Rotman Ctr. For Global Health',
'Advancement Medicine',
'Standardized Patient Program',
'UME: Mississauga Academy of Medicine',
'MED: Strategy Communications External Rel',
'Education Innovations Office');
--
-- New FEED SOURCE
insert into FEED_SOURCE (DESCRIPTION, SOURCE) values ('Custom Appointments', 'CUSTOM_APPOINTMENT');
-- FIELD DEF rows to support Custom Appointment
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, fielddatatype_id) 
    select 'Custom Appointment', 'customAppointments.customAppointment', 0, 0, 0, fs.feedsource_id, null from FEED_SOURCE fs
    where fs.source = 'CUSTOM_APPOINTMENT';
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Start Date', 'customAppointments.customAppointment.startDate', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID 
    from FEED_SOURCE fs, FIELD_DEF fd, FIELD_DATA_TYPE fdt 
    where fs.source = 'CUSTOM_APPOINTMENT' and fd.path = 'customAppointments.customAppointment' and fdt.DATATYPE = 'Date';
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'End Date', 'customAppointments.customAppointment.endDate', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID 
    from FEED_SOURCE fs, FIELD_DEF fd, FIELD_DATA_TYPE fdt 
    where fs.source = 'CUSTOM_APPOINTMENT' and fd.path = 'customAppointments.customAppointment' and fdt.DATATYPE = 'Date';
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Research Institute', 'customAppointments.customAppointment.department.name', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID 
    from FEED_SOURCE fs, FIELD_DEF fd, FIELD_DATA_TYPE fdt 
    where fs.source = 'CUSTOM_APPOINTMENT' and fd.path = 'customAppointments.customAppointment' and fdt.DATATYPE = 'Text';
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Research Inst. Code', 'customAppointments.customAppointment.department.key', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID 
    from FEED_SOURCE fs, FIELD_DEF fd, FIELD_DATA_TYPE fdt 
    where fs.source = 'CUSTOM_APPOINTMENT' and fd.path = 'customAppointments.customAppointment' and fdt.DATATYPE = 'Text';
--
-- FIELD DEF APPLICATIONS
--insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
--    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
--    where fd.path in ('customAppointments.customAppointment')
--    and fa.token = 'DISPLAY';
--insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
--    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
--    where fd.path in ('customAppointments.customAppointment')
--    and fa.token = 'SEARCH_PARENT';
--insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
--    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
--    where fd.path in (
--        'customAppointments.customAppointment.startDate', 
--        'customAppointments.customAppointment.endDate',
--        'customAppointments.customAppointment.department.name',
--        'customAppointments.customAppointment.department.key')
--    and fa.token = 'SEARCH_CHILD';
--insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
--    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
--    where fd.path in ('customAppointments.customAppointment')
--    and fa.token = 'REPORT_PARENT';

------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Previous Rank End Date', 'extractedFields.previousFREndDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Date'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFREndDate')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFREndDate')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFREndDate')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFREndDate')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFREndDate')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------