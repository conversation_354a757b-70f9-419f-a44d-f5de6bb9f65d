-- FIELDDEF APPLICATION TYPES
insert into FD_APPLICATION (token, description) values ('DISPLAY', 'May add to profile detail display panel');
insert into FD_APPLICATION (token, description) values ('SEARCH_PARENT', 'Is selectable at the first level of advanced search');
insert into FD_APPLICATION (token, description) values ('SEARCH_CHILD', 'Is selectable at the second level of advanced search');
insert into FD_APPLICATION (token, description) values ('REPORT_PARENT', 'May be added to report template');
insert into FD_APPLICATION (token, description) values ('EMAIL', 'May be added to an email template');
-- BEGIN: Create FIELDDEFS_FDAPPLICATIONS link table entries for existing FIELD_DEF entries
-- DISPLAY fields show up in Fields selection list when adding fields to display
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
	select fd.FIELDDEF_ID, a.FDAPPLICATION_ID
	FROM FIELD_DEF fd, FD_APPLICATION a
	where fd.selectable = 1 
	and not fd.path like 'dynamicGroups%'
	and not fd.path like 'staticGroups%'
	and (fd.fielddatatype_id is null or not fd.FIELDDATATYPE_ID in (
        select fielddatatype_id from FIELD_DATA_TYPE where DATATYPE in ('Address', 'Email', 'Phone')
    ))
	and a.token = 'DISPLAY';
-- SEARCH first level
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
	select fd.FIELDDEF_ID, a.FDAPPLICATION_ID
	FROM FIELD_DEF fd, FD_APPLICATION a
	where fd.selectable = 1 
	and not fd.path = 'notes.notes'
    and (fd.fielddatatype_id is null or not fd.FIELDDATATYPE_ID in (
        select fielddatatype_id from FIELD_DATA_TYPE where DATATYPE in ('Text Box')
    ))
	and a.token = 'SEARCH_PARENT';
-- SEARCH second level
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID
    FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.non_searchable=0 
    and fd.PARENTFIELDDEF_ID is not null
    and a.token = 'SEARCH_CHILD';
-- SEARCH second level for simple custom fields
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
	select fd.FIELDDEF_ID, a.FDAPPLICATION_ID
	FROM FIELD_DEF fd, FD_APPLICATION a
	where fd.selectable = 1
	and fd.FEEDSOURCE_ID = (
	    select feedsource_id from FEED_SOURCE WHERE SOURCE = 'CUSTOM FIELD'
	)
	and fd.FIELDDATATYPE_ID not in (
	    select fielddatatype_id 
	    from FIELD_DATA_TYPE 
	    where DATATYPE in ('Address', 'Email', 'Telephone')
	)
	and a.token = 'SEARCH_CHILD';
-- REPORT first level (second level not implemented yet)
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
	select fd.FIELDDEF_ID, a.FDAPPLICATION_ID
	FROM FIELD_DEF fd, FD_APPLICATION a
	where fd.selectable = 1 and fd.FEEDSOURCE_ID not in (
		select feedsource_id from FEED_SOURCE WHERE SOURCE = 'HRIS PSEUDO' OR SOURCE = 'INTERNAL'
	)
	and a.token = 'REPORT_PARENT';
-- EMAIL template HRIS field defs
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
	select fd.FIELDDEF_ID, a.FDAPPLICATION_ID
	FROM FIELD_DEF fd, FD_APPLICATION a
	where fd.selectable = 1
	and (fd.path like '%personalData%'
    or fd.path like 'extractedFields%'
    or fd.path like 'primary%')
	and a.token = 'EMAIL'
	order by path;
-- EMAIL migrate existing custom field defs
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
	select fd.FIELDDEF_ID, a.FDAPPLICATION_ID
	FROM FIELD_DEF fd, FD_APPLICATION a
	where fd.selectable = 1
	and (fd.FEEDSOURCE_ID = (
			select feedsource_id from FEED_SOURCE WHERE SOURCE = 'CUSTOM FIELD'
		)
		and fd.PARENTFIELDDEF_ID is null
	)
	and a.token = 'EMAIL';
-- END: Create FIELDDEFS_FDAPPLICATIONS link table entries for existing FIELD_DEF entries
--
-- BEGIN: INSERT FIELD_DEF ROWS FOR HRIS_RULE_EVENT columns
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id) 
    select 'HRIS Rule Events', 'hrisRuleEventList.hrisRuleEvent', 0, 0, 0, fs.feedsource_id, null from FEED_SOURCE fs
    where fs.source = 'HRIS'
-- HRIS Rule Event: Rule
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Rule', 'hrisRuleEventList.hrisRuleEvent.rule', 0, 0, 0, fs.feedsource_id, parent.fielddef_id, fdt.fielddatatype_id from FEED_SOURCE fs, FIELD_DEF parent, FIELD_DATA_TYPE fdt 
    where fs.source = 'HRIS' and parent.path = 'hrisRuleEventList.hrisRuleEvent' and fdt.DATATYPE = 'Text';
-- HRIS Rule Event: Appointment Type
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Appointment Type', 'hrisRuleEventList.hrisRuleEvent.appointmentType', 0, 0, 0, fs.feedsource_id, parent.fielddef_id, fdt.fielddatatype_id from FEED_SOURCE fs, FIELD_DEF parent, FIELD_DATA_TYPE fdt 
    where fs.source = 'HRIS' and parent.path = 'hrisRuleEventList.hrisRuleEvent' and fdt.DATATYPE = 'Text';
-- HRIS Rule Event: Flagged Value
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Flagged Value', 'hrisRuleEventList.hrisRuleEvent.flaggedValue', 0, 0, 0, fs.feedsource_id, parent.fielddef_id, fdt.fielddatatype_id from FEED_SOURCE fs, FIELD_DEF parent, FIELD_DATA_TYPE fdt 
    where fs.source = 'HRIS' and parent.path = 'hrisRuleEventList.hrisRuleEvent' and fdt.DATATYPE = 'Text';
-- HRIS Rule Event: Reason
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Reason', 'hrisRuleEventList.hrisRuleEvent.reason', 0, 0, 0, fs.feedsource_id, parent.fielddef_id, fdt.fielddatatype_id from FEED_SOURCE fs, FIELD_DEF parent, FIELD_DATA_TYPE fdt 
    where fs.source = 'HRIS' and parent.path = 'hrisRuleEventList.hrisRuleEvent' and fdt.DATATYPE = 'Text';
-- END: INSERT FIELD_DEF ROWS FOR HRIS_RULE_EVENT columns
-- BEGIN: MAKE HRIS RULE EVENTS availble for Reports and Profile Display
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'hrisRuleEventList.hrisRuleEvent' and a.token = 'DISPLAY';
--
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'hrisRuleEventList.hrisRuleEvent' and a.token = 'REPORT_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'hrisRuleEventList.hrisRuleEvent' and a.token = 'SEARCH_PARENT';
--
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path like 'hrisRuleEventList.hrisRuleEvent.%' and a.token = 'SEARCH_CHILD';
-- END: MAKE HRIS RULE EVENTS availble for Reports and Profile Display
--
-- BEGIN: INSERT FIELD_DEF ROWS FOR NEW v1.1 SMART FIELDS (EXTRACTED_FIELDS)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is Tenured/Tenure Stream', 'extractedFields.tenuredOrTenureStream', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd 
    where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields';
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is Teaching Stream', 'extractedFields.teachingStream', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd 
    where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields';
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is CLTA', 'extractedFields.clta', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd 
    where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields';
-- END: INSERT FIELD_DEF ROWS FOR NEW v1.1 SMART FIELDS (EXTRACTED_FIELDS)
--
-- BEGIN: ADD FIELDDEF APPLICATIONS ENTRIES FOR NEW EXTRACTED FIELDDEFS
-- MAKE AVAILABLE TO ADVANCED SEARCH AS TOP LEVEL (OWN PARENT)
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path in ('extractedFields.tenuredOrTenureStream', 'extractedFields.teachingStream', 'extractedFields.clta') 
    and a.token = 'SEARCH_PARENT';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path in ('extractedFields.tenuredOrTenureStream', 'extractedFields.teachingStream', 'extractedFields.clta') 
    and a.token = 'SEARCH_CHILD';
-- MAKE AVAILABLE TO PROFILE DISPLAY FIELD SELECTION
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path in ('extractedFields.tenuredOrTenureStream', 'extractedFields.teachingStream', 'extractedFields.clta') 
    and a.token = 'DISPLAY';
-- MAKE AVAILABLE TO REPORT FIELD SELECTION
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path in ('extractedFields.tenuredOrTenureStream', 'extractedFields.teachingStream', 'extractedFields.clta') 
    and a.token = 'REPORT_PARENT';
-- MAKE AVAILABLE TO EMAIL TEMPLATE FIELD SELECTION
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path in ('extractedFields.tenuredOrTenureStream', 'extractedFields.teachingStream', 'extractedFields.clta') 
    and a.token = 'EMAIL';
-- END: ADD FIELDDEF APPLICATIONS ENTRIES FOR NEW EXTRACTED FIELDDEFS
--
-- BEGIN: REMOVE OLD "Students and Fellows" APPOINTMENT_DETAILS RECORDS
delete from APPOINTMENTDETAILS where personnelArea = '0003';
delete from APPOINTMENTDETAILS_HISTORY where personnelArea = '0003';
-- END: REMOVE OLD "Students and Fellows" APPOINTMENT_DETAILS RECORDS
