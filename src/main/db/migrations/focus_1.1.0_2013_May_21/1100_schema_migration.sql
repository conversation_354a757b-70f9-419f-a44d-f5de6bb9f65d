-- CREATE TABLES
create table FIELDDEFS_FDAPPLICATIONS (
   FIELDDEF_ID numeric(19,0) not null,
   FDAPPLICATION_ID numeric(19,0) not null
);
create table FD_APPLICATION (
   FDAPPLICATION_ID numeric(19,0) identity not null,
   DESCRIPTION varchar(255) null,
   TOKEN varchar(255) not null,
   primary key (FDAPPLICATION_ID),
   unique (TOKEN)
);
-- END CREATE TABLES
-- CREATE TABLE CONSTRAINTS
alter table FIELDDEFS_FDAPPLICATIONS 
   add constraint FK__FIELDDEFS_FDAPPLICATIONS__FIELDDEF_ID
   foreign key (FIELDDEF_ID) 
   references FIELD_DEF;
alter table FIELDDEFS_FDAPPLICATIONS 
   add constraint FK__FIELDDEFS_FDAPPLICATIONS__FDAPPLICATION_ID
   foreign key (FDAPPLICATION_ID) 
   references FD_APPLICATION;
-- HRIS INCOMPLETE
-- Add column to EXTRACTED_FIELDS
alter table EXTRACTED_FIELDS add INCOMPLETE_RECORD varchar(255) null;
-- Create HRIS RULE EVENT table
create table HRIS_RULE_EVENT (
    HRISRULEEVENT_ID numeric(19,0) identity not null,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    APPOINTMENT_TYPE varchar(255) not null,
    FLAGGED_VALUE varchar(255) not null,
    REASON varchar(255) not null,
    RULE_NAME varchar(255) not null,
    PROFILE_ID numeric(19,0) not null,
    primary key (HRISRULEEVENT_ID)
);
-- PROFILE_ID INDEX
create index IDX_HRIS_RULE_EVENT_PROFILE on HRIS_RULE_EVENT (PROFILE_ID);
--
alter table HRIS_RULE_EVENT 
    add constraint FK_HRIS_RULE_EVENT_PROFILE 
    foreign key (PROFILE_ID) 
    references PROFILE;
-- END: PROFILE_ID INDEX
-- BEGIN: ADD NEW v1.1 SMART FIELDS (EXTRACTED_FIELDS)
alter table EXTRACTED_FIELDS add IS_CLTA varchar(255);
alter table EXTRACTED_FIELDS add IS_TEACHING_STREAM varchar(255);
alter table EXTRACTED_FIELDS add IS_TENURED_OR_TENURE_STREAM varchar(255);
-- END: ADD NEW v1.1 SMART FIELDS (EXTRACTED_FIELDS)
