-- SECURITY SEED DATA --
-- Groups
insert into groups (name) values ('DC System Administrators')
insert into groups (name) values ('Department Administrators')
insert into groups (name) values ('HR Administrators')
insert into groups (name) values ('Users')
-- Authorities
insert into authority (authority)  values ('ROLE_DC_SYSTEM_ADMINISTRATOR');
insert into authority (authority)  values ('ROLE_HR_ADMINISTRATOR');
insert into authority (authority)  values ('ROLE_CREATE_DEPARTMENT_ADMINISTRATOR');
insert into authority (authority)  values ('ROLE_DEPARTMENT_ADMINISTRATOR');
insert into authority (authority)  values ('ROLE_VIEW_HRIS_RESTRICTED');
insert into authority (authority)  values ('ROLE_VIEW_SENSITIVE_DATA');
insert into authority (authority)  values ('ROLE_VIEW_STAFF');
insert into authority (authority)  values ('ROLE_EDIT_DATA');
insert into authority (authority)  values ('ROLE_VIEW_FACULTY');
insert into authority (authority)  values ('ROLE_USER');
-- Group Authorities
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'DC System Administrators' and authority = 'ROLE_DC_SYSTEM_ADMINISTRATOR';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'DC System Administrators' and authority = 'ROLE_CREATE_DEPARTMENT_ADMINISTRATOR';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'DC System Administrators' and authority = 'ROLE_DEPARTMENT_ADMINISTRATOR';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'DC System Administrators' and authority = 'ROLE_VIEW_HRIS_RESTRICTED';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'DC System Administrators' and authority = 'ROLE_VIEW_SENSITIVE_DATA';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'DC System Administrators' and authority = 'ROLE_VIEW_STAFF';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'DC System Administrators' and authority = 'ROLE_EDIT_DATA';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'DC System Administrators' and authority = 'ROLE_VIEW_FACULTY';
-- Department Admins
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Department Administrators' and authority = 'ROLE_DEPARTMENT_ADMINISTRATOR';
--insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Department Administrators' and authority = 'ROLE_VIEW_HRIS_RESTRICTED';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Department Administrators' and authority = 'ROLE_VIEW_SENSITIVE_DATA';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Department Administrators' and authority = 'ROLE_VIEW_STAFF';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Department Administrators' and authority = 'ROLE_EDIT_DATA';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Department Administrators' and authority = 'ROLE_VIEW_FACULTY';
-- HR Admins
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'HR Administrators' and authority = 'ROLE_HR_ADMINISTRATOR';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'HR Administrators' and authority = 'ROLE_VIEW_FACULTY';
-- Users
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Users' and authority = 'ROLE_USER';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Users' and authority = 'ROLE_VIEW_FACULTY';
-- FIELD_DATA_TYPE
insert into FIELD_DATA_TYPE (DATATYPE, DESCRIPTION) values ('Text', 'String values, upto 255 characters (HTML INPUT BOX)'); -- 1
insert into FIELD_DATA_TYPE (DATATYPE, DESCRIPTION) values ('Text Box', 'String values, upto 7192 characters (HTML TEXTAREA)'); -- 2 
insert into FIELD_DATA_TYPE (DATATYPE, DESCRIPTION) values ('Number', 'Numeric field'); -- 3
insert into FIELD_DATA_TYPE (DATATYPE, DESCRIPTION) values ('Date', 'Date field'); -- 4
insert into FIELD_DATA_TYPE (DATATYPE, DESCRIPTION) values ('URL', 'URL'); -- 5
insert into FIELD_DATA_TYPE (DATATYPE, DESCRIPTION) values ('Email', 'Email'); -- 6
insert into FIELD_DATA_TYPE (DATATYPE, DESCRIPTION) values ('Phone', 'Phone'); -- 7
insert into FIELD_DATA_TYPE (DATATYPE, DESCRIPTION) values ('Address', 'Address'); -- 8
insert into FIELD_DATA_TYPE (DATATYPE, DESCRIPTION) values ('Dropdown List', 'A controlled list of values'); -- 9
insert into FIELD_DATA_TYPE (DATATYPE, DESCRIPTION) values ('Checkbox', 'A true or false value'); -- 10
-- FEED_SOURCE
insert into FEED_SOURCE (SOURCE, DESCRIPTION) VALUES ('HRIS', 'HRIS Feed');
insert into FEED_SOURCE (SOURCE, DESCRIPTION) VALUES ('CUSTOM FIELD', 'CUSTOM FIELD');
insert into FEED_SOURCE (SOURCE, DESCRIPTION) VALUES ('HRIS PSEUDO', 'HRIS PSEUDO');
insert into FEED_SOURCE (SOURCE, DESCRIPTION) VALUES ('HRIS EXTRACTED', 'A scalar field extracted from a composite, for eg. CPSO field extracted from OtherUniversityAppt)');
insert into FEED_SOURCE (SOURCE, DESCRIPTION) VALUES ('HRIS DERIVED', 'Derived Field');
insert into FEED_SOURCE (SOURCE, DESCRIPTION) VALUES ('INTERNAL', 'Internal fields such as Tags, Profile Groups');
insert into FEED_SOURCE (SOURCE, DESCRIPTION) VALUES ('PRIMARY_COMMUNICAITON_FIELD', 'Primay Address, Telephone & Email');
-- Feed Fields
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) select 'Personal Data', 'personalData', 0, 0, 1, fdt.fielddatatype_id, null, 0, 0 from FIELD_DATA_TYPE fdt where fdt.DATATYPE = 'Text'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Personnel Number', 'personalData.personnelNumber', 1, 0, 1, 1, fielddef_id, 1, 0, 3 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Start Date', 'personalData.startDate', 0, 0, 1, 1, fielddef_id, 1, 0, 4 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'End Date', 'personalData.endDate', 0, 0, 1, 1, fielddef_id, 1, 0, 4 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Form Of Address', 'personalData.formOfAddress', 0, 0, 1, 1, fielddef_id, 1, 0, 1 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Last Name', 'personalData.lastName', 0, 0, 1, 1, fielddef_id, 1, 0, 1 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'First Name', 'personalData.firstName', 0, 0, 1, 1, fielddef_id, 1, 0, 1 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Middle Name', 'personalData.middleName', 0, 0, 1, 1, fielddef_id, 1, 0, 1 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Designation', 'personalData.designation', 0, 0, 1, 1, fielddef_id, 1, 0, 1 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Full Name', 'personalData.fullName', 0, 0, 1, 1, fielddef_id, 1, 0, 1 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Birth Name', 'personalData.birthName', 0, 0, 1, 1, fielddef_id, 1, 0, 1 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Middle Initial', 'personalData.middleInitial', 0, 0, 1, 1, fielddef_id, 1, 0, 1 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Known As', 'personalData.knownAs', 0, 0, 1, 1, fielddef_id, 1, 0, 1 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Birth Date', 'personalData.birthDate', 1, 0, 1, 1, fielddef_id, 1, 0, 4 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Gender', 'personalData.gender', 0, 0, 1, 1, fielddef_id, 1, 0, 1 from field_def where path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Nationality', 'personalData.nationality', 1, 0, 1, 1, fielddef_id, 1, 0, 1 from field_def where path = 'personalData'
-- ADDRESS
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, fielddatatype_id, parentfielddef_id, selectable, non_searchable) select 'Address', 'addresses.address', 0, 0, 1, 1, fdt.fielddatatype_id, null, 1, 0 from FIELD_DATA_TYPE fdt where fdt.DATATYPE = 'Address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Address Type', 'addresses.address.addressType', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Address Type Text', 'addresses.address.addressTypeText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Street And House Number', 'addresses.address.streetAndHouseNumber', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Second Address Line', 'addresses.address.secondAddressLine', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'City', 'addresses.address.city', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Province', 'addresses.address.province', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Postal Code', 'addresses.address.postalCode', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Country', 'addresses.address.country', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'addresses.address'
-- EMAIL
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, fielddatatype_id, parentfielddef_id, selectable, non_searchable) select 'Email', 'emails.email', 0, 0, 1, 1, fdt.fielddatatype_id, null, 1, 0 from FIELD_DATA_TYPE fdt where fdt.DATATYPE = 'Email'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Email Address', 'emails.email.emailAddress', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'emails.email'
-- PHONE
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, fielddatatype_id, parentfielddef_id, selectable, non_searchable) select 'Telephone', 'telephones.telephone', 1, 0, 1, 1, fdt.fielddatatype_id, null, 1, 0  from FIELD_DATA_TYPE fdt where fdt.DATATYPE = 'Phone'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Phone Type', 'telephones.telephone.phoneType', 1, 0, 1, 1, fielddef_id, 0, 0, 1  from field_def where path = 'telephones.telephone'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Phone Number', 'telephones.telephone.phoneNumber', 1, 0, 1, 1, fielddef_id, 0, 0, 1  from field_def where path = 'telephones.telephone'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Extension', 'telephones.telephone.extension', 1, 0, 1, 1, fielddef_id, 0, 0, 1  from field_def where path = 'telephones.telephone'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Long Distance', 'telephones.telephone.longDistance', 1, 0, 1, 1, fielddef_id, 0, 0, 10  from field_def where path = 'telephones.telephone'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Tel Primary', 'telephones.telephone.telPrimary', 1, 0, 1, 1, fielddef_id, 0, 0, 10  from field_def where path = 'telephones.telephone'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Telecom Org', 'telephones.telephone.telecomOrg', 1, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'telephones.telephone'
-- Appointment Details
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) values ('Appointment Details', 'appointmentDetails.appointmentDetail', 0, 0, 1, 1, null, 1, 0)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Start Date', 'appointmentDetails.appointmentDetail.startDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'End Date', 'appointmentDetails.appointmentDetail.endDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Personnel Area', 'appointmentDetails.appointmentDetail.personnelArea', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Personnel Area Text', 'appointmentDetails.appointmentDetail.personnelAreaText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Personnel Subarea', 'appointmentDetails.appointmentDetail.persSubArea', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Personnel Subarea Text', 'appointmentDetails.appointmentDetail.persSubAreaText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Org Unit', 'appointmentDetails.appointmentDetail.orgUnit', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Org Unit Text', 'appointmentDetails.appointmentDetail.orgUnitText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Percentage', 'appointmentDetails.appointmentDetail.percentage', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Job', 'appointmentDetails.appointmentDetail.job', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Job Text', 'appointmentDetails.appointmentDetail.jobText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Position', 'appointmentDetails.appointmentDetail.position', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Position Text', 'appointmentDetails.appointmentDetail.positionText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Employee Group', 'appointmentDetails.appointmentDetail.employeeGroup', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Employee Group Text', 'appointmentDetails.appointmentDetail.employeeGroupText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Employee Subgroup', 'appointmentDetails.appointmentDetail.employeeSubGroup', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Employee Subgroup Text', 'appointmentDetails.appointmentDetail.employeeSubGroupText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Payroll Area', 'appointmentDetails.appointmentDetail.payrollArea', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Payroll Area Text', 'appointmentDetails.appointmentDetail.payrollAreaText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Work Contract', 'appointmentDetails.appointmentDetail.workContract', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Work Contract Text', 'appointmentDetails.appointmentDetail.workContractText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Cost Center', 'appointmentDetails.appointmentDetail.costCenter', 0, 0, 1, 1, fielddef_id, 0, 0, 3 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Fund Center', 'appointmentDetails.appointmentDetail.fundCenter', 0, 0, 1, 1, fielddef_id, 0, 0, 3 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Order', 'appointmentDetails.appointmentDetail.order', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Fund', 'appointmentDetails.appointmentDetail.fund', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'appointmentDetails.appointmentDetail'

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) values ('Basic Pay', 'basicPay.basicPay', 1, 0, 1, 1, null, 1, 0)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Start Date', 'basicPay.basicPay.startDate', 1, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'basicPay.basicPay'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'End Date', 'basicPay.basicPay.endDate', 1, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'basicPay.basicPay'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Pay Scale Type', 'basicPay.basicPay.payScaleType', 1, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'basicPay.basicPay'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Pay Scale Type Text', 'basicPay.basicPay.payScaleTypeText', 1, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'basicPay.basicPay'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Pay Scale Area', 'basicPay.basicPay.payScaleArea', 1, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'basicPay.basicPay'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Pay Scale Area Text', 'basicPay.basicPay.payScaleAreaText', 1, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'basicPay.basicPay'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Pay Scale Group', 'basicPay.basicPay.payScaleGroup', 1, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'basicPay.basicPay'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Pay Scale Level', 'basicPay.basicPay.payScaleLevel', 1, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'basicPay.basicPay'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Wage Type', 'basicPay.basicPay.wageType', 1, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'basicPay.basicPay'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Wage Type Text', 'basicPay.basicPay.wageTypeText', 1, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'basicPay.basicPay'

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) values ('Contract Element', 'contractElements.contractElement', 0, 0, 1, 1, null, 1, 0)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Start Date', 'contractElements.contractElement.startDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'contractElements.contractElement'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'End Date', 'contractElements.contractElement.endDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'contractElements.contractElement'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Contract Type', 'contractElements.contractElement.contractType', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'contractElements.contractElement'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Contract Type Text', 'contractElements.contractElement.contractTypeText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'contractElements.contractElement'

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) values ('Post Sec Ed', 'education.postSecEd', 0, 0, 1, 1, null, 1, 0)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Education Code', 'education.postSecEd.educationCode', 0, 0, 1, 1, fielddef_id, 0, 1, 3 from field_def where path = 'education.postSecEd'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Education Code Text', 'education.postSecEd.educationCodeText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.postSecEd'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Institution', 'education.postSecEd.instution', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'education.postSecEd'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Institution Text', 'education.postSecEd.instutionText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.postSecEd'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Year Conferred', 'education.postSecEd.yearConfirmed', 0, 0, 1, 1, fielddef_id, 0, 0, 3 from field_def where path = 'education.postSecEd'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Verified', 'education.postSecEd.verified', 0, 0, 1, 1, fielddef_id, 0, 0, 1  from field_def where path = 'education.postSecEd'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Source Document', 'education.postSecEd.sourceDocument', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'education.postSecEd'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Source Document Text', 'education.postSecEd.sourceDocumentText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.postSecEd'

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) values ('Career Progress', 'education.careerProgress', 0, 0, 1, 1, null, 1, 0)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Start Date', 'education.careerProgress.startDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'education.careerProgress'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'End Date', 'education.careerProgress.endDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'education.careerProgress'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Faculty Rank', 'education.careerProgress.facultyRank', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'education.careerProgress'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Faculty Rank Text', 'education.careerProgress.facultyRankText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.careerProgress'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Faculty Rank Date', 'education.careerProgress.facultyRankDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'education.careerProgress'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Tenure', 'education.careerProgress.tenure', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'education.careerProgress'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Tenure Text', 'education.careerProgress.tenureText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.careerProgress'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Tenure Effective Date', 'education.careerProgress.tenureEffectiveDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'education.careerProgress'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Tenure Review Date', 'education.careerProgress.tenureReviewDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'education.careerProgress'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Adjunct Visit', 'education.careerProgress.adjunctVisit', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.careerProgress'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Adjunct Visit Description', 'education.careerProgress.adjunctVisitDescription', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.careerProgress'

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) values ('Prof Designation', 'education.profDesignation', 0, 0, 1, 1, null, 1, 0)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Start Date', 'education.profDesignation.startDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'education.profDesignation'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'End Date', 'education.profDesignation.endDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'education.profDesignation'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Designation Code', 'education.profDesignation.designationCode', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.profDesignation'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Designation Code Text', 'education.profDesignation.designationCodeText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.profDesignation'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Year Conferred', 'education.profDesignation.yearConferred', 0, 0, 1, 1, fielddef_id, 0, 0, 3 from field_def where path = 'education.profDesignation'

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) values ('Award', 'education.award', 0, 0, 1, 1, null, 1, 0)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Start Date', 'education.award.startDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'education.award'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'End Date', 'education.award.endDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'education.award'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Award Honour', 'education.award.awardHonour', 0, 0, 1, 1, fielddef_id, 0, 0, 3 from field_def where path = 'education.award'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Award Honour Name', 'education.award.awardHonourName', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.award'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Year Conferred', 'education.award.yearConferred', 0, 0, 1, 1, fielddef_id, 0, 0, 3 from field_def where path = 'education.award'

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) values ('Grad Appt', 'education.gradAppt', 0, 0, 1, 1, null,1, 0)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Start Date', 'education.gradAppt.startDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'education.gradAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'End Date', 'education.gradAppt.endDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'education.gradAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Graduate Unit', 'education.gradAppt.graduateUnit', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'education.gradAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Graduate Unit Text', 'education.gradAppt.graduateUnitText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.gradAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Supervisory Level', 'education.gradAppt.supervisoryLevel', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'education.gradAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Supervisory Level Text', 'education.gradAppt.supervisoryLevelText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.gradAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Primary Graduate Indicator', 'education.gradAppt.primaryGradAppointIndicator', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.gradAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Restriction Indicator', 'education.gradAppt.restrictionIndicator', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'education.gradAppt'

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) values ('Other University Appointment', 'otherUnivAppts.otherUnivAppt', 0, 0, 1, 1, null, 1, 0)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Start Date', 'otherUnivAppts.otherUnivAppt.startDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'End Date', 'otherUnivAppts.otherUnivAppt.endDate', 0, 0, 1, 1, fielddef_id, 0, 0, 4 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Sub Type', 'otherUnivAppts.otherUnivAppt.subtype', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Sub Type Text', 'otherUnivAppts.otherUnivAppt.subtypeText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Appointment Type', 'otherUnivAppts.otherUnivAppt.appointmentType', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Appointment Type Text', 'otherUnivAppts.otherUnivAppt.appointmentTypeText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Organization Unit', 'otherUnivAppts.otherUnivAppt.organizationUnit', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Organization UnitText', 'otherUnivAppts.otherUnivAppt.organizationUnitText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Emeritus Or Emerita Rank', 'otherUnivAppts.otherUnivAppt.emeritusOrEmeritaRank', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Main Indicator', 'otherUnivAppts.otherUnivAppt.mainIndicator', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Academic Rank', 'otherUnivAppts.otherUnivAppt.academicRank', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Academic Rank Text', 'otherUnivAppts.otherUnivAppt.academicRankText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Career Path Title', 'otherUnivAppts.otherUnivAppt.careerPathTitle', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Hospital Code', 'otherUnivAppts.otherUnivAppt.hospitalCode', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Clinical Contract Status', 'otherUnivAppts.otherUnivAppt.clinContractStatus', 0, 0, 1, 1, fielddef_id, 0, 1, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Clinical Contract Status Text', 'otherUnivAppts.otherUnivAppt.clinContractStatusText', 0, 0, 1, 1, fielddef_id, 0, 0, 1 from field_def where path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'CPSO Number', 'otherUnivAppts.otherUnivAppt.cpsoNumber', 0, 0, 1, 1, fielddef_id, 0, 0, 3 from field_def where path = 'otherUnivAppts.otherUnivAppt'
-- INTERNAL
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, FIELDDATATYPE_ID, selectable, non_searchable) select 'Tag Entity', 'tags.tag', 0, 0, 1, feedsource_id, null, 3, 0, 0 from feed_source where source = 'INTERNAL'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Tag', 'tags.tag.name', 0, 0, 1, feedsource_id, fielddef_id, 1, 0, 9 from field_def where path = 'tags.tag'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, FIELDDATATYPE_ID, selectable, non_searchable) select 'Static Group Entity', 'staticGroups.staticGroup', 0, 0, 1, feedsource_id, null, 3, 0, 0 from feed_source where source = 'INTERNAL'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Group (Static)', 'staticGroups.staticGroup.name', 0, 0, 1, feedsource_id, fielddef_id, 1, 0, 9 from field_def where path = 'staticGroups.staticGroup'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, FIELDDATATYPE_ID, selectable, non_searchable) select 'Dynamic Group Entity', 'dynamicGroups.dynamicGroup', 0, 0, 1, feedsource_id, null, 3, 0, 0 from feed_source where source = 'INTERNAL'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Group (Dynamic)', 'dynamicGroups.dynamicGroup.name', 0, 0, 1, feedsource_id, fielddef_id, 1, 0, 9 from field_def where path = 'dynamicGroups.dynamicGroup'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) select 'Notes', 'notes.notes', 0, 0, 1, FEEDSOURCE_ID, null, 1, 0 from FEED_SOURCE where source = 'INTERNAL' -- BSL see B-23909
-- HRIS EXTRACTED
/*
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Employee Group', 'appointmentDetails.appointmentDetail.employeeGroupText', 0, 0, 1, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'appointmentDetails.appointmentDetail'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Rank', 'education.careerProgress.facultyRankText', 0, 0, 1, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'education.careerProgress'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Emeritus', 'otherUnivAppts.otherUnivAppt.emeritusOrEmeritaRank', 0, 0, 1, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'otherUnivAppts.otherUnivAppt'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'CPSO', 'otherUnivAppts.otherUnivAppt.cpsoNumber', 0, 0, 3, fs.feedsource_id, fielddef_id, 1, 0, 3 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'otherUnivAppts.otherUnivAppt'
*/

-- added new extracted fields (smart fields)
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) select 'Extracted Field', 'extractedFields', 0, 0, 0, fdt.fielddatatype_id, null, 0, 0 from FIELD_DATA_TYPE fdt where fdt.DATATYPE = 'Text'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Current Rank', 'extractedFields.currentRank', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Current Rank Date', 'extractedFields.currentRankDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Appointment Position', 'extractedFields.appointmentPosition', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Is Staff', 'extractedFields.isStaff', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Current Tenure Status', 'extractedFields.currentTenureStatus', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Current Tenure Effective Date', 'extractedFields.currentTenureEffectiveDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Current Tenure Review Date', 'extractedFields.currentTenureReviewDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Is Emeritus', 'extractedFields.isEmeritus', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'First Appt To Dept', 'extractedFields.firstAppointmentToDept', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Hospital Affilation', 'extractedFields.hospitalAffiliation', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Appointment Type', 'extractedFields.appointmentType', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Primary Department', 'extractedFields.primaryDepartment', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'CPSO', 'extractedFields.cpsoNumber', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 3 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Clinician', 'extractedFields.clinician', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Employee Group', 'extractedFields.employeeGroup', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'

-- HRIS DERIVED
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Retirement Date (U)', 'formula:65+personalData.birthDate', 1, 0, 0, fs.FEEDSOURCE_ID, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS DERIVED' and fd.path = 'personalData'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Retirement Date (P)', 'formula:71+personalData.birthDate', 1, 0, 0, fs.FEEDSOURCE_ID, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS DERIVED' and fd.path = 'personalData'
-- PRIMARY_COMMUNICAITON_FIELD
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, fielddatatype_id, parentfielddef_id, selectable, non_searchable) select 'Primary Address', 'primary+addresses.address', 0, 0, 0, fs.feedsource_id, fdt.fielddatatype_id, null, 1, 0 from FEED_SOURCE fs, FIELD_DATA_TYPE fdt where fs.SOURCE = 'PRIMARY_COMMUNICAITON_FIELD' and fdt.DATATYPE='Address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Address Type', 'primary+addresses.address.addressType', 0, 0, 1, feedsource_id, fielddef_id, 0, 1, 1 from field_def fd where path = 'primary+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Address Type Text', 'primary+addresses.address.addressTypeText', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'primary+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Street And House Number', 'primary+addresses.address.streetAndHouseNumber', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'primary+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Second Address Line', 'primary+addresses.address.secondAddressLine', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'primary+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'City', 'primary+addresses.address.city', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'primary+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Province', 'primary+addresses.address.province', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'primary+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Postal Code', 'primary+addresses.address.postalCode', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'primary+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Country', 'primary+addresses.address.country', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'primary+addresses.address'
-- PRIMARY EMAIL
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, fielddatatype_id, parentfielddef_id, selectable, non_searchable) select 'Primary Email', 'primary+emails.email', 0, 0, 0, fs.feedsource_id, fdt.fielddatatype_id, null, 1, 0 from FEED_SOURCE fs, FIELD_DATA_TYPE fdt where fs.SOURCE = 'PRIMARY_COMMUNICAITON_FIELD' and fdt.DATATYPE='Email'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Email Address', 'primary+emails.email.emailAddress', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'primary+emails.email' 
-- PRIMARY TELEPHONE
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, fielddatatype_id, parentfielddef_id, selectable, non_searchable) select 'Primary Telephone', 'primary+telephones.telephone', 1, 0, 0, fs.feedsource_id, fdt.fielddatatype_id, null, 1, 0 from FEED_SOURCE fs, FIELD_DATA_TYPE fdt where fs.SOURCE = 'PRIMARY_COMMUNICAITON_FIELD' and fdt.DATATYPE='Phone' 
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Phone Number', 'primary+telephones.telephone.phoneNumber', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'primary+telephones.telephone'
-- OPERATORS
insert into PROFILE_GROUP_CRIT_OP (RESTRICTION_OP_NAME, HTML_ENTITY, DESCRIPTION, DISPLAY_WEIGHT) VALUES ('eq', '=', 'Equals', 0) --1
insert into PROFILE_GROUP_CRIT_OP (RESTRICTION_OP_NAME, HTML_ENTITY, DESCRIPTION, DISPLAY_WEIGHT) VALUES ('ne', '&ne;', 'Not Equals', 0) --2
insert into PROFILE_GROUP_CRIT_OP (RESTRICTION_OP_NAME, HTML_ENTITY, DESCRIPTION, DISPLAY_WEIGHT) VALUES ('gt', '&gt;', 'Greater Than', 0) --3
insert into PROFILE_GROUP_CRIT_OP (RESTRICTION_OP_NAME, HTML_ENTITY, DESCRIPTION, DISPLAY_WEIGHT) VALUES ('lt', '&lt;', 'Less Than', 0) --4
insert into PROFILE_GROUP_CRIT_OP (RESTRICTION_OP_NAME, HTML_ENTITY, DESCRIPTION, DISPLAY_WEIGHT) VALUES ('ge', '&ge;', 'Greater Than or Equal', 0) --5
insert into PROFILE_GROUP_CRIT_OP (RESTRICTION_OP_NAME, HTML_ENTITY, DESCRIPTION, DISPLAY_WEIGHT) VALUES ('le', '&le;', 'Less Than or Equal', 0) --6
insert into PROFILE_GROUP_CRIT_OP (RESTRICTION_OP_NAME, HTML_ENTITY, DESCRIPTION, DISPLAY_WEIGHT) VALUES ('isNull', 'NULL', 'is Blank', 0) --7
insert into PROFILE_GROUP_CRIT_OP (RESTRICTION_OP_NAME, HTML_ENTITY, DESCRIPTION, DISPLAY_WEIGHT) VALUES ('isNotNull', 'Not NULL', 'is Not Blank', 0) --8
insert into PROFILE_GROUP_CRIT_OP (RESTRICTION_OP_NAME, HTML_ENTITY, DESCRIPTION, DISPLAY_WEIGHT) VALUES ('iLike', 'Begins', 'Starts with text', 0) --9
insert into PROFILE_GROUP_CRIT_OP (RESTRICTION_OP_NAME, HTML_ENTITY, DESCRIPTION, DISPLAY_WEIGHT) VALUES ('iLike', 'Ends', 'Ends with text', 0) --10
insert into PROFILE_GROUP_CRIT_OP (RESTRICTION_OP_NAME, HTML_ENTITY, DESCRIPTION, DISPLAY_WEIGHT) VALUES ('iLike', 'Contains', 'Contains text', 0) --11

-- ASSOCIATE OPERATORS FOR FIELD DATA TYPES
-- Equals
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (1,1) -- Text
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (1,3) -- Number
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (1,4) -- Date
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (1,5) -- URL
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (1,6) -- Email
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (1,7) -- Phone
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (1,9) -- Dropdown
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (1,10) -- Checkbox
-- Not Equals
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (2,1) -- Text
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (2,3) -- Number
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (2,4) -- Date
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (2,5) -- URL
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (2,6) -- Email
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (2,9) -- Dropdown
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (2,10) -- Checkbox
-- Greater Than
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (3,3) -- Number
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (3,4) -- Date
-- Less Than
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (4,3) -- Number
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (4,4) -- Date
-- Greater Than or Equal
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (5,3) -- Number
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (5,4) -- Date
-- Less Than or Equal
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (6,3) -- Number
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (6,4) -- Date
-- NULL
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (7,1) -- Text
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (7,3) -- Number
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (7,4) -- Date
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (7,5) -- URL
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (7,6) -- Email
-- Not NULL
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (8,1) -- Text
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (8,3) -- Number
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (8,4) -- Date
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (8,5) -- URL
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (8,6) -- Email
-- Begins
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (9,1) -- Text
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (9,5) -- URL
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (9,6) -- Email
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (9,7) -- Phone
-- Ends
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (10,1) -- Text
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (10,5) -- URL
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (10,6) -- Email
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (10,7) -- Phone
-- Contains
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (11,1) -- Text
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (11,5) -- URL
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (11,6) -- Email
insert into OPERATOR_DATATYPE (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID) VALUES (11,7) -- Phone

-- DEPARTMENT
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('89 Chestnut Residence',	'00000488', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('A&S: Aboriginal Studies',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Academic Technology Initiatives',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Access & Information Serv.',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Accessibility Services',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Accounting-Comptroller Ofc',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Admin&Gen Serv Camp Pol-07',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Admissions & Stud.Affairs',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Advancement Medicine',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ancillary Services',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('APSC Admin. Units',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('APSC: Ofc of the Dean - Faculty General',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('ARTSC: Ofc of the Dean',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Asian Institute',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Bant & Best Dept Med Res',	'00000166', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Banting & Best Diabetes Ctr',	'00000204', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Biological Sciences - Zoology',	'00000454', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Biological Sciences-Biochemistry',	'00000450', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Campus Mail Services',	'00000305', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Campus Safety & Security',	'00000547', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Cardiovascular Sci. Collaborative Prog',	'00000212', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Career Centre',	'00000281', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Cell and Systems Biology',	'00000527', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Centre for Health Promotion',	'00000209', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Centre for Teaching Support & Innovation',	'00000480', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Centre Women''s Studies Ed.',	'00000378', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('CERIS - The Ontario Metropolis Centre',	'00000411', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Cities Centre',	'00000561', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Clinical Sc Div',	'00000161', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('College Administration',	'00000091', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Community Police',	'00000124', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Comparative Evaluation Services',	'00000560', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Continuing Education',	'00000381', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Continuing Education, Medicine',	'00000193', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Counselling & Psychological Services',	'00000287', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ctr for Environment',	'00000036', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ctr for International Studies',	'00000242', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ctr for Medieval Studies',	'00000244', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ctr for Phys Ed, Athletics & Recreation',	'00000135', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ctr Hydrogen & Electroch. Studies',	'00000230', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ctr of Criminology',	'00000238', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dalla Lana School of Public Health',	'00000190', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Daniels Faculty of Arch., Land & Design',	'00000025', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dean''s Office',	'00000234', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Department of Anaesthesia',	'00000174', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Department of Psychology',	'00000539', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Adult Educ. & Couns. Psychology',	'00000364', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Astronomy and Astrophysics',	'00000046', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Biochemistry',	'00000167', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Biological Sciences',	'00000102', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Chemical Eng.& Applied Chemistry',	'00000223', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Chemistry',	'00000048', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Civil Engineering',	'00000224', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Community Health(Grad)',	'00000195', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Computer & Mathematical Sciences',	'00000485', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Computer Science',	'00000050', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Curriculum, Teaching & Learning',	'00000365', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Development',	'00000338', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Economics',	'00000074', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Electrical & Computer Eng',	'00000225', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of English',	'00000052', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Family & Community Medicine',	'00000177', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Fine Art',	'00000053', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of French',	'00000054', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Geography',	'00000055', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Health Policy, Mgmt & Evaluation',	'00000189', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of History',	'00000058', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Human Devel. & Appl. Psychology',	'00000366', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Humanities',	'00000100', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Immunology',	'00000199', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Lab. Medicine & Pathobiology',	'00000183', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Linguistics',	'00000060', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Management',	'00000123', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Mechanical & Industrial Eng',	'00000226', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Medical Biophysics',	'00000168', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Medical Imaging',	'00000185', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Medicine',	'00000178', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Molecular Genetics',	'00000169', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Near & Middle East.Civilizations',	'00000062', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Nutritional Sciences',	'00000171', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Obstetrics&Gynaecology',	'00000179', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Occupational Science & Therapy',	'00000186', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Ophthalmology & Vision Sciences',	'00000180', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Otolaryngology - Head & Neck Sur',	'00000181', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Paediatrics',	'00000182', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Pharmacology',	'00000172', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Philosophy',	'00000063', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Physical Therapy',	'00000213', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Physics',	'00000064', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Physiology',	'00000173', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Psychiatry',	'00000184', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Psychology',	'00000066', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Radiation Oncology',	'00000211', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Rehabilitation Science(Graduate)',	'00000214', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Social Sciences',	'00000101', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Sociology',	'00000069', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Sociology & Equity Stud.in Educ.',	'00000176', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Speech-Language Pathology',	'00000192', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Surgery',	'00000187', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Dept of Theory & Policy Studies in Educ.',	'00000356', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Discovery Commons',	'00000160', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Div of Comparative Med.',	'00000158', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Div of Engineering Science',	'00000221', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Div of Teaching Labs',	'00000164', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Div. of Biomedical Communications',	'00000175', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Division of Anatomy',	'00000400', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Division of Educational Computing',	'00000415', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Donnelly Centre',	'00000474', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('DUA-Advancement Services',	'00000324', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ecology and Evolutionary Biology',	'00000526', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Education Commons',	'00000382', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Education Deans',	'00000417', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Engineering Communication Program',	'00000360', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Enrolment Services: Admissions',	'00000582', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Enterprise Apps & Solutions Integration',	'00000301', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Environmental Hlth&Safety',	'00000299', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('F.&S. Bldgs & Grounds -01',	'00000314', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('F.&S. Utilities -05',	'00000310', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Facilities Management',	'00000096', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Faculty of Dentistry',	'00000012', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Faculty of Forestry',	'00000017', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Faculty of Information',	'00000019', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Faculty of Kinesiology & Physl Ed',	'00000031', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Faculty of Law',	'00000018', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Faculty of Music',	'00000023', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Faculty of Pharmacy',	'00000014', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Faculty of Social Work',	'00000024', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Faculty Registrar',	'00000073', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Financial Services',	'00000118', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Gage Occup. & Environ. Health Unit',	'00000191', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('General University',	'00000322', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Graduate House',	'00000398', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Hart House',	'00000043', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Health and Wellness',	'00000557', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Health Counselling Centre',	'00000136', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Health Service',	'00000285', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Heart & Stroke/R.Lewar Ctr of Excellence',	'00000484', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('History of Medicine Program',	'00000170', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Housing Service',	'00000282', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Human Biology',	'00000586', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Human Resource Services',	'00000146', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Human Resources',	'00000298', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Humanities - Visual & Performing Arts',	'00000448', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('IMG-Ontario',	'00000210', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Info. Technology Service',	'00000271', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Information Commons',	'00000275', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Information Security',	'00000569', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Innovations & Partnerships Office',	'00000328', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Inst for Aerospace Studies',	'00000219', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Inst for Hist & Phil of Sci & Tech',	'00000248', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Inst of Biomat. & Biomedical Engineering',	'00000157', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Inst of Medical Science',	'00000200', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Inst of Population and Health',	'00000413', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Inst.Biomat & Biomed.Eng.',	'00000220', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Institute for Life Course & Aging',	'00000253', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Institute of CCIT',	'00000494', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Integrated Client Services',	'00000570', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Internal Audit',	'00000042', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('International Student Ctr',	'00000284', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Joint Centre for Bioethics',	'00000201', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Joseph L. Rotman School of Management',	'00000235', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Lawrence S. Bloomberg Faculty of Nursing',	'00000013', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Level 3 Facility',	'00000513', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Library Circulation',	'00000436', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('LTD & Special Cases Dept',	'00000325', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Master of  Biotechnology Program',	'00000489', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('McLaughlin Centre for Molecular Medicine',	'00000040', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Mclaughlin-Rotman Ctr. for Global Health',	'00000524', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('MED: Ofc of the Dean',	'00000154', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('MED:Strategy Communications External Rel',	'00000585', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('MedStore',	'00000216', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Microscopy Imaging Laboratory',	'00000198', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Munk School of Global Affairs',	'00000399', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('NCIC Epidemiology Unit',	'00000194', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('New College',	'00000028', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('New College Prog',	'00000077', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ofc of Governing Council',	'00000002', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ofc of Student Affairs & Services',	'00000389', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ofc of Student Life',	'00000280', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ofc of the Dean - Faculty',	'00000206', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ofc of the Ombudsperson',	'00000041', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ofc of the President',	'00000003', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ofc of the Registrar',	'00000131', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ofc of Vice Dean, Graduate  Affairs',	'00000410', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ofc of Vice Pres. Research',	'00000326', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ofc of Vice-Provost, Planning & Budget',	'00000291', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ofc of VP-University Relations',	'00000409', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Off. of the Vice Principal (Acad) & Dean',	'00000105', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Office of the Vice Principal Research',	'00000479', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Ontario Tobacco Research Unit',	'00000516', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('OSL Director Admin',	'00000553', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('OSM Test & Exam Services',	'00000548', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Parking Services',	'00000321', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Postgraduate Medical Education',	'00000197', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Procurement Services',	'00000335', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Psychology',	'00000453', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Registrar''s Office',	'00000370', 0, 0)
--insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Rehabilitation Sciences Sector',	'00000000', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Rehabilitation Sciences Sector',	'00000419', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Research Oversight & Compliance Office',	'00000354', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Research Services Office',	'00000327', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Resource Centre for Academic Technology',	'00000478', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Sch of Continuing Studies',	'00000026', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('School of Public Policy',	'00000530', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('SciNet',	'00000541', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('SGS General Administration',	'00000395', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('SGS: English Language Writing Support',	'00000515', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Social Sciences-Health Studies',	'00000470', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Social Sciences-Political Sc.',	'00000472', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Standardized Patient Program',	'00000564', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Stores',	'00000529', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Structural Genomics Consortium',	'00000509', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Student Services',	'00000372', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Tanz Ctr Res. Neurodegenerative Diseases',	'00000208', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Toronto Western Research Institute',	'00000162', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Transitional Year Program',	'00000276', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('UC: Art Centre',	'00000407', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Undergraduate Medical Education',	'00000207', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('University College Prog',	'00000078', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('UTM: Anthropology',	'00000503', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('UTM: Biology',	'00000499', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('UTM: Chem/Phys. Sciences',	'00000501', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('UTM: Dept. of Language Studies',	'00000497', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('UTM: Economics',	'00000505', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('UTM: Geography',	'00000506', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('UTM: Ofc of Advancement',	'00000132', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('UTM: Ofc of the Dean',	'00000491', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('UTM: Psychology',	'00000500', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('UTM: Sociology',	'00000508', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Vice-Dean Research and Int. Relations',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Vice-Provost Relations Health Care Inst.',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Wilson Centre for Research in Education',	'********', 0, 0)
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Woodsworth College',	'********', 0, 0)
-- USERS
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'admin1', 'admin1', 'John', 'E', 'Administrator', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name = 'Dept of Medical Imaging';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'dcsysadmin1', 'dcsysadmin1', 'System', 'DC', 'Administrator', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name = 'Discovery Commons';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'vanbeenc', 'vanbeenc', 'Court', '', 'Vanbeek', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name = 'Discovery Commons';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'chansam7', 'chansam7', 'Sam', '', 'Chan', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name = 'Discovery Commons';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'mansoork', 'mansoork', 'Kamal', '', 'Mansoor', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name = 'Discovery Commons';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'hradmin1', 'hradmin1', 'Jane', '', 'Administrator', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name = 'Discovery Commons';
-- group members (create Department Admin for virtual Discovery Commons department)
insert into group_members select u.users_id, g.group_id from users u, groups g where g.name = 'Department Administrators' and u.username = 'admin1';
-- DC Sys Admins
insert into group_members select u.users_id, g.group_id from users u, groups g where g.name = 'DC System Administrators' and u.username = 'dcsysadmin1';
insert into group_members select u.users_id, g.group_id from users u, groups g where g.name = 'DC System Administrators' and u.username = 'vanbeenc';
insert into group_members select u.users_id, g.group_id from users u, groups g where g.name = 'DC System Administrators' and u.username = 'chansam7';
insert into group_members select u.users_id, g.group_id from users u, groups g where g.name = 'DC System Administrators' and u.username = 'mansoork';
-- HR Admin
insert into group_members select u.users_id, g.group_id from users u, groups g where g.name = 'HR Administrators' and u.username = 'hradmin1';