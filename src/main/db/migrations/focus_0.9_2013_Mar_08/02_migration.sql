

/****** Object:  Table [dbo].[DYNAMIC_GROUP]    Script Date: 2013-02-25 3:55:26 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[DYNAMIC_GROUP](
	[DYNA<PERSON><PERSON><PERSON><PERSON><PERSON>_ID] [numeric](19, 0) IDENTITY(1,1) NOT NULL,
	[CREATEDBY] [varchar](255) NULL,
	[CREATEDON] [datetime] NULL,
	[DELETED] [tinyint] NULL,
	[OPTLOCK] [numeric](19, 0) NULL,
	[UPDATEDBY] [varchar](255) NULL,
	[UPDATEDON] [datetime] NULL,
	[IS_COMPONENT] [tinyint] NOT NULL,
	[DEPT_VISIBILITY] [int] NOT NULL,
	[NAME] [varchar](255) NOT NULL,
	[AUTHUSER_ID] [numeric](19, 0) NOT NULL,
	[DEPARTMENT_ID] [numeric](19, 0) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[DY<PERSON><PERSON><PERSON><PERSON>OUP_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
UNIQUE NONCLUSTERED 
(
	[AUTHUSER_ID] ASC,
	[NAME] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[DYNAMIC_GROUP]  WITH CHECK ADD  CONSTRAINT [FK__DYNAMIC_GROUP__DEPARTMENT] FOREIGN KEY([DEPARTMENT_ID])
REFERENCES [dbo].[DEPARTMENT] ([DEPARTMENT_ID])
GO

ALTER TABLE [dbo].[DYNAMIC_GROUP] CHECK CONSTRAINT [FK__DYNAMIC_GROUP__DEPARTMENT]
GO

ALTER TABLE [dbo].[DYNAMIC_GROUP]  WITH CHECK ADD  CONSTRAINT [FK__DYNAMIC_GROUP__USERS] FOREIGN KEY([AUTHUSER_ID])
REFERENCES [dbo].[USERS] ([users_id])
GO

ALTER TABLE [dbo].[DYNAMIC_GROUP] CHECK CONSTRAINT [FK__DYNAMIC_GROUP__USERS]
GO


/****** Object:  Table [dbo].[DYNAMIC_GROUP_CRIT]    Script Date: 2013-02-25 3:56:30 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[DYNAMIC_GROUP_CRIT](
	[DYNAMICGROUPCRIT_ID] [numeric](19, 0) IDENTITY(1,1) NOT NULL,
	[VALUE] [varchar](255) NULL,
	[DYNAMICGROUP_ID] [numeric](19, 0) NULL,
	[PROFILEGROUPCRITOP_ID] [numeric](19, 0) NULL,
	[SEARCHABLE_FIELDDEF_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[DYNAMICGROUPCRIT_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[DYNAMIC_GROUP_CRIT]  WITH CHECK ADD  CONSTRAINT [FK__DYNAMICGROUPCRITERION__DYNAMICGROUP] FOREIGN KEY([DYNAMICGROUP_ID])
REFERENCES [dbo].[DYNAMIC_GROUP] ([DYNAMICGROUP_ID])
GO

ALTER TABLE [dbo].[DYNAMIC_GROUP_CRIT] CHECK CONSTRAINT [FK__DYNAMICGROUPCRITERION__DYNAMICGROUP]
GO

ALTER TABLE [dbo].[DYNAMIC_GROUP_CRIT]  WITH CHECK ADD  CONSTRAINT [FK6F7B7FDA6DF8B566] FOREIGN KEY([SEARCHABLE_FIELDDEF_ID])
REFERENCES [dbo].[FIELD_DEF] ([FIELDDEF_ID])
GO

ALTER TABLE [dbo].[DYNAMIC_GROUP_CRIT] CHECK CONSTRAINT [FK6F7B7FDA6DF8B566]
GO

ALTER TABLE [dbo].[DYNAMIC_GROUP_CRIT]  WITH CHECK ADD  CONSTRAINT [FK6F7B7FDA82E98DCF] FOREIGN KEY([PROFILEGROUPCRITOP_ID])
REFERENCES [dbo].[PROFILE_GROUP_CRIT_OP] ([PROFILEGROUPCRITOP_ID])
GO

ALTER TABLE [dbo].[DYNAMIC_GROUP_CRIT] CHECK CONSTRAINT [FK6F7B7FDA82E98DCF]
GO


/****** Object:  Table [dbo].[STATIC_GROUP]    Script Date: 2013-02-25 3:57:09 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[STATIC_GROUP](
	[STATICGROUP_ID] [numeric](19, 0) IDENTITY(1,1) NOT NULL,
	[CREATEDBY] [varchar](255) NULL,
	[CREATEDON] [datetime] NULL,
	[DELETED] [tinyint] NULL,
	[OPTLOCK] [numeric](19, 0) NULL,
	[UPDATEDBY] [varchar](255) NULL,
	[UPDATEDON] [datetime] NULL,
	[DEPT_VISIBILITY] [int] NOT NULL,
	[NAME] [varchar](255) NOT NULL,
	[AUTHUSER_ID] [numeric](19, 0) NOT NULL,
	[DEPARTMENT_ID] [numeric](19, 0) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[STATICGROUP_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
UNIQUE NONCLUSTERED 
(
	[AUTHUSER_ID] ASC,
	[NAME] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[STATIC_GROUP]  WITH CHECK ADD  CONSTRAINT [FK__STATIC_GROUP__DEPARTMENT] FOREIGN KEY([DEPARTMENT_ID])
REFERENCES [dbo].[DEPARTMENT] ([DEPARTMENT_ID])
GO

ALTER TABLE [dbo].[STATIC_GROUP] CHECK CONSTRAINT [FK__STATIC_GROUP__DEPARTMENT]
GO

ALTER TABLE [dbo].[STATIC_GROUP]  WITH CHECK ADD  CONSTRAINT [FK__STATIC_GROUP__USERS] FOREIGN KEY([AUTHUSER_ID])
REFERENCES [dbo].[USERS] ([users_id])
GO

ALTER TABLE [dbo].[STATIC_GROUP] CHECK CONSTRAINT [FK__STATIC_GROUP__USERS]
GO


/****** Object:  Table [dbo].[STATICGROUP_PROFILE]    Script Date: 2013-02-25 3:57:40 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[STATICGROUP_PROFILE](
	[STATICGROUP_ID] [numeric](19, 0) NOT NULL,
	[PROFILE_ID] [numeric](19, 0) NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[STATICGROUP_ID] ASC,
	[PROFILE_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE [dbo].[STATICGROUP_PROFILE]  WITH CHECK ADD  CONSTRAINT [FK__PROFILE__STATICGROUP_ID] FOREIGN KEY([PROFILE_ID])
REFERENCES [dbo].[PROFILE] ([PROFILE_ID])
GO

ALTER TABLE [dbo].[STATICGROUP_PROFILE] CHECK CONSTRAINT [FK__PROFILE__STATICGROUP_ID]
GO

ALTER TABLE [dbo].[STATICGROUP_PROFILE]  WITH CHECK ADD  CONSTRAINT [FK__STATICGROUP__PROFILE_ID] FOREIGN KEY([STATICGROUP_ID])
REFERENCES [dbo].[STATIC_GROUP] ([STATICGROUP_ID])
GO

ALTER TABLE [dbo].[STATICGROUP_PROFILE] CHECK CONSTRAINT [FK__STATICGROUP__PROFILE_ID]
GO

/****** Object:  Table [dbo].[EXTRACTED_FIELDS]    Script Date: 2013-03-08 10:13:25 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[EXTRACTED_FIELDS](
	[EXTRACTEDFIELD_ID] [numeric](19, 0) IDENTITY(1,1) NOT NULL,
	[APPOINTMENT_POSITION] [varchar](255) NULL,
	[CLN_APPOINTMENT_TYPE] [varchar](255) NULL,
	[IS_CLINICIAN] [varchar](255) NULL,
	[CPSO_NUMBER] [numeric](19, 0) NULL,
	[CURRENT_RANK] [varchar](255) NULL,
	[CURRENT_RANK_DATE] [datetime] NULL,
	[CURRENT_TENURE_EFFECT_DATE] [datetime] NULL,
	[CURRENT_TENURE_REVIEW_DATE] [datetime] NULL,
	[CURRENT_TENURE_STATUS] [varchar](255) NULL,
	[EMPLOYEE_GROUP] [varchar](255) NULL,
	[FIRST_APPOINTMENT] [datetime] NULL,
	[HOSPITAL_AFFILIATION] [varchar](255) NULL,
	[IS_EMERITUS] [varchar](255) NULL,
	[IS_STAFF] [varchar](255) NULL,
	[PRIMARY_DEPT] [varchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[EXTRACTEDFIELD_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO


/****** Object:  Table [dbo].[Field_def]   add column ******/

alter table field_def add non_searchable tinyint
GO

alter table field_def add OPERATIONS_FLAG varchar(255)
GO

/****** Object:  Table [dbo].[Field_def]   add new data ******/

update FIELD_DEF set LABEL = 'Dynamic Group Entity', PATH = 'dynamicGroups.dynamicGroup' where path = 'profileGroups.profileGroup';

update FIELD_DEF set LABEL = 'Group (Dynamic)', PATH = 'dynamicGroups.dynamicGroup.name', 
PARENTFIELDDEF_ID = (select fielddef_id from FIELD_DEF where path='dynamicGroups.dynamicGroup') where path = 'profileGroups.profileGroup.name';

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, FIELDDATATYPE_ID, selectable, non_searchable) select 'Static Group Entity', 'staticGroups.staticGroup', 0, 0, 1, feedsource_id, null, 3, 0, 0 from feed_source where source = 'INTERNAL';
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Group (Static)', 'staticGroups.staticGroup.name', 0, 0, 1, feedsource_id, fielddef_id, 1, 0, 1 from field_def where path = 'staticGroups.staticGroup';

update FIELD_DEF set non_searchable = 0
update FIELD_DEF set non_searchable = 1 where PATH in ('addresses.address.addressType', 'appointmentDetails.appointmentDetail.personnelArea',
												'appointmentDetails.appointmentDetail.persSubArea', 'appointmentDetails.appointmentDetail.orgUnit',
												'appointmentDetails.appointmentDetail.job', 'appointmentDetails.appointmentDetail.position',
												'appointmentDetails.appointmentDetail.employeeGroup', 'appointmentDetails.appointmentDetail.employeeSubGroup',
												'appointmentDetails.appointmentDetail.payrollArea', 'appointmentDetails.appointmentDetail.workContract',
												'basicPay.basicPay.payScaleType', 'basicPay.basicPay.payScaleArea',
												'basicPay.basicPay.wageType', 'contractElements.contractElement.contractType',
												'education.postSecEd.educationCode', 'education.postSecEd.instution',
												'education.postSecEd.sourceDocument', 'education.careerProgress.facultyRank',
												'education.careerProgress.tenure', 'education.gradAppt.graduateUnit',
												'education.gradAppt.supervisoryLevel', 'otherUnivAppts.otherUnivAppt.subtype',
												'otherUnivAppts.otherUnivAppt.appointmentType', 'otherUnivAppts.otherUnivAppt.organizationUnit',
												'otherUnivAppts.otherUnivAppt.academicRank', 'otherUnivAppts.otherUnivAppt.clinContractStatus',
												'primary+addresses.address.addressType');

/******   add dynamic_group / static_group data ******/

SET IDENTITY_INSERT DYNAMIC_GROUP ON

insert into DYNAMIC_GROUP(DYNAMICGROUP_ID, CREATEDBY, CREATEDON, DELETED, OPTLOCK, UPDATEDBY, UPDATEDON, IS_COMPONENT, DEPT_VISIBILITY, NAME, AUTHUSER_ID, DEPARTMENT_ID)
SELECT PROFILEGROUP_ID, CREATEDBY, CREATEDON, DELETED, OPTLOCK, UPDATEDBY, UPDATEDON, 0, DEPT_VISIBILITY, NAME, AUTHUSER_ID, DEPARTMENT_ID  FROM PROFILE_GROUP where PROFILEGROUP_TYPE = 1;

SET IDENTITY_INSERT DYNAMIC_GROUP OFF 


SET IDENTITY_INSERT STATIC_GROUP ON

insert into STATIC_GROUP(STATICGROUP_ID, CREATEDBY, CREATEDON, DELETED, OPTLOCK, UPDATEDBY, UPDATEDON, DEPT_VISIBILITY, NAME, AUTHUSER_ID, DEPARTMENT_ID)
SELECT PROFILEGROUP_ID, CREATEDBY, CREATEDON, DELETED, OPTLOCK, UPDATEDBY, UPDATEDON, DEPT_VISIBILITY, NAME, AUTHUSER_ID, DEPARTMENT_ID  FROM PROFILE_GROUP where PROFILEGROUP_TYPE = 0;

SET IDENTITY_INSERT STATIC_GROUP OFF 


SET IDENTITY_INSERT DYNAMIC_GROUP_CRIT ON

insert into DYNAMIC_GROUP_CRIT(DYNAMICGROUPCRIT_ID, VALUE, DYNAMICGROUP_ID, PROFILEGROUPCRITOP_ID, SEARCHABLE_FIELDDEF_ID)
select PROFILEGROUPCRIT_ID, VALUE, PROFILEGROUP_ID, PROFILEGROUPCRITOP_ID, SEARCHABLE_FIELDDEF_ID from PROFILE_GROUP_CRIT 

SET IDENTITY_INSERT DYNAMIC_GROUP_CRIT OFF 


insert into STATICGROUP_PROFILE(STATICGROUP_ID, PROFILE_ID)
select PROFILEGROUP_ID, PROFILE_ID from PROFILEGROUP_PROFILE

/******  Extracted Fields ******/

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable) select 'Extracted Field', 'extractedFields', 0, 0, 0, fdt.fielddatatype_id, null, 0, 0 from FIELD_DATA_TYPE fdt where fdt.DATATYPE = 'Text'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Current Rank Date', 'extractedFields.currentRankDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Appointment Position', 'extractedFields.appointmentPosition', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Current Tenure Status', 'extractedFields.currentTenureStatus', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Current Tenure Effective Date', 'extractedFields.currentTenureEffectiveDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Current Tenure Review Date', 'extractedFields.currentTenureReviewDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'First Appt To Dept', 'extractedFields.firstAppointmentToDept', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 4 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Hospital Affilation', 'extractedFields.hospitalAffiliation', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Appointment Type', 'extractedFields.appointmentType', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Primary Department', 'extractedFields.primaryDepartment', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 1 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Clinician', 'extractedFields.clinician', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields'

-- update existing extracted field and IsStaff derived field

update FIELD_DEF set PATH = 'extractedFields.employeeGroup', PARENTFIELDDEF_ID = (select fd.FIELDDEF_ID from FIELD_DEF fd where fd.PATH = 'extractedFields') where FEEDSOURCE_ID = (select fs.FEEDSOURCE_ID from FEED_SOURCE fs where fs.source = 'HRIS EXTRACTED') and PATH = 'appointmentDetails.appointmentDetail.employeeGroupText'

update FIELD_DEF set label = 'Current Rank', PATH = 'extractedFields.currentRank', PARENTFIELDDEF_ID = (select fd.FIELDDEF_ID from FIELD_DEF fd where fd.PATH = 'extractedFields') where feedsource_id = (select fs.FEEDSOURCE_ID from FEED_SOURCE fs where fs.source = 'HRIS EXTRACTED') and PATH = 'education.careerProgress.facultyRankText'

update FIELD_DEF set label = 'Is Emeritus', PATH = 'extractedFields.isEmeritus', FIELDDATATYPE_ID = 10, PARENTFIELDDEF_ID = (select fd.FIELDDEF_ID from FIELD_DEF fd where fd.PATH = 'extractedFields') where FEEDSOURCE_ID = (select fs.FEEDSOURCE_ID from FEED_SOURCE fs where fs.source = 'HRIS EXTRACTED') and PATH = 'otherUnivAppts.otherUnivAppt.emeritusOrEmeritaRank'

update FIELD_DEF set PATH = 'extractedFields.cpsoNumber', PARENTFIELDDEF_ID = (select fd.FIELDDEF_ID from FIELD_DEF fd where fd.PATH = 'extractedFields') where FEEDSOURCE_ID = (select fs.FEEDSOURCE_ID from FEED_SOURCE fs where fs.source = 'HRIS EXTRACTED') and PATH = 'otherUnivAppts.otherUnivAppt.cpsoNumber'

update FIELD_DEF set PATH = 'extractedFields.isStaff', FEEDSOURCE_ID = (select fs.FEEDSOURCE_ID from FEED_SOURCE fs where fs.source = 'HRIS EXTRACTED'), PARENTFIELDDEF_ID = (select fd.FIELDDEF_ID from FIELD_DEF fd where fd.PATH = 'extractedFields') where FEEDSOURCE_ID = (select fs.FEEDSOURCE_ID from FEED_SOURCE fs where fs.source = 'HRIS DERIVED') and PATH = 'formula:isStaff+appointmentDetails.persSubArea'


/******  Extracted Fields data for profile ******/

alter table profile add EXTRACTEDFIELDS_ID numeric(19, 0) 
GO

SET IDENTITY_INSERT EXTRACTED_FIELDS ON

insert into EXTRACTED_FIELDS(EXTRACTEDFIELD_ID) select PROFILE_ID from PROFILE

SET IDENTITY_INSERT EXTRACTED_FIELDS OFF 

update PROFILE set EXTRACTEDFIELDS_ID = PROFILE_ID

ALTER TABLE [dbo].[PROFILE]  WITH CHECK ADD  CONSTRAINT [FK__PROFILE_EXTRACTEDFIELDS] FOREIGN KEY([EXTRACTEDFIELDS_ID])
REFERENCES [dbo].[EXTRACTED_FIELDS] ([EXTRACTEDFIELD_ID])
GO

ALTER TABLE [dbo].[PROFILE] CHECK CONSTRAINT [FK__PROFILE_EXTRACTEDFIELDS]
GO

alter table profile alter column EXTRACTEDFIELDS_ID numeric(19, 0) NOT NULL 
GO

/******  update dynamic group criteria table for nested static groups ******/

update DYNAMIC_GROUP_CRIT set SEARCHABLE_FIELDDEF_ID = (select fielddef_id from FIELD_DEF where path = 'staticGroups.staticGroup.name'), value = (select STATICGROUP_ID from STATIC_GROUP where name = 'Executive Committee') where DYNAMICGROUPCRIT_ID = 438

update DYNAMIC_GROUP_CRIT set SEARCHABLE_FIELDDEF_ID = (select fielddef_id from FIELD_DEF where path = 'staticGroups.staticGroup.name'), value = (select STATICGROUP_ID from STATIC_GROUP where name = 'Static-Emeritus') where DYNAMICGROUPCRIT_ID = 432


/******   End of script ******/

