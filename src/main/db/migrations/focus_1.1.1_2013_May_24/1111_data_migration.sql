-- Rename field def labels
update field_def set label = 'Data Integrity Rules' where label = 'HRIS Rule Events' and path = 'hrisRuleEventList.hrisRuleEvent';
update field_def set label = 'Appointment Type Rule' where label = 'Appointment Type' and path = 'hrisRuleEventList.hrisRuleEvent.appointmentType';
update field_def set label = 'Data Integrity Flagged Value' where label = 'Flagged Value' and path = 'hrisRuleEventList.hrisRuleEvent.flaggedValue';
update field_def set label = 'Data Integrity Flagged Reason' where label = 'Reason' and path = 'hrisRuleEventList.hrisRuleEvent.reason';
update field_def set label = 'Static Group' where path = 'staticGroups.staticGroup';
--
-- BEGIN: MAKE STATIC GROUPS AVAILABLE TO DISPLAY
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'staticGroups.staticGroup' and a.token = 'DISPLAY';
-- END
--
-- INITIAL DEFAULT VALUE FOR EXTRACTED FIELD IMPORT_DATE
update EXTRACTED_FIELDS set IMPORT_DATE='2013-05-01' where EXTRACTEDFIELD_ID in (SELECT p.EXTRACTEDFIELDS_ID FROM PROFILE p WHERE p.FEEDSOURCE_ID=1)
--
-- ADD DEFINITION FOR inHrisFeed EXTRACTED FIELD 
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is In HRIS Feed', 'extractedFields.inHrisFeed', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd 
    where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields';
--
-- BEGIN: ADD FIELDDEF APPLICATIONS ENTRIES FOR inHrisFeed EXTRACTED FIELDDEF
-- MAKE AVAILABLE TO ADVANCED SEARCH AS TOP LEVEL (OWN PARENT)
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'extractedFields.inHrisFeed' 
    and a.token = 'SEARCH_PARENT';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'extractedFields.inHrisFeed' 
    and a.token = 'SEARCH_CHILD';
-- MAKE AVAILABLE TO REPORT FIELD SELECTION
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'extractedFields.inHrisFeed' 
    and a.token = 'REPORT_PARENT';
-- END: ADD FIELDDEF APPLICATIONS ENTRIES FOR NEW EXTRACTED FIELDDEFS
