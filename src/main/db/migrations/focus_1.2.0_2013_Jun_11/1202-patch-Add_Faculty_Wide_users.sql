
-- Users
insert into users (USER<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LASTNAME, <PERSON><PERSON><PERSON>OR<PERSON>, SALT, <PERSON>MAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, <PERSON><PERSON><PERSON><PERSON>, deleted, failedLoginAttempts, CREATEDBY, CREATE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, UPDATEDB<PERSON>, UPDA<PERSON><PERSON><PERSON>, DEPARTMENT_ID) select 'verducij', 'verducij', 'Julie', '', 'Verduci', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name = 'Virtual All Departments';
insert into users (USERNA<PERSON>, UTORID, F<PERSON>STNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, <PERSON>NABLED, deleted, failedLoginAttempts, CREA<PERSON><PERSON>B<PERSON>, CREA<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>D<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DEPARTMENT_ID) select 'balosina', 'balosina', '<PERSON>na', '', 'Balosin', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name = 'Virtual All Departments';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'qq069232', 'qq069232', 'Court', '', 'FWU', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name = 'Virtual All Departments';

-- Group members
insert into group_members select u.users_id, g.group_id from users u, groups g where g.name = 'Faculty Administrators' and u.username = 'verducij';
insert into group_members select u.users_id, g.group_id from users u, groups g where g.name = 'Faculty Administrators' and u.username = 'balosina';
insert into group_members select u.users_id, g.group_id from users u, groups g where g.name = 'Faculty Administrators' and u.username = 'qq069232';

 