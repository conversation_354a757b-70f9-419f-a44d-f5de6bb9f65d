-- Focus Insight Integration
    create table WEBC<PERSON>_BRIDGE (
        W<PERSON>BCVBRI<PERSON>GE_ID numeric(19,0) identity not null,
        CREATED_ON datetime null,
        WEBCV_ID numeric(19,0) not null unique,
        PROFILE_ID numeric(19,0) not null,
        primary key (W<PERSON><PERSON><PERSON><PERSON>D<PERSON>_ID),
        unique (PROFILE_ID)
    );
--
create index IDX_WEBCV_BRIDGE_PROFILE on WEBCV_BRIDGE (PROFILE_ID);
--
alter table WEBCV_BRIDGE 
    add constraint FK_WEBCV_BRIDGE_PROFILE 
    foreign key (PROFILE_ID) 
    references PROFILE;
--       
/****** Object:  Table [dbo].[ADDRESS_TYPE_TEXT_ALIAS]    Script Date: 2013-06-20 3:10:07 PM ******/
SET ANSI_NULLS ON
GO
--
SET QUOTED_IDENTIFIER ON
GO
--
SET ANSI_PADDING ON
GO
--
CREATE TABLE [dbo].[ADDRESS_TYPE_TEXT_ALIAS](
	[ADDRESSTYPETEXTALIAS_ID] [numeric](19, 0) IDENTITY(1,1) NOT NULL,
	[ADDRESS_TYPE] [varchar](255) NOT NULL,
	[ADDRESS_TYPE_TEXT_ALIAS] [varchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[ADDRESSTYPETEXTALIAS_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
UNIQUE NONCLUSTERED 
(
	[ADDRESS_TYPE] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
--
GO
--
SET ANSI_PADDING OFF
GO
-- 
/****** Object:  Table [dbo].[DEPARTMENT_SETTINGS]    Script Date: 2013-06-20 3:10:07 PM ******/
SET ANSI_NULLS ON
GO
--
SET QUOTED_IDENTIFIER ON
GO
--
SET ANSI_PADDING ON
GO
--
CREATE TABLE [dbo].[DEPARTMENT_SETTINGS](
    DEPARTMENTSETTINGS_ID [numeric](19, 0) IDENTITY(1,1) NOT NULL,
    DEPARTMENT_ID [numeric](19, 0) NOT NULL,
    LOGIN_GROUP_ID [numeric](19, 0) NULL,
    LOGIN_GROUP_TYPE [numeric](19, 0) NULL,
    CREATEDBY varchar(255) null,
    CREATEDON datetime null,
    DELETED tinyint null,
    OPTLOCK numeric(19,0) null,
    UPDATEDBY varchar(255) null,
    UPDATEDON datetime null,
    PRIMARY KEY CLUSTERED (
        [DEPARTMENTSETTINGS_ID] ASC
    )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
        UNIQUE NONCLUSTERED (
            [DEPARTMENT_ID] ASC
        )WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
--
GO
--
SET ANSI_PADDING OFF
GO
--
create index IDX__DEPARTMENT_SETTINGS__DEPARTMENT_ID on DEPARTMENT_SETTINGS (DEPARTMENT_ID);
--
alter table DEPARTMENT_SETTINGS 
    add constraint FK__DEPARTMENT_SETTINGS__DEPARTMENT 
    foreign key (DEPARTMENT_ID) 
    references DEPARTMENT;
--
/****** Add Primary Appointment End Date to EXTRACTED_FIELDS ***********************/
ALTER TABLE EXTRACTED_FIELDS
	ADD APPOINTMENT_END_DATE datetime NULL
	DEFAULT NULL
GO

/****** Add Primary Appointment APPOINTMENT_SUBTYPE_TEXT (Primary Academic Affiliation) to EXTRACTED_FIELDS ***********************/
ALTER TABLE EXTRACTED_FIELDS
	ADD APPOINTMENT_SUBTYPE_TEXT varchar(255) NULL
	DEFAULT NULL
GO
