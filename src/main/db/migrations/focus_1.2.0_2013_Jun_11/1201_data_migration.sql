-- ADD DEFINITION FOR inHrisFeed EXTRACTED FIELD 
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Is Flagged', 'extractedFields.flagged', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, 10 from FEED_SOURCE fs, field_def fd 
    where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields';
--
-- BEGIN: ADD FIELDDEF APPLICATIONS ENTRIES FOR inHrisFeed EXTRACTED FIELDDEF
-- MAKE AVAILABLE TO ADVANCED SEARCH AS TOP LEVEL (OWN PARENT)
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'extractedFields.flagged' 
    and a.token = 'SEARCH_PARENT';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'extractedFields.flagged' 
    and a.token = 'SEARCH_CHILD';
-- MAKE AVAILABLE TO REPORT FIELD SELECTION
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'extractedFields.flagged' 
    and a.token = 'REPORT_PARENT';
-- END: ADD FIELDDEF APPLICATIONS ENTRIES FOR NEW EXTRACTED FIELDDEFS
--
-- RENAME Dynamic Groups and Static Groups
update field_def set label = 'Group' where path = 'dynamicGroups.dynamicGroup.name';
update field_def set label = 'List' where path = 'staticGroups.staticGroup.name';

-- Mailing Label Field
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, fielddatatype_id, parentfielddef_id, selectable, non_searchable) select 'Mailing Label', 'mailing+addresses.address', 0, 0, 0, fs.feedsource_id, fdt.fielddatatype_id, null, 1, 0 from FEED_SOURCE fs, FIELD_DATA_TYPE fdt where fs.SOURCE = 'PRIMARY_COMMUNICAITON_FIELD' and fdt.DATATYPE='Address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'AddressLine1', 'mailing+addresses.address.addressTypeText', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'mailing+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'AddressLine2', 'mailing+addresses.address.streetAndHouseNumber', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'mailing+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'AddressLine3', 'mailing+addresses.address.secondAddressLine', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'mailing+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'City', 'mailing+addresses.address.city', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'mailing+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Province', 'mailing+addresses.address.province', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'mailing+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Postal Code', 'mailing+addresses.address.postalCode', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'mailing+addresses.address'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) select 'Country', 'mailing+addresses.address.country', 0, 0, 1, feedsource_id, fielddef_id, 0, 0, 1 from field_def where path = 'mailing+addresses.address'

insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'mailing+addresses.address' 
    and a.token = 'REPORT_PARENT';

-- data for address type text alias
insert into ADDRESS_TYPE_TEXT_ALIAS (ADDRESS_TYPE, ADDRESS_TYPE_TEXT_ALIAS) values ('H01', 'Toronto General Hospital');
insert into ADDRESS_TYPE_TEXT_ALIAS (ADDRESS_TYPE, ADDRESS_TYPE_TEXT_ALIAS) values ('H02', 'Toronto Western Hospital');
insert into ADDRESS_TYPE_TEXT_ALIAS (ADDRESS_TYPE, ADDRESS_TYPE_TEXT_ALIAS) values ('H31', 'Trillium Health Ctr - Mississauga');
insert into ADDRESS_TYPE_TEXT_ALIAS (ADDRESS_TYPE, ADDRESS_TYPE_TEXT_ALIAS) values ('H32', 'Trillium Health Ctr - Queensway');


--- orgstructure changes Migrations

-- Groups
insert into groups (name) values ('Faculty Administrators');

-- Authorities
insert into authority (authority)  values ('ROLE_FACULTY_ADMINISTRATOR');
insert into authority (authority)  values ('ROLE_EMAIL_ACCESS_PERMISSION');

-- Group Authorities - Faculty Administrators

insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Faculty Administrators' and authority = 'ROLE_FACULTY_ADMINISTRATOR';
--insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Faculty Administrators' and authority = 'ROLE_CREATE_DEPARTMENT_ADMINISTRATOR';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Faculty Administrators' and authority = 'ROLE_DEPARTMENT_ADMINISTRATOR';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Faculty Administrators' and authority = 'ROLE_VIEW_STAFF';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Faculty Administrators' and authority = 'ROLE_EDIT_DATA';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Faculty Administrators' and authority = 'ROLE_VIEW_FACULTY';

-- ROLE_EMAIL_ACCESS_PERMISSION Authority for existing groups
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'DC System Administrators' and authority = 'ROLE_EMAIL_ACCESS_PERMISSION';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Department Administrators' and authority = 'ROLE_EMAIL_ACCESS_PERMISSION';
insert into group_authority (group_id, authority_id) select g.group_id, authority_id from groups g, authority where g.name = 'Users' and authority = 'ROLE_EMAIL_ACCESS_PERMISSION';


-- Department - insert dept key
insert into DEPARTMENT (NAME, DEPT_KEY, DELETED, OPTLOCK) VALUES ('Virtual All Departments', 'VIRTUAL_ALL_DEPT', 0, 0);

-- Users
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'facultyadmin1', 'facultyadmin1', 'Faculty', '', 'Administrator', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name = 'Virtual All Departments';

-- Group members
insert into group_members select u.users_id, g.group_id from users u, groups g where g.name = 'Faculty Administrators' and u.username = 'facultyadmin1';

-- Actions
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Actions', 'actions.action', 0, 0, 0, fs.feedsource_id, null, null from FEED_SOURCE fs
    where fs.source = 'HRIS';
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
	select 'Start Date', 'actions.action.startDate', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID from field_def fd, FEED_SOURCE fs, FIELD_DATA_TYPE fdt 
	where fd.path = 'actions.action' and fs.source = 'HRIS' and fdt.datatype='Date'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
	select 'End Date', 'actions.action.endDate', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID from field_def fd, FEED_SOURCE fs, FIELD_DATA_TYPE fdt 
	where fd.path = 'actions.action' and fs.source = 'HRIS' and fdt.datatype='Date'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
	select 'Changed On', 'actions.action.changedOn', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID from field_def fd, FEED_SOURCE fs, FIELD_DATA_TYPE fdt 
	where fd.path = 'actions.action' and fs.source = 'HRIS' and fdt.datatype='Date'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
	select 'Action Type', 'actions.action.actionTypeText', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, 0, 0, fdt.FIELDDATATYPE_ID from field_def fd, FEED_SOURCE fs, FIELD_DATA_TYPE fdt 
	where fd.path = 'actions.action' and fs.source = 'HRIS' and fdt.datatype='Text'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
	select 'Reason for Action', 'actions.action.reasonForActionText', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID from field_def fd, FEED_SOURCE fs, FIELD_DATA_TYPE fdt 
	where fd.path = 'actions.action' and fs.source = 'HRIS' and fdt.datatype='Text'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
	select 'Employment Status', 'actions.action.employmentStatusText', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID from field_def fd, FEED_SOURCE fs, FIELD_DATA_TYPE fdt 
	where fd.path = 'actions.action' and fs.source = 'HRIS' and fdt.datatype='Text'

insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'actions.action' 
    and a.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'actions.action' 
    and a.token = 'REPORT_PARENT';
    
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'actions.action' and a.token = 'SEARCH_PARENT';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'actions.action.startDate' and a.token = 'SEARCH_CHILD';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'actions.action.endDate' and a.token = 'SEARCH_CHILD';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'actions.action.changedOn' and a.token = 'SEARCH_CHILD';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'actions.action.actionTypeText' and a.token = 'SEARCH_CHILD';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'actions.action.reasonforAction' and a.token = 'SEARCH_CHILD';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'actions.action.employmentStatusText' and a.token = 'SEARCH_CHILD';
--end of Actions


