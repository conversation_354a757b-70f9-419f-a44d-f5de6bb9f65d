    create table ACTION (
        ACTION_ID numeric(19,0) identity not null,
        CREATED<PERSON><PERSON> varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDB<PERSON> varchar(255) null,
        UPDATEDON datetime null,
        actionType numeric(19,0) null,
        actionTypeText varchar(255) null,
        changedOn datetime null,
        employmentStatus numeric(19,0) null,
        employmentStatusText varchar(255) null,
        endDate datetime null,
        reasonForAction numeric(19,0) null,
        reasonForActionText varchar(255) null,
        recordNumber numeric(19,0) null,
        startDate datetime null,
        PROFILE_ID numeric(19,0) not null,
        primary key (ACTION_ID)
    );

    create table ADDRESS (
        ADDRESS_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDB<PERSON> varchar(255) null,
        UPDATEDON datetime null,
        addressType varchar(255) null,
        addressTypeText varchar(255) null,
        changedOn datetime null,
        city varchar(255) null,
        country varchar(255) null,
        endDate datetime null,
        postalCode varchar(255) null,
        province varchar(255) null,
        RECORD_NUMBER numeric(19,0) not null,
        secondAddressLine varchar(255) null,
        startDate datetime null,
        streetAndHouseNumber varchar(255) null,
        telephoneNumber varchar(255) null,
        CUSTOMFIELD_ID numeric(19,0) null,
        PROFILE_ID numeric(19,0) not null,
        primary key (ADDRESS_ID)
    );

    create table APPOINTMENTDETAILS (
        APPOINTMENTDETAILS_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        changedOn datetime null,
        companyCode varchar(255) null,
        costCenter numeric(19,0) null,
        employeeGroup varchar(255) null,
        employeeGroupText varchar(255) null,
        employeeSubGroup varchar(255) null,
        employeeSubGroupText varchar(255) null,
        endDate datetime null,
        fund varchar(255) null,
        fundCenter numeric(19,0) null,
        job varchar(255) null,
        jobText varchar(255) null,
        APTDETAILS_ORDER varchar(255) null,
        orgUnit varchar(255) null,
        orgUnitText varchar(255) null,
        payrollArea varchar(255) null,
        payrollAreaText varchar(255) null,
        percentage varchar(255) null,
        persSubArea varchar(255) null,
        persSubAreaText varchar(255) null,
        personnelArea varchar(255) null,
        personnelAreaText varchar(255) null,
        position varchar(255) null,
        positionText varchar(255) null,
        RECORD_NUMBER numeric(19,0) not null,
        startDate datetime null,
        workContract varchar(255) null,
        workContractText varchar(255) null,
        PROFILE_ID numeric(19,0) not null,
        primary key (APPOINTMENTDETAILS_ID)
    );

    create table AUDIT_LOG (
        AUDIT_LOG_ID numeric(19,0) identity not null,
        ACTION nvarchar(10) not null,
        CREATED datetime not null,
        ENTITY_CLASS varchar(255) not null,
        ENTITY_ID numeric(19,0) not null,
        NEW_VALUE nvarchar(255) null,
        OLD_VALUE nvarchar(255) null,
        PROPERTY nvarchar(255) null,
        USERNAME varchar(255) not null,
        primary key (AUDIT_LOG_ID)
    );

    create table AUTHORITY (
        AUTHORITY_ID numeric(19,0) identity not null,
        AUTHORITY varchar(255) not null unique,
        primary key (AUTHORITY_ID)
    );

    create table AWARD (
        AWARD_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        awardHonour numeric(19,0) null,
        awardHonourName varchar(255) null,
        changedOn datetime null,
        endDate datetime null,
        RECORD_NUMBER numeric(19,0) not null,
        startDate datetime null,
        yearConferred numeric(19,0) null,
        EDUCATION_ID numeric(19,0) not null,
        primary key (AWARD_ID)
    );

    create table BASICPAY (
        BASICPAY_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        changedOn datetime null,
        endDate datetime null,
        payScaleArea varchar(255) null,
        payScaleAreaText varchar(255) null,
        payScaleGroup varchar(255) null,
        payScaleLevel varchar(255) null,
        payScaleType varchar(255) null,
        payScaleTypeText varchar(255) null,
        RECORD_NUMBER numeric(19,0) not null,
        startDate datetime null,
        wageType varchar(255) null,
        wageTypeText varchar(255) null,
        PROFILE_ID numeric(19,0) not null,
        primary key (BASICPAY_ID)
    );

    create table BOOK (
        BOOK_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        ISBN varchar(255) null unique,
        AUTHOR varchar(255) null,
        DATE_PUBLISHED datetime null,
        PUBLISHER varchar(255) null,
        TITLE varchar(255) null unique,
        primary key (BOOK_ID)
    );

    create table CAREERPROGRESS (
        CAREERPROGRESS_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        adjunctVisit varchar(255) null,
        adjunctVisitDescription varchar(255) null,
        changedOn datetime null,
        endDate datetime null,
        facultyRank varchar(255) null,
        facultyRankDate datetime null,
        facultyRankText varchar(255) null,
        RECORD_NUMBER numeric(19,0) not null,
        startDate datetime null,
        tenure varchar(255) null,
        tenureEffectiveDate datetime null,
        tenureReviewDate datetime null,
        tenureText varchar(255) null,
        EDUCATION_ID numeric(19,0) not null,
        primary key (CAREERPROGRESS_ID)
    );

    create table COMMPREF (
        COMMPREF_ID numeric(19,0) identity not null,
        ADDRESS_ID numeric(19,0) null,
        ADDRESS_TYPE varchar(255) null,
        EMAIL_ID numeric(19,0) null,
        EMAIL_TYPE varchar(255) null,
        TELEPHONE_ID numeric(19,0) null,
        TELEPHONE_TYPE varchar(255) null,
        DEPARTMENT_ID numeric(19,0) not null,
        PROFILE_ID numeric(19,0) not null,
        primary key (COMMPREF_ID)
    );

    create table CONTAINER_DEF (
        CONTAINERDEF_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        LOCATION_COLUMN varchar(255) null,
        DISPLAY_ORDER int null,
        NAME varchar(255) null,
        DEPARTMENT_ID numeric(19,0) not null,
        primary key (CONTAINERDEF_ID)
    );

    create table CONTAINER_TAB (
        CONTAINERTAB_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        DISPLAY_ORDER int not null,
        NAME varchar(255) not null,
        CONTAINER_ID numeric(19,0) not null,
        primary key (CONTAINERTAB_ID)
    );

    create table CONTRACTELEMENT (
        CONTRACTELEMENT_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        changedOn datetime null,
        contractType varchar(255) null,
        contractTypeText varchar(255) null,
        endDate datetime null,
        RECORD_NUMBER numeric(19,0) not null,
        startDate datetime null,
        validUntil datetime null,
        PROFILE_ID numeric(19,0) not null,
        primary key (CONTRACTELEMENT_ID)
    );

    create table CUSTOM_AUTHORITY (
        USERS_ID numeric(19,0) not null,
        AUTHORITY_ID numeric(19,0) not null,
        primary key (USERS_ID, AUTHORITY_ID)
    );

    create table DEPARTMENT (
        DEPARTMENT_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        DEPT_KEY varchar(255) not null unique,
        NAME varchar(255) not null unique,
        primary key (DEPARTMENT_ID)
    );

    create table DEPT_ADDRESS_PREF (
        DEPARTMENT_ID numeric(19,0) not null,
        ADDRESS_ID numeric(19,0) not null,
        primary key (DEPARTMENT_ID, ADDRESS_ID)
    );

    create table DEPT_EMAIL_PREF (
        DEPARTMENT_ID numeric(19,0) not null,
        EMAIL_ID numeric(19,0) not null,
        primary key (DEPARTMENT_ID, EMAIL_ID)
    );

    create table DEPT_EMAIL_SETTING (
        DEPARTMENTEMAILSETTING_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        DISPLAY_NAME varchar(255) not null,
        EMAIL_ADDRESS varchar(255) null,
        SIGNATURE varchar(255) null,
        DEPARTMENT_ID numeric(19,0) not null,
        primary key (DEPARTMENTEMAILSETTING_ID),
        unique (DEPARTMENT_ID)
    );

    create table DEPT_PHONE_PREF (
        DEPARTMENT_ID numeric(19,0) not null,
        TELEPHONE_ID numeric(19,0) not null,
        primary key (DEPARTMENT_ID, TELEPHONE_ID)
    );

    create table EDUCATION (
        EDUCATION_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        PROFILE_ID numeric(19,0) not null,
        primary key (EDUCATION_ID),
        unique (PROFILE_ID)
    );

    create table EMAIL (
        EMAIL_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        changedOn datetime null,
        emailAddress varchar(255) null,
        endDate datetime null,
        RECORD_NUMBER numeric(19,0) not null,
        startDate datetime null,
        CUSTOMFIELD_ID numeric(19,0) null,
        PROFILE_ID numeric(19,0) not null,
        primary key (EMAIL_ID)
    );

    create table EMAIL_FIELD (
        EMAILFIELD_ID numeric(19,0) identity not null,
        DISPLAY_ORDER int not null,
        EMAILTEMPLATE_ID numeric(19,0) not null,
        FIELDDEF_ID numeric(19,0) not null,
        primary key (EMAILFIELD_ID),
        unique (EMAILFIELD_ID, FIELDDEF_ID, DISPLAY_ORDER)
    );

    create table EMAIL_RECORD (
        EMAILRECORD_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        CC varchar(255) null,
        EMAIL varchar(8000) null,
        FROMADDRESS varchar(255) null,
        SUBJECT varchar(255) null,
        TOADDRESS varchar(255) null,
        PROFILE_ID numeric(19,0) null,
        SENDER_ID numeric(19,0) null,
        primary key (EMAILRECORD_ID)
    );

    create table EMAIL_SETTING (
        EMAILSETTING_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        DISPLAY_NAME varchar(255) not null,
        EMAIL_ADDRESS varchar(255) null,
        SIGNATURE varchar(255) null,
        USERS_ID numeric(19,0) not null,
        DEPARTMENT_ID numeric(19,0) null,
        primary key (EMAILSETTING_ID)
    );

    create table EMAIL_TEMPLATE (
        EMAILTEMPLATE_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        TEMPLATE_NAME varchar(255) not null,
        EMAIL_BODY varchar(8000) not null,
        DEPARTMENT_ID numeric(19,0) null,
        USERS_ID numeric(19,0) not null,
        primary key (EMAILTEMPLATE_ID)
    );

    create table FEED (
        FEED_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        DURATION numeric(19,0) null,
        IMPORTDATE datetime not null,
        PROFILE_COUNT numeric(19,0) not null,
        STATUS varchar(255) not null,
        FEEDSOURCE_ID numeric(19,0) not null,
        primary key (FEED_ID)
    );

    create table FEED_ERROR (
        FEEDERROR_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        ERROR_TEXT NVARCHAR(512) not null,
        SEQUENCE_NO numeric(19,0) not null,
        FEED_ID numeric(19,0) not null,
        primary key (FEEDERROR_ID),
        unique (FEED_ID, SEQUENCE_NO)
    );

    create table FEED_FIELD_PERMISSION (
        FEEDFIELPERM_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        PERMISSION numeric(19,0) not null,
        DEPARTMENT_ID numeric(19,0) not null,
        FIELDDEF_ID numeric(19,0) not null,
        primary key (FEEDFIELPERM_ID)
    );

    create table FEED_SOURCE (
        FEEDSOURCE_ID numeric(19,0) identity not null,
        DESCRIPTION varchar(255) null,
        SOURCE varchar(255) not null unique,
        primary key (FEEDSOURCE_ID)
    );

    create table FIELD_DATA_TYPE (
        FIELDDATATYPE_ID numeric(19,0) identity not null,
        DESCRIPTION varchar(255) null,
        DATATYPE varchar(255) not null unique,
        primary key (FIELDDATATYPE_ID)
    );

    create table FIELD_DEF (
        FIELDDEF_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        RECORD_TYPE varchar(255) null,
        DISPLAY_ORDER int null,
        EXTRA_INFO varchar(255) null,
        LABEL varchar(255) not null,
        PATH varchar(255) null,
        RESTRICTION_CODE int null,
        selectable tinyint null,
        DEPARTMENT_ID numeric(19,0) null,
        FEEDSOURCE_ID numeric(19,0) not null,
        FIELDDATATYPE_ID numeric(19,0) null,
        PARENTFIELDDEF_ID numeric(19,0) null,
        primary key (FIELDDEF_ID),
        unique (PATH, DEPARTMENT_ID, FEEDSOURCE_ID),
        unique (DEPARTMENT_ID, LABEL, PATH)
    );

    create table FIELD_VALUE (
        FIELDVALUE_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        VALUE_DATE datetime null,
        VALUE_LONG numeric(19,0) null,
        VALUE_STRING varchar(255) null,
        VALUE_TEXTAREA varchar(8000) null,
        FIELDDEF_ID numeric(19,0) not null,
        PARENTFIELDVAL_ID numeric(19,0) null,
        PROFILE_ID numeric(19,0) not null,
        primary key (FIELDVALUE_ID),
        unique (FIELDDEF_ID, PROFILE_ID)
    );

    create table GRADAPPT (
        GRADAPPT_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        changedOn datetime null,
        endDate datetime null,
        graduateUnit varchar(255) null,
        graduateUnitText varchar(255) null,
        primaryGradAppointIndicator varchar(255) null,
        RECORD_NUMBER numeric(19,0) not null,
        restrictionIndicator varchar(255) null,
        startDate datetime null,
        supervisoryLevel varchar(255) null,
        supervisoryLevelText varchar(255) null,
        EDUCATION_ID numeric(19,0) not null,
        primary key (GRADAPPT_ID)
    );

    create table GROUPS (
        GROUP_ID numeric(19,0) identity not null,
        NAME varchar(255) not null unique,
        primary key (GROUP_ID)
    );

    create table GROUP_AUTHORITY (
        GROUP_ID numeric(19,0) not null,
        AUTHORITY_ID numeric(19,0) not null,
        primary key (GROUP_ID, AUTHORITY_ID)
    );

    create table GROUP_MEMBERS (
        USERS_ID numeric(19,0) not null,
        GROUP_ID numeric(19,0) not null,
        primary key (USERS_ID, GROUP_ID)
    );

    create table NOTE (
        NOTE_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        AUTHOR_NAME varchar(255) null,
        NOTE varchar(8000) not null,
        TITLE varchar(255) not null,
        DEPARTMENT_ID numeric(19,0) null,
        PROFILE_ID numeric(19,0) null,
        primary key (NOTE_ID)
    );

    create table OPERATOR_DATATYPE (
        PROFILEGROUPCRITOP_ID numeric(19,0) not null,
        FIELDDATATYPE_ID numeric(19,0) not null,
        primary key (PROFILEGROUPCRITOP_ID, FIELDDATATYPE_ID)
    );

    create table OTHERUNIVERSITYAPPOINTMENT (
        OTHERUNIVERSITYAPPOINTMENT_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        academicRank varchar(255) null,
        academicRankText varchar(255) null,
        appointmentType varchar(255) null,
        appointmentTypeText varchar(255) null,
        careerPathTitle varchar(255) null,
        changedOn datetime null,
        clinContractStatus varchar(255) null,
        clinContractStatusText varchar(255) null,
        cpsoNumber numeric(19,0) null,
        emeritusOrEmeritaRank varchar(255) null,
        endDate datetime null,
        hospitalAddress varchar(255) null,
        hospitalCode varchar(255) null,
        mainIndicator varchar(255) null,
        organizationUnit varchar(255) null,
        organizationUnitText varchar(255) null,
        RECORD_NUMBER numeric(19,0) not null,
        startDate datetime null,
        subtype varchar(255) null,
        subtypeText varchar(255) null,
        PROFILE_ID numeric(19,0) not null,
        primary key (OTHERUNIVERSITYAPPOINTMENT_ID)
    );

    create table PERSONALDATA (
        PERSONALDATA_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        birthDate datetime null,
        birthName varchar(255) null,
        changedOn datetime null,
        designation varchar(255) null,
        endDate datetime null,
        firstName varchar(255) null,
        formOfAddress varchar(255) null,
        fullName varchar(255) null,
        gender varchar(255) null,
        knownAs varchar(255) null,
        lastName varchar(255) null,
        middleInitial varchar(255) null,
        middleName varchar(255) null,
        nationality varchar(255) null,
        personnelNumber numeric(19,0) null,
        RECORD_NUMBER numeric(19,0) not null,
        startDate datetime null,
        primary key (PERSONALDATA_ID)
    );

    create table POSTSECED (
        POSTSECED_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        changedOn datetime null,
        educationCode numeric(19,0) null,
        educationCodeText varchar(255) null,
        endDate datetime null,
        instution varchar(255) null,
        instutionText varchar(255) null,
        RECORD_NUMBER numeric(19,0) not null,
        sourceDocument varchar(255) null,
        sourceDocumentText varchar(255) null,
        startDate datetime null,
        verified varchar(255) null,
        yearConfirmed numeric(19,0) null,
        EDUCATION_ID numeric(19,0) not null,
        primary key (POSTSECED_ID)
    );

    create table PROFDESIGNATION (
        PROFDESIGNATION_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        changedOn datetime null,
        designationCode varchar(255) null,
        designationCodeText varchar(255) null,
        endDate datetime null,
        RECORD_NUMBER numeric(19,0) not null,
        startDate datetime null,
        yearConferred numeric(19,0) null,
        EDUCATION_ID numeric(19,0) not null,
        primary key (PROFDESIGNATION_ID)
    );

    create table PROFILE (
        PROFILE_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        PERSONALDATA_ID numeric(19,0) not null,
        FEEDSOURCE_ID numeric(19,0) not null,
        primary key (PROFILE_ID),
        unique (PERSONALDATA_ID)
    );

    create table PROFILEGROUP_PROFILE (
        PROFILEGROUP_ID numeric(19,0) not null,
        PROFILE_ID numeric(19,0) not null,
        primary key (PROFILEGROUP_ID, PROFILE_ID)
    );

    create table PROFILE_GROUP (
        PROFILEGROUP_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        DEPT_VISIBILITY int not null,
        NAME varchar(255) not null,
        PROFILEGROUP_TYPE int not null,
        AUTHUSER_ID numeric(19,0) not null,
        DEPARTMENT_ID numeric(19,0) not null,
        primary key (PROFILEGROUP_ID),
        unique (AUTHUSER_ID, NAME)
    );

    create table PROFILE_GROUP_CRIT (
        PROFILEGROUPCRIT_ID numeric(19,0) identity not null,
        VALUE varchar(255) null,
        PROFILEGROUPCRITOP_ID numeric(19,0) null,
        PROFILEGROUP_ID numeric(19,0) null,
        SEARCHABLE_FIELDDEF_ID numeric(19,0) null,
        primary key (PROFILEGROUPCRIT_ID)
    );

    create table PROFILE_GROUP_CRIT_OP (
        PROFILEGROUPCRITOP_ID numeric(19,0) identity not null,
        DESCRIPTION varchar(255) null,
        DISPLAY_WEIGHT int null,
        HTML_ENTITY varchar(255) null,
        RESTRICTION_OP_NAME varchar(255) null,
        primary key (PROFILEGROUPCRITOP_ID)
    );

    create table PROFILE_TAG (
        TAG_ID numeric(19,0) not null,
        PROFILE_ID numeric(19,0) not null,
        primary key (TAG_ID, PROFILE_ID),
        unique (PROFILE_ID, TAG_ID)
    );

    create table REPORT_FIELD (
        REPORTFIELD_ID numeric(19,0) identity not null,
        DISPLAY_ORDER int not null,
        FIELDDEF_ID numeric(19,0) null,
        REPORTTEMPLATE_ID numeric(19,0) null,
        primary key (REPORTFIELD_ID),
        unique (REPORTFIELD_ID, FIELDDEF_ID, DISPLAY_ORDER)
    );

    create table REPORT_TEMPLATE (
        REPORTTEMPLATE_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        TEMPLATE_NAME varchar(255) not null,
        DEPARTMENT_ID numeric(19,0) null,
        USERS_ID numeric(19,0) not null,
        primary key (REPORTTEMPLATE_ID)
    );

    create table TAB_FIELD (
        TABFIELD_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        DISPLAY_ORDER int not null,
        FIELDDEF_ID numeric(19,0) not null,
        CONTAINERTAB_ID numeric(19,0) not null,
        primary key (TABFIELD_ID),
        unique (CONTAINERTAB_ID, FIELDDEF_ID)
    );

    create table TAG (
        TAG_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        NAME varchar(255) not null,
        USERS_ID numeric(19,0) not null,
        primary key (TAG_ID),
        unique (NAME, USERS_ID)
    );

    create table TELEPHONE (
        TELEPHONE_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        areaCode varchar(255) null,
        changedOn datetime null,
        endDate datetime null,
        extension varchar(255) null,
        longDistance tinyint null,
        phoneNumber varchar(255) null,
        phoneType varchar(255) null,
        startDate datetime null,
        telPrimary tinyint null,
        telecomOrg varchar(255) null,
        CUSTOMFIELD_ID numeric(19,0) null,
        PROFILE_ID numeric(19,0) not null,
        primary key (TELEPHONE_ID)
    );

    create table USERS (
        users_id numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        accountNonExpired tinyint not null,
        accountNonLocked tinyint not null,
        credentialsNonExpired tinyint not null,
        email varchar(255) null,
        enabled tinyint not null,
        failedLoginAttempts int not null,
        firstName varchar(255) null,
        lastName varchar(255) null,
        middleName varchar(255) null,
        password varchar(255) not null,
        salt varchar(255) null,
        userSessionId numeric(19,0) null,
        username varchar(255) not null unique,
        UTORID varchar(255) null unique,
        DEPARTMENT_ID numeric(19,0) not null,
        primary key (users_id)
    );

    create table USER_CONTAINERDEF (
        users_id numeric(19,0) not null,
        CONTAINERDEF_ID numeric(19,0) not null,
        VISIBLE TINYINT DEFAULT 1 not null,
        primary key (users_id, CONTAINERDEF_ID)
    );

    create table USER_EMAIL_SETTING (
        USEREMAILSETTING_ID numeric(19,0) identity not null,
        CREATEDBY varchar(255) null,
        CREATEDON datetime null,
        DELETED tinyint null,
        OPTLOCK numeric(19,0) null,
        UPDATEDBY varchar(255) null,
        UPDATEDON datetime null,
        DISPLAY_NAME varchar(255) not null,
        EMAIL_ADDRESS varchar(255) null,
        SIGNATURE varchar(255) null,
        USERS_ID numeric(19,0) not null,
        primary key (USEREMAILSETTING_ID),
        unique (USERS_ID)
    );

    create table USER_SESSION (
        USERSESSION_ID numeric(19,0) identity not null,
        IP varchar(255) null,
        LOGIN_DATE datetime not null,
        LOGOUT_DATE datetime null,
        USERNAME varchar(255) not null,
        primary key (USERSESSION_ID)
    );

    create index IDX__ACTION__PROFILE_ID on ACTION (PROFILE_ID);

    alter table ACTION 
        add constraint FK__ACTION__PROFILE 
        foreign key (PROFILE_ID) 
        references PROFILE;

    create index IDX_ADDRESS__FIELDDEF_ID on ADDRESS (CUSTOMFIELD_ID);

    create index IDX_ADDRESS__PROFILE_ID on ADDRESS (PROFILE_ID);

    alter table ADDRESS 
        add constraint FK__ADDRESS__FIELDDEF 
        foreign key (CUSTOMFIELD_ID) 
        references FIELD_DEF;

    alter table ADDRESS 
        add constraint FK__ADDRESS__PROFILE 
        foreign key (PROFILE_ID) 
        references PROFILE;

    create index IDX__APPOINTMENTDETAILS__PROFILE_ID on APPOINTMENTDETAILS (PROFILE_ID);

    alter table APPOINTMENTDETAILS 
        add constraint FK__APPOINTMENTDETAILS__PROFILE 
        foreign key (PROFILE_ID) 
        references PROFILE;

    create index IDX__AWARD__EDUCATION_ID on AWARD (EDUCATION_ID);

    alter table AWARD 
        add constraint FK__AWARD__EDUCATION 
        foreign key (EDUCATION_ID) 
        references EDUCATION;

    create index IDX__BASICPAY__PROFILE_ID on BASICPAY (PROFILE_ID);

    alter table BASICPAY 
        add constraint FK__BASICPAY__PROFILE 
        foreign key (PROFILE_ID) 
        references PROFILE;

    create index IDX_EDUCATION_ID on CAREERPROGRESS (EDUCATION_ID);

    alter table CAREERPROGRESS 
        add constraint FK_CAREERPROGRESS__EDUCATION 
        foreign key (EDUCATION_ID) 
        references EDUCATION;

    create index IDX__COMMPREF__PROFILE on COMMPREF (PROFILE_ID);

    create index IDX__COMMPREF__DEPARTMENT on COMMPREF (DEPARTMENT_ID);

    alter table COMMPREF 
        add constraint FK__COMMPREF__DEPARTMENT 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table COMMPREF 
        add constraint FK__COMMPREF__PROFILE 
        foreign key (PROFILE_ID) 
        references PROFILE;

    alter table CONTAINER_DEF 
        add constraint FK2F0E7B0714C4C3A3 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    create index IDX__CONTAINER_DEF__CONTAINER_ID on CONTAINER_TAB (CONTAINER_ID);

    alter table CONTAINER_TAB 
        add constraint FK__CONTAINER_TAB__CONTAINER_DEF 
        foreign key (CONTAINER_ID) 
        references CONTAINER_DEF;

    create index IDX__PROFILE__PROFILE_ID on CONTRACTELEMENT (PROFILE_ID);

    alter table CONTRACTELEMENT 
        add constraint FK__CONTRACTELEMENT__PROFILE 
        foreign key (PROFILE_ID) 
        references PROFILE;

    alter table CUSTOM_AUTHORITY 
        add constraint FK2AD6B7D56AF71291 
        foreign key (AUTHORITY_ID) 
        references AUTHORITY;

    alter table CUSTOM_AUTHORITY 
        add constraint FK2AD6B7D512D691AE 
        foreign key (USERS_ID) 
        references USERS;

    alter table DEPT_ADDRESS_PREF 
        add constraint FK__ADDRESS__ADDRESS_ID 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table DEPT_ADDRESS_PREF 
        add constraint FK__ADDRESS__DEPARTMENT_ID 
        foreign key (ADDRESS_ID) 
        references ADDRESS;

    alter table DEPT_EMAIL_PREF 
        add constraint FK__EMAIL__EMAIL_ID 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table DEPT_EMAIL_PREF 
        add constraint FK__EMAIL__DEPARTMENT_ID 
        foreign key (EMAIL_ID) 
        references EMAIL;

    create index IDX__DEPT_EMAIL_SETTING__DEPARTMENT_ID on DEPT_EMAIL_SETTING (DEPARTMENT_ID);

    alter table DEPT_EMAIL_SETTING 
        add constraint FK__DEPT_EMAIL_SETTING__DEPARTMENT 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table DEPT_PHONE_PREF 
        add constraint FK__TELEPHONE__TELEPHONE_ID 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table DEPT_PHONE_PREF 
        add constraint FK__TELEPHONE__DEPARTMENT_ID 
        foreign key (TELEPHONE_ID) 
        references TELEPHONE;

    create index IDX_PROFILE_PROFILE_ID on EDUCATION (PROFILE_ID);

    alter table EDUCATION 
        add constraint FK_EDUCATION__PROFILE 
        foreign key (PROFILE_ID) 
        references PROFILE;

    create index IDX_PROFILE_ID on EMAIL (PROFILE_ID);

    create index IDX_EMAIL__FIELDDEF_ID on EMAIL (CUSTOMFIELD_ID);

    alter table EMAIL 
        add constraint FK__EMAIL__FIELDDEF 
        foreign key (CUSTOMFIELD_ID) 
        references FIELD_DEF;

    alter table EMAIL 
        add constraint FK_EMAIL__PROFILE 
        foreign key (PROFILE_ID) 
        references PROFILE;

    create index IDX__EMAIL_FIELD__EMAILTEMPLATE_ID on EMAIL_FIELD (EMAILTEMPLATE_ID);

    create index IDX__EMAIL_FIELD__FIELDDEF_ID on EMAIL_FIELD (FIELDDEF_ID);

    alter table EMAIL_FIELD 
        add constraint FK__EMAIL_FIELD__FIELD_DEF 
        foreign key (FIELDDEF_ID) 
        references FIELD_DEF;

    alter table EMAIL_FIELD 
        add constraint FK__EMAIL_FIELD__EMAIL_TEMPLATE 
        foreign key (EMAILTEMPLATE_ID) 
        references EMAIL_TEMPLATE;

    create index IDX_EMAIL_RECORD__PROFILE_ID on EMAIL_RECORD (PROFILE_ID);

    create index IDX_EMAIL_RECORD__AUTHUSER_ID on EMAIL_RECORD (SENDER_ID);

    alter table EMAIL_RECORD 
        add constraint FK_EMAIL_RECORD__AUTHUSER 
        foreign key (SENDER_ID) 
        references USERS;

    alter table EMAIL_RECORD 
        add constraint FK_EMAIL_RECORD__PROFILE 
        foreign key (PROFILE_ID) 
        references PROFILE;

    create index IDX__EMAIL_SETTING__USERS_ID on EMAIL_SETTING (USERS_ID);

    create index IDX__EMAIL_SETTING__DEPARTMENT_ID on EMAIL_SETTING (DEPARTMENT_ID);

    alter table EMAIL_SETTING 
        add constraint FK__EMAIL_SETTING__DEPARTMENT 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table EMAIL_SETTING 
        add constraint FK__EMAIL_SETTING__USERS 
        foreign key (USERS_ID) 
        references USERS;

    alter table EMAIL_TEMPLATE 
        add constraint FK7295C5DD14C4C3A3 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table EMAIL_TEMPLATE 
        add constraint FK_EMAIL__USERS_ID 
        foreign key (USERS_ID) 
        references USERS;

    alter table FEED 
        add constraint FK20DD9E8DE0D263 
        foreign key (FEEDSOURCE_ID) 
        references FEED_SOURCE;

    create index IDX__FEED_ERROR__FEED_ID on FEED_ERROR (FEED_ID);

    alter table FEED_ERROR 
        add constraint FK__FEED_ERROR__FEED 
        foreign key (FEED_ID) 
        references FEED;

    create index IDX__FEED_FIELD_PERMISSION__DEPARTMENT_ID on FEED_FIELD_PERMISSION (DEPARTMENT_ID);

    create index IDX__FEED_FIELD_PERMISSION__FIELDDEF_ID on FEED_FIELD_PERMISSION (FIELDDEF_ID);

    alter table FEED_FIELD_PERMISSION 
        add constraint FK__FEED_FIELD_PERMISSION__FIELD_DEF 
        foreign key (FIELDDEF_ID) 
        references FIELD_DEF;

    alter table FEED_FIELD_PERMISSION 
        add constraint FK__FEED_FIELD_PERMISSION__DEPARTMENT 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    create index IDX_FIELD_DEF__DEPARTMENT_ID on FIELD_DEF (DEPARTMENT_ID);

    create index IDX_FIELD_DEF__FIELDDATATYPE_ID on FIELD_DEF (FIELDDATATYPE_ID);

    create index IDX_FIELD_DEF__PARENTFIELDDEF_ID on FIELD_DEF (PARENTFIELDDEF_ID);

    create index IDX_FIELD_DEF__FEEDSOURCE_ID on FIELD_DEF (FEEDSOURCE_ID);

    alter table FIELD_DEF 
        add constraint FK_FIELD_DEF__FEED_SOURCE 
        foreign key (FEEDSOURCE_ID) 
        references FEED_SOURCE;

    alter table FIELD_DEF 
        add constraint FK_FIELD_DEF__DEPARTMENT 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table FIELD_DEF 
        add constraint FK_FIELD_DEF__FIELD_DEF 
        foreign key (PARENTFIELDDEF_ID) 
        references FIELD_DEF;

    alter table FIELD_DEF 
        add constraint FK_FIELD_DEF__FIELD_DATA_TYPE 
        foreign key (FIELDDATATYPE_ID) 
        references FIELD_DATA_TYPE;

    create index IDX_FIELD_VALUE__FIELDDEF_ID on FIELD_VALUE (FIELDDEF_ID);

    create index IDX_FIELD_VALUE__PARENTFIELDVAL_ID on FIELD_VALUE (PARENTFIELDVAL_ID);

    create index IDX_FIELD_VALUE__PROFILE_ID on FIELD_VALUE (PROFILE_ID);

    alter table FIELD_VALUE 
        add constraint FK_FIELD_VALUE__FIELDDEF_ID 
        foreign key (FIELDDEF_ID) 
        references FIELD_DEF;

    alter table FIELD_VALUE 
        add constraint FK_FIELD_VALUE__FIELD_VALUE 
        foreign key (PARENTFIELDVAL_ID) 
        references FIELD_VALUE;

    alter table FIELD_VALUE 
        add constraint FK_FIELD_VALUE__PROFILE_ID 
        foreign key (PROFILE_ID) 
        references PROFILE;

    create index IDX_EDUCATION_ID on GRADAPPT (EDUCATION_ID);

    alter table GRADAPPT 
        add constraint FK_GRADAPPT__EDUCATION 
        foreign key (EDUCATION_ID) 
        references EDUCATION;

    alter table GROUP_AUTHORITY 
        add constraint FK9A14E5436AF71291 
        foreign key (AUTHORITY_ID) 
        references AUTHORITY;

    alter table GROUP_AUTHORITY 
        add constraint FK9A14E5438FC86C91 
        foreign key (GROUP_ID) 
        references GROUPS;

    alter table GROUP_MEMBERS 
        add constraint FKF1802B198FC86C91 
        foreign key (GROUP_ID) 
        references GROUPS;

    alter table GROUP_MEMBERS 
        add constraint FKF1802B1912D691AE 
        foreign key (USERS_ID) 
        references USERS;

    create index IDX_NOTE__DEPARTMENT on NOTE (DEPARTMENT_ID);

    create index IDX_PROFILE__NOTE on NOTE (PROFILE_ID);

    alter table NOTE 
        add constraint FK_NOTE__DEPARTMENT_ID 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table NOTE 
        add constraint FK_NOTE__PROFILE_ID 
        foreign key (PROFILE_ID) 
        references PROFILE;

    alter table OPERATOR_DATATYPE 
        add constraint FKD9941B1F82E98DCF 
        foreign key (PROFILEGROUPCRITOP_ID) 
        references PROFILE_GROUP_CRIT_OP;

    alter table OPERATOR_DATATYPE 
        add constraint FKD9941B1F196C0D71 
        foreign key (FIELDDATATYPE_ID) 
        references FIELD_DATA_TYPE;

    create index IDX__OTHERUNIVERSITYAPPOINTMENT__PROFILE_ID on OTHERUNIVERSITYAPPOINTMENT (PROFILE_ID);

    alter table OTHERUNIVERSITYAPPOINTMENT 
        add constraint FK_OTHERUNIVERSITYAPPOINTMENT__PROFILE 
        foreign key (PROFILE_ID) 
        references PROFILE;

    create index IDX__POSTSECED__EDUCATION_ID on POSTSECED (EDUCATION_ID);

    alter table POSTSECED 
        add constraint FK__POSTSECED__EDUCATION 
        foreign key (EDUCATION_ID) 
        references EDUCATION;

    create index IDX__PROFDESIGNATION__EDUCATION_ID on PROFDESIGNATION (EDUCATION_ID);

    alter table PROFDESIGNATION 
        add constraint FK__PROFDESIGNATION__EDUCATION 
        foreign key (EDUCATION_ID) 
        references EDUCATION;

    create index IDX__PROFILE__PERSONALDATA_ID on PROFILE (PERSONALDATA_ID);

    create index IDX__PROFILE_FEEDSOURCE_ID on PROFILE (FEEDSOURCE_ID);

    alter table PROFILE 
        add constraint FK__PROFILE_FEEDSOURCE 
        foreign key (FEEDSOURCE_ID) 
        references FEED_SOURCE;

    alter table PROFILE 
        add constraint FK__PROFILE_PERSONALDATA 
        foreign key (PERSONALDATA_ID) 
        references PERSONALDATA;

    alter table PROFILEGROUP_PROFILE 
        add constraint FK__PROFILE__PROFILE_ID 
        foreign key (PROFILEGROUP_ID) 
        references PROFILE_GROUP;

    alter table PROFILEGROUP_PROFILE 
        add constraint FK__PROFILE_GROUP__PROFILEGROUP_ID 
        foreign key (PROFILE_ID) 
        references PROFILE;

    create index IDX__PROFILE_GROUP__USERS on PROFILE_GROUP (AUTHUSER_ID);

    create index IDX__PROFILE_GROUP__DEPARTMENT_ID on PROFILE_GROUP (DEPARTMENT_ID);

    alter table PROFILE_GROUP 
        add constraint FK__PROFILE_GROUPT__DEPARTMENT 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table PROFILE_GROUP 
        add constraint FK__PROFILE_GROUP__USERS 
        foreign key (AUTHUSER_ID) 
        references USERS;

    alter table PROFILE_GROUP_CRIT 
        add constraint FK67FD3030784C2AC3 
        foreign key (PROFILEGROUP_ID) 
        references PROFILE_GROUP;

    alter table PROFILE_GROUP_CRIT 
        add constraint FK67FD303082E98DCF 
        foreign key (PROFILEGROUPCRITOP_ID) 
        references PROFILE_GROUP_CRIT_OP;

    alter table PROFILE_GROUP_CRIT 
        add constraint FK67FD30306DF8B566 
        foreign key (SEARCHABLE_FIELDDEF_ID) 
        references FIELD_DEF;

    alter table PROFILE_TAG 
        add constraint FK__PROFILE__TAG 
        foreign key (PROFILE_ID) 
        references PROFILE;

    alter table PROFILE_TAG 
        add constraint FK__TAG__PROFILE 
        foreign key (TAG_ID) 
        references TAG;

    alter table REPORT_FIELD 
        add constraint FKF8619E0FC77BE323 
        foreign key (FIELDDEF_ID) 
        references FIELD_DEF;

    alter table REPORT_FIELD 
        add constraint FKF8619E0F748CFDC3 
        foreign key (REPORTTEMPLATE_ID) 
        references REPORT_TEMPLATE;

    alter table REPORT_TEMPLATE 
        add constraint FK5073BF2514C4C3A3 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table REPORT_TEMPLATE 
        add constraint FK_REPORT__USERS_ID 
        foreign key (USERS_ID) 
        references USERS;

    alter table TAB_FIELD 
        add constraint FK50152390C77BE323 
        foreign key (FIELDDEF_ID) 
        references FIELD_DEF;

    alter table TAB_FIELD 
        add constraint FK50152390E1D51A03 
        foreign key (CONTAINERTAB_ID) 
        references CONTAINER_TAB;

    create index IDX__TAG__USERS_ID on TAG (USERS_ID);

    alter table TAG 
        add constraint FK_TAG__USERS 
        foreign key (USERS_ID) 
        references USERS;

    create index IDX_PROFILE_ID on TELEPHONE (PROFILE_ID);

    create index IDX_TELEPHONE__FIELDDEF_ID on TELEPHONE (CUSTOMFIELD_ID);

    alter table TELEPHONE 
        add constraint FK__TELEPHONE__FIELDDEF 
        foreign key (CUSTOMFIELD_ID) 
        references FIELD_DEF;

    alter table TELEPHONE 
        add constraint FK_TELEPHONE__PROFILE 
        foreign key (PROFILE_ID) 
        references PROFILE;

    alter table USERS 
        add constraint FK4D495E814C4C3A3 
        foreign key (DEPARTMENT_ID) 
        references DEPARTMENT;

    alter table USER_CONTAINERDEF 
        add constraint FK8458BD8C6C18403 
        foreign key (CONTAINERDEF_ID) 
        references CONTAINER_DEF;

    alter table USER_CONTAINERDEF 
        add constraint FK8458BD812D691AE 
        foreign key (users_id) 
        references USERS;

    create index IDX__USER_EMAIL_SETTING__USERS_ID on USER_EMAIL_SETTING (USERS_ID);

    alter table USER_EMAIL_SETTING 
        add constraint FK__USER_EMAIL_SETTING__USERS 
        foreign key (USERS_ID) 
        references USERS;
