-- ADD DEFINITION FOR hrisChangeLog FIELD 
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, fielddatatype_id) 
    select 'HRIS Change Log', 'hrisChangeLogEvents.hrisChangeLogEvent', 0, 0, 0, fs.feedsource_id, 1 from FEED_SOURCE fs
    where fs.source = 'HRIS';
-- ADD "DISPLAY" APPLICATION FOR hrisChangeLog FIELD
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'hrisChangeLogEvents.hrisChangeLogEvent' 
    and a.token = 'DISPLAY';

-- ADD "DISPLAY" APPLICATION FOR Is Flagged FIELD
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.flagged')
    and fa.token = 'DISPLAY';
 -----------------------------------Adding Smarth Fields data--------------------------------------------------  
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id)
    select 'Focus ID', 'extractedFields.focusId', 0, 0, 0, fs.feedsource_id, parent.fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, FIELD_DEF parent, FIELD_DATA_TYPE fdt
    where fs.source = 'HRIS EXTRACTED' and parent.path = 'extractedFields' and fdt.DATATYPE = 'Number';
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.focusId')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.focusId')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.focusId')
    and fa.token = 'SEARCH_CHILD';
--
-- Create FieldDef entries for changedOn fields
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Changed On', 'appointmentDetails.appointmentDetail.changedOn', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID from field_def fd, FEED_SOURCE fs, FIELD_DATA_TYPE fdt 
    where fd.path = 'appointmentDetails.appointmentDetail' and fs.source = 'HRIS' and fdt.datatype='Date'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Changed On', 'basicPay.basicPay.changedOn', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID from field_def fd, FEED_SOURCE fs, FIELD_DATA_TYPE fdt 
    where fd.path = 'basicPay.basicPay' and fs.source = 'HRIS' and fdt.datatype='Date'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Changed On', 'education.careerProgress.changedOn', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID from field_def fd, FEED_SOURCE fs, FIELD_DATA_TYPE fdt 
    where fd.path = 'education.careerProgress' and fs.source = 'HRIS' and fdt.datatype='Date'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Changed On', 'education.gradAppt.changedOn', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID from field_def fd, FEED_SOURCE fs, FIELD_DATA_TYPE fdt 
    where fd.path = 'education.gradAppt' and fs.source = 'HRIS' and fdt.datatype='Date'
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, fielddatatype_id) 
    select 'Changed On', 'otherUnivAppts.otherUnivAppt.changedOn', 0, 0, 0, fs.feedsource_id, fd.fielddef_id, fdt.FIELDDATATYPE_ID from field_def fd, FEED_SOURCE fs, FIELD_DATA_TYPE fdt 
    where fd.path = 'otherUnivAppts.otherUnivAppt' and fs.source = 'HRIS' and fdt.datatype='Date'
--
-- Add FD_APPLICATIONS entries for changedOn fields
--
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'appointmentDetails.appointmentDetail.changedOn' and a.token = 'SEARCH_CHILD';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'basicPay.basicPay.changedOn' and a.token = 'SEARCH_CHILD';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'education.careerProgress.changedOn' and a.token = 'SEARCH_CHILD';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'education.gradAppt.changedOn' and a.token = 'SEARCH_CHILD';
insert into FIELDDEFS_FDAPPLICATIONS (FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, a.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION a
    where fd.path = 'otherUnivAppts.otherUnivAppt.changedOn' and a.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentPosition')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentPosition')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentPosition')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentPosition')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentPosition')
    and fa.token = 'EMAIL';
    
-----------------update Label values on smart fields-----------------------
update field_def set label = 'Primary Appt Start Date' where path = 'extractedFields.appointmentStartDate'
update field_def set label = 'Position' where path = 'extractedFields.appointmentPosition'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.focusId')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.focusId')
    and fa.token = 'EMAIL';
 
 ------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id)
    select 'Career Path Title', 'extractedFields.careerPathTitle', 0, 0, 0, fs.feedsource_id, parent.fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, FIELD_DEF parent, FIELD_DATA_TYPE fdt
    where fs.source = 'HRIS EXTRACTED' and parent.path = 'extractedFields' and fdt.DATATYPE = 'Text';
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.careerPathTitle')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.careerPathTitle')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.careerPathTitle')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.careerPathTitle')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.careerPathTitle')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Hospital Affiliation End Date', 'extractedFields.hospitalAffiliationEndDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Date'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.hospitalAffiliationEndDate')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.hospitalAffiliationEndDate')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.hospitalAffiliationEndDate')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.hospitalAffiliationEndDate')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.hospitalAffiliationEndDate')
    and fa.token = 'EMAIL';

------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Primary Appt End Date', 'extractedFields.appointmentEndDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Date'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentEndDate')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentEndDate')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentEndDate')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentEndDate')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentEndDate')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Pay Scale Group', 'extractedFields.payScaleGroup', 1, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleGroup')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleGroup')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleGroup')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleGroup')
    and fa.token = 'REPORT_PARENT';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Pay Scale Level', 'extractedFields.payScaleLevel', 1, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleLevel')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleLevel')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleLevel')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleLevel')
    and fa.token = 'REPORT_PARENT';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Pay Scale Type', 'extractedFields.payScaleType', 1, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleType')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleType')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleType')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.payScaleType')
    and fa.token = 'REPORT_PARENT';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Wage Type', 'extractedFields.wageTypeText', 1, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.wageTypeText')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.wageTypeText')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.wageTypeText')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.wageTypeText')
    and fa.token = 'REPORT_PARENT';

------------------------------------------------------------------------------------------------

insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Current Action Start Date', 'extractedFields.currentActionStartDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Date'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionStartDate')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionStartDate')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionStartDate')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionStartDate')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionStartDate')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Current Action End Date', 'extractedFields.currentActionEndDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Date'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionEndDate')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionEndDate')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionEndDate')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionEndDate')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionEndDate')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Current Action Type', 'extractedFields.currentActionTypeText', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionTypeText')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionTypeText')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionTypeText')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionTypeText')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentActionTypeText')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Current Action Reason', 'extractedFields.currentReasonForAction', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentReasonForAction')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentReasonForAction')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentReasonForAction')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentReasonForAction')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentReasonForAction')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Current Employment Status', 'extractedFields.currentEmployeeStatusText', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentEmployeeStatusText')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentEmployeeStatusText')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentEmployeeStatusText')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentEmployeeStatusText')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.currentEmployeeStatusText')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Previous Rank', 'extractedFields.previousFacultyRank', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFacultyRank')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFacultyRank')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFacultyRank')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFacultyRank')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFacultyRank')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Previous Rank Start Date', 'extractedFields.previousFRStartDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Date'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFRStartDate')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFRStartDate')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFRStartDate')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFRStartDate')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.previousFRStartDate')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Primary Grad Appt Unit', 'extractedFields.primaryGradApptUnitText', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.primaryGradApptUnitText')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.primaryGradApptUnitText')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.primaryGradApptUnitText')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.primaryGradApptUnitText')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.primaryGradApptUnitText')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Primary Grad Appt End Date', 'extractedFields.primaryGradApptEndDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Date'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.primaryGradApptEndDate')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.primaryGradApptEndDate')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.primaryGradApptEndDate')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.primaryGradApptEndDate')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.primaryGradApptEndDate')
    and fa.token = 'EMAIL';

------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Personnel Area', 'extractedFields.personnelArea', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.personnelArea')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.personnelArea')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.personnelArea')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.personnelArea')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.personnelArea')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Personnel Subarea', 'extractedFields.personnelSubArea', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.personnelSubArea')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.personnelSubArea')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.personnelSubArea')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.personnelSubArea')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.personnelSubArea')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Position #', 'extractedFields.positionNumber', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.positionNumber')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.positionNumber')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.positionNumber')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.positionNumber')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.positionNumber')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Home Org', 'extractedFields.homeOrgUnit', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Text'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.homeOrgUnit')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.homeOrgUnit')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.homeOrgUnit')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.homeOrgUnit')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.homeOrgUnit')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Latest Hire Date', 'extractedFields.latestHireDate', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Date'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.latestHireDate')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.latestHireDate')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.latestHireDate')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.latestHireDate')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.latestHireDate')
    and fa.token = 'EMAIL';
------------------------------------------------------------------------------------------------
insert into field_def (label, path, restriction_code, deleted, optlock, feedsource_id, parentfielddef_id, selectable, non_searchable, fielddatatype_id) 
    select 'Appointment Percentage', 'extractedFields.appointmentPercentage', 0, 0, 0, fs.feedsource_id, fielddef_id, 1, 0, fdt.FIELDDATATYPE_ID from FEED_SOURCE fs, field_def fd, FIELD_DATA_TYPE fdt where fs.source = 'HRIS EXTRACTED' and fd.path = 'extractedFields' and fdt.DATATYPE = 'Real'
    
insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentPercentage')
    and fa.token = 'DISPLAY';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentPercentage')
    and fa.token = 'SEARCH_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentPercentage')
    and fa.token = 'SEARCH_CHILD';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentPercentage')
    and fa.token = 'REPORT_PARENT';

insert into FIELDDEFS_FDAPPLICATIONS(FIELDDEF_ID, FDAPPLICATION_ID)
    select fd.FIELDDEF_ID, fa.FDAPPLICATION_ID FROM FIELD_DEF fd, FD_APPLICATION fa
    where fd.path in ('extractedFields.appointmentPercentage')
    and fa.token = 'EMAIL';
--
-- Prune Audit Log table --
delete from AUDIT_LOG where profile_id is null;    
