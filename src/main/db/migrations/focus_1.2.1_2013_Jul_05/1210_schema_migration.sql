-- Alter table audit_log for profile_id and fielddef_id
--
ALTER TABLE AUDIT_LOG
	ADD PROFILE_ID numeric(19, 0) NULL;

alter table EXTRACTED_FIELDS add FOCUS_ID numeric(19,0) null;
alter table EXTRACTED_FIELDS add CAREER_PATH_TITLE varchar(255) null;
alter table EXTRACTED_FIELDS add HOSPITAL_AFFILIATION_END_DATE datetime NULL;
alter table EXTRACTED_FIELDS add  PAY_SCALE_GROUP varchar(255) NULL;
alter table EXTRACTED_FIELDS add  PAY_SCALE_LEVEL varchar(255) NULL;
alter table EXTRACTED_FIELDS add  PAY_SCALE_TYPE varchar(255) NULL;
alter table EXTRACTED_FIELDS add  WAGE_TYPE_TEXT varchar(255) NULL;
alter table EXTRACTED_FIELDS add  CURRENT_ACTION_START_DATE date NULL;
alter table EXTRACTED_FIELDS add  CURRENT_ACTION_END_DATE date NULL;
alter table EXTRACTED_FIELDS add CURRENT_ACTION_TYPE_TEXT varchar(255) NULL;
alter table EXTRACTED_FIELDS add CURRENT_REASON_FOR_ACTION varchar(255) NULL;
alter table EXTRACTED_FIELDS add CURRENT_EMPLOYEE_STATUS_TEXT varchar(255) NULL;
alter table EXTRACTED_FIELDS add PREVIOUS_FACULTY_RANK varchar(255) NULL;
alter table EXTRACTED_FIELDS add PREVIOUS_FACULTY_RANK_START_DATE date NULL;
alter table EXTRACTED_FIELDS add PRIMARY_GRAD_APPT_UNIT varchar(255) NULL;
alter table EXTRACTED_FIELDS add PRIMARY_GRAD_APPT_END_DATE date NULL;
alter table EXTRACTED_FIELDS add PERSONNEL_AREA varchar(255) NULL;
alter table EXTRACTED_FIELDS add PERSONNEL_SUB_AREA varchar(255) NULL;
alter table EXTRACTED_FIELDS add POSITION_NUMBER varchar(255) NULL;
alter table EXTRACTED_FIELDS add HOME_ORG_UNIT varchar(255) NULL;
alter table EXTRACTED_FIELDS add LATEST_HIRE_DATE date NULL;
alter table EXTRACTED_FIELDS add APPOINTMENT_PERCENTAGE varchar(255) NULL;

alter table HRIS_RULE_EVENT add FIELDDEF_ID numeric(19,0) null;
create index IDX_RULEEVENT__FIELDDEF_ID on HRIS_RULE_EVENT (FIELDDEF_ID);
alter table HRIS_RULE_EVENT 
        add constraint FK__RULEEVENT__FIELDDEF 
        foreign key (FIELDDEF_ID) 
        references FIELD_DEF;
        
-- Audit Log tables     

/****** Object:  Table [dbo].[REVISIONS_INFO]    Script Date: 2013-07-31 2:32:18 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[REVISIONS_INFO](
	[revision_id] [int] IDENTITY(1,1) NOT NULL,
	[revision_timestamp] [datetime] NULL,
	[revision_createdby] [varchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[revision_id] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

/****** Object:  Table [dbo].[REVISIONS_ENTITY_INFO]    Script Date: 2013-07-31 2:31:44 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[REVISIONS_ENTITY_INFO](
	[ENTITY_ID] [int] IDENTITY(1,1) NOT NULL,
	[ENTITY_CLASSNAME] [varchar](255) NULL,
	[REVISION_ID] [int] NULL,
PRIMARY KEY CLUSTERED 
(
	[ENTITY_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[REVISIONS_ENTITY_INFO]  WITH CHECK ADD  CONSTRAINT [FKA04661A32DAC2419] FOREIGN KEY([REVISION_ID])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[REVISIONS_ENTITY_INFO] CHECK CONSTRAINT [FKA04661A32DAC2419]
GO

/****** Object:  Table [dbo].[ACTION_AUD]    Script Date: 2013-07-31 2:24:52 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[ACTION_AUD](
	[ACTION_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[actionType] [numeric](19, 0) NULL,
	[actionTypeText] [varchar](255) NULL,
	[changedOn] [datetime] NULL,
	[employmentStatus] [numeric](19, 0) NULL,
	[employmentStatusText] [varchar](255) NULL,
	[endDate] [datetime] NULL,
	[reasonForAction] [numeric](19, 0) NULL,
	[reasonForActionText] [varchar](255) NULL,
	[recordNumber] [numeric](19, 0) NULL,
	[startDate] [datetime] NULL,
	[PROFILE_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[ACTION_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[ACTION_AUD]  WITH CHECK ADD  CONSTRAINT [FK8B5E9507F0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[ACTION_AUD] CHECK CONSTRAINT [FK8B5E9507F0CF4BD]
GO

/****** Object:  Table [dbo].[ADDRESS_AUD]    Script Date: 2013-07-31 2:26:08 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[ADDRESS_AUD](
	[ADDRESS_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[addressType] [varchar](255) NULL,
	[addressTypeText] [varchar](255) NULL,
	[changedOn] [datetime] NULL,
	[city] [varchar](255) NULL,
	[country] [varchar](255) NULL,
	[endDate] [datetime] NULL,
	[postalCode] [varchar](255) NULL,
	[province] [varchar](255) NULL,
	[RECORD_NUMBER] [numeric](19, 0) NULL,
	[secondAddressLine] [varchar](255) NULL,
	[startDate] [datetime] NULL,
	[streetAndHouseNumber] [varchar](255) NULL,
	[telephoneNumber] [varchar](255) NULL,
	[PROFILE_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[ADDRESS_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[ADDRESS_AUD]  WITH CHECK ADD  CONSTRAINT [FK2E4F4BA5F0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[ADDRESS_AUD] CHECK CONSTRAINT [FK2E4F4BA5F0CF4BD]
GO

/****** Object:  Table [dbo].[APPOINTMENTDETAILS_AUD]    Script Date: 2013-07-31 2:26:31 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[APPOINTMENTDETAILS_AUD](
	[APPOINTMENTDETAILS_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[changedOn] [datetime] NULL,
	[companyCode] [varchar](255) NULL,
	[costCenter] [numeric](19, 0) NULL,
	[employeeGroup] [varchar](255) NULL,
	[employeeGroupText] [varchar](255) NULL,
	[employeeSubGroup] [varchar](255) NULL,
	[employeeSubGroupText] [varchar](255) NULL,
	[endDate] [datetime] NULL,
	[fund] [varchar](255) NULL,
	[fundCenter] [numeric](19, 0) NULL,
	[job] [varchar](255) NULL,
	[jobText] [varchar](255) NULL,
	[APTDETAILS_ORDER] [varchar](255) NULL,
	[orgUnit] [varchar](255) NULL,
	[orgUnitText] [varchar](255) NULL,
	[payrollArea] [varchar](255) NULL,
	[payrollAreaText] [varchar](255) NULL,
	[percentage] [float] NULL,
	[persSubArea] [varchar](255) NULL,
	[persSubAreaText] [varchar](255) NULL,
	[personnelArea] [varchar](255) NULL,
	[personnelAreaText] [varchar](255) NULL,
	[position] [varchar](255) NULL,
	[positionText] [varchar](255) NULL,
	[RECORD_NUMBER] [numeric](19, 0) NULL,
	[startDate] [datetime] NULL,
	[workContract] [varchar](255) NULL,
	[workContractText] [varchar](255) NULL,
	[PROFILE_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[APPOINTMENTDETAILS_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[APPOINTMENTDETAILS_AUD]  WITH CHECK ADD  CONSTRAINT [FKB85D8614F0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[APPOINTMENTDETAILS_AUD] CHECK CONSTRAINT [FKB85D8614F0CF4BD]
GO

/****** Object:  Table [dbo].[AWARD_AUD]    Script Date: 2013-07-31 2:26:55 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[AWARD_AUD](
	[AWARD_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[awardHonour] [numeric](19, 0) NULL,
	[awardHonourName] [varchar](255) NULL,
	[changedOn] [datetime] NULL,
	[endDate] [datetime] NULL,
	[RECORD_NUMBER] [numeric](19, 0) NULL,
	[startDate] [datetime] NULL,
	[yearConferred] [numeric](19, 0) NULL,
	[EDUCATION_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[AWARD_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[AWARD_AUD]  WITH CHECK ADD  CONSTRAINT [FKF133498EF0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[AWARD_AUD] CHECK CONSTRAINT [FKF133498EF0CF4BD]
GO

/****** Object:  Table [dbo].[BASICPAY_AUD]    Script Date: 2013-07-31 2:27:15 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[BASICPAY_AUD](
	[BASICPAY_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[changedOn] [datetime] NULL,
	[endDate] [datetime] NULL,
	[payScaleArea] [varchar](255) NULL,
	[payScaleAreaText] [varchar](255) NULL,
	[payScaleGroup] [varchar](255) NULL,
	[payScaleLevel] [varchar](255) NULL,
	[payScaleType] [varchar](255) NULL,
	[payScaleTypeText] [varchar](255) NULL,
	[RECORD_NUMBER] [numeric](19, 0) NULL,
	[startDate] [datetime] NULL,
	[wageType] [varchar](255) NULL,
	[wageTypeText] [varchar](255) NULL,
	[PROFILE_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[BASICPAY_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[BASICPAY_AUD]  WITH CHECK ADD  CONSTRAINT [FK710FF68BF0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[BASICPAY_AUD] CHECK CONSTRAINT [FK710FF68BF0CF4BD]
GO

/****** Object:  Table [dbo].[CAREERPROGRESS_AUD]    Script Date: 2013-07-31 2:27:36 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[CAREERPROGRESS_AUD](
	[CAREERPROGRESS_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[adjunctVisit] [varchar](255) NULL,
	[adjunctVisitDescription] [varchar](255) NULL,
	[changedOn] [datetime] NULL,
	[endDate] [datetime] NULL,
	[facultyRank] [varchar](255) NULL,
	[facultyRankDate] [datetime] NULL,
	[facultyRankText] [varchar](255) NULL,
	[RECORD_NUMBER] [numeric](19, 0) NULL,
	[startDate] [datetime] NULL,
	[tenure] [varchar](255) NULL,
	[tenureEffectiveDate] [datetime] NULL,
	[tenureReviewDate] [datetime] NULL,
	[tenureText] [varchar](255) NULL,
	[EDUCATION_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[CAREERPROGRESS_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[CAREERPROGRESS_AUD]  WITH CHECK ADD  CONSTRAINT [FK61E3AFFCF0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[CAREERPROGRESS_AUD] CHECK CONSTRAINT [FK61E3AFFCF0CF4BD]
GO

/****** Object:  Table [dbo].[CONTRACTELEMENT_AUD]    Script Date: 2013-07-31 2:27:57 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[CONTRACTELEMENT_AUD](
	[CONTRACTELEMENT_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[changedOn] [datetime] NULL,
	[contractType] [varchar](255) NULL,
	[contractTypeText] [varchar](255) NULL,
	[endDate] [datetime] NULL,
	[RECORD_NUMBER] [numeric](19, 0) NULL,
	[startDate] [datetime] NULL,
	[validUntil] [datetime] NULL,
	[PROFILE_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[CONTRACTELEMENT_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[CONTRACTELEMENT_AUD]  WITH CHECK ADD  CONSTRAINT [FKD0FA0A5BF0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[CONTRACTELEMENT_AUD] CHECK CONSTRAINT [FKD0FA0A5BF0CF4BD]
GO

/****** Object:  Table [dbo].[EDUCATION_AUD]    Script Date: 2013-07-31 2:28:19 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[EDUCATION_AUD](
	[EDUCATION_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[PROFILE_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[EDUCATION_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE [dbo].[EDUCATION_AUD]  WITH CHECK ADD  CONSTRAINT [FKFD8A159F0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[EDUCATION_AUD] CHECK CONSTRAINT [FKFD8A159F0CF4BD]
GO

/****** Object:  Table [dbo].[EMAIL_AUD]    Script Date: 2013-07-31 2:28:38 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[EMAIL_AUD](
	[EMAIL_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[changedOn] [datetime] NULL,
	[emailAddress] [varchar](255) NULL,
	[endDate] [datetime] NULL,
	[RECORD_NUMBER] [numeric](19, 0) NULL,
	[startDate] [datetime] NULL,
	[PROFILE_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[EMAIL_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[EMAIL_AUD]  WITH CHECK ADD  CONSTRAINT [FK248E634DF0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[EMAIL_AUD] CHECK CONSTRAINT [FK248E634DF0CF4BD]
GO

/****** Object:  Table [dbo].[GRADAPPT_AUD]    Script Date: 2013-07-31 2:29:10 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[GRADAPPT_AUD](
	[GRADAPPT_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[changedOn] [datetime] NULL,
	[endDate] [datetime] NULL,
	[graduateUnit] [varchar](255) NULL,
	[graduateUnitText] [varchar](255) NULL,
	[primaryGradAppointIndicator] [varchar](255) NULL,
	[RECORD_NUMBER] [numeric](19, 0) NULL,
	[restrictionIndicator] [varchar](255) NULL,
	[startDate] [datetime] NULL,
	[supervisoryLevel] [varchar](255) NULL,
	[supervisoryLevelText] [varchar](255) NULL,
	[EDUCATION_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[GRADAPPT_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[GRADAPPT_AUD]  WITH CHECK ADD  CONSTRAINT [FKB1C7D312F0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[GRADAPPT_AUD] CHECK CONSTRAINT [FKB1C7D312F0CF4BD]
GO

/****** Object:  Table [dbo].[OTHERUNIVERSITYAPPOINTMENT_AUD]    Script Date: 2013-07-31 2:30:05 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[OTHERUNIVERSITYAPPOINTMENT_AUD](
	[OTHERUNIVERSITYAPPOINTMENT_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[academicRank] [varchar](255) NULL,
	[academicRankText] [varchar](255) NULL,
	[appointmentType] [varchar](255) NULL,
	[appointmentTypeText] [varchar](255) NULL,
	[careerPathTitle] [varchar](255) NULL,
	[changedOn] [datetime] NULL,
	[clinContractStatus] [varchar](255) NULL,
	[clinContractStatusText] [varchar](255) NULL,
	[cpsoNumber] [numeric](19, 0) NULL,
	[emeritusOrEmeritaRank] [varchar](255) NULL,
	[endDate] [datetime] NULL,
	[hospitalAddress] [varchar](255) NULL,
	[hospitalCode] [varchar](255) NULL,
	[mainIndicator] [varchar](255) NULL,
	[organizationUnit] [varchar](255) NULL,
	[organizationUnitText] [varchar](255) NULL,
	[RECORD_NUMBER] [numeric](19, 0) NULL,
	[startDate] [datetime] NULL,
	[subtype] [varchar](255) NULL,
	[subtypeText] [varchar](255) NULL,
	[PROFILE_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[OTHERUNIVERSITYAPPOINTMENT_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[OTHERUNIVERSITYAPPOINTMENT_AUD]  WITH CHECK ADD  CONSTRAINT [FKA832E892F0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[OTHERUNIVERSITYAPPOINTMENT_AUD] CHECK CONSTRAINT [FKA832E892F0CF4BD]
GO

/****** Object:  Table [dbo].[POSTSECED_AUD]    Script Date: 2013-07-31 2:30:42 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[POSTSECED_AUD](
	[POSTSECED_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[changedOn] [datetime] NULL,
	[educationCode] [numeric](19, 0) NULL,
	[educationCodeText] [varchar](255) NULL,
	[endDate] [datetime] NULL,
	[instution] [varchar](255) NULL,
	[instutionText] [varchar](255) NULL,
	[RECORD_NUMBER] [numeric](19, 0) NULL,
	[sourceDocument] [varchar](255) NULL,
	[sourceDocumentText] [varchar](255) NULL,
	[startDate] [datetime] NULL,
	[verified] [varchar](255) NULL,
	[yearConfirmed] [numeric](19, 0) NULL,
	[EDUCATION_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[POSTSECED_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[POSTSECED_AUD]  WITH CHECK ADD  CONSTRAINT [FKBACE89C1F0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[POSTSECED_AUD] CHECK CONSTRAINT [FKBACE89C1F0CF4BD]
GO

/****** Object:  Table [dbo].[PROFDESIGNATION_AUD]    Script Date: 2013-07-31 2:31:00 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[PROFDESIGNATION_AUD](
	[PROFDESIGNATION_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[changedOn] [datetime] NULL,
	[designationCode] [varchar](255) NULL,
	[designationCodeText] [varchar](255) NULL,
	[endDate] [datetime] NULL,
	[RECORD_NUMBER] [numeric](19, 0) NULL,
	[startDate] [datetime] NULL,
	[yearConferred] [numeric](19, 0) NULL,
	[EDUCATION_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[PROFDESIGNATION_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[PROFDESIGNATION_AUD]  WITH CHECK ADD  CONSTRAINT [FK297805AFF0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[PROFDESIGNATION_AUD] CHECK CONSTRAINT [FK297805AFF0CF4BD]
GO

/****** Object:  Table [dbo].[PROFILE_AUD]    Script Date: 2013-07-31 2:31:23 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[PROFILE_AUD](
	[PROFILE_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[PERSONALDATA_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[PROFILE_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

ALTER TABLE [dbo].[PROFILE_AUD]  WITH CHECK ADD  CONSTRAINT [FK8AFE56DAF0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[PROFILE_AUD] CHECK CONSTRAINT [FK8AFE56DAF0CF4BD]
GO

/****** Object:  Table [dbo].[TELEPHONE_AUD]    Script Date: 2013-07-31 2:32:41 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[TELEPHONE_AUD](
	[TELEPHONE_ID] [numeric](19, 0) NOT NULL,
	[REV] [int] NOT NULL,
	[REVTYPE] [tinyint] NULL,
	[areaCode] [varchar](255) NULL,
	[changedOn] [datetime] NULL,
	[endDate] [datetime] NULL,
	[extension] [varchar](255) NULL,
	[longDistance] [tinyint] NULL,
	[phoneNumber] [varchar](255) NULL,
	[phoneType] [varchar](255) NULL,
	[startDate] [datetime] NULL,
	[telPrimary] [tinyint] NULL,
	[telecomOrg] [varchar](255) NULL,
	[PROFILE_ID] [numeric](19, 0) NULL,
PRIMARY KEY CLUSTERED 
(
	[TELEPHONE_ID] ASC,
	[REV] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[TELEPHONE_AUD]  WITH CHECK ADD  CONSTRAINT [FKCA665FB5F0CF4BD] FOREIGN KEY([REV])
REFERENCES [dbo].[REVISIONS_INFO] ([revision_id])
GO

ALTER TABLE [dbo].[TELEPHONE_AUD] CHECK CONSTRAINT [FKCA665FB5F0CF4BD]
GO
--

/****** Object:  Table [dbo].[HRIS_CHANGE_LOG_REPORT_DATA]    Script Date: 2013-08-15 2:22:01 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

SET ANSI_PADDING ON
GO

CREATE TABLE [dbo].[HRIS_CHANGE_LOG_REPORT_DATA](
	[HRISCHANGELOGREPORTDATA_ID] [numeric](19, 0) IDENTITY(1,1) NOT NULL,
	[columnH] [varchar](255) NULL,
	[columnI] [varchar](255) NULL,
	[columnJ] [varchar](255) NULL,
	[columnK] [varchar](255) NULL,
	[columnL] [varchar](255) NULL,
	[columnM] [varchar](255) NULL,
	[columnN] [varchar](255) NULL,
	[columnO] [varchar](255) NULL,
	[columnP] [varchar](255) NULL,
	[columnQ] [varchar](255) NULL,
	[columnR] [varchar](255) NULL,
	[columnS] [varchar](255) NULL,
	[columnT] [varchar](255) NULL,
	[columnU] [varchar](255) NULL,
	[columnV] [varchar](255) NULL,
	[columnW] [varchar](255) NULL,
	[columnX] [varchar](255) NULL,
	[columnY] [varchar](255) NULL,
	[columnZ] [varchar](255) NULL,
	[firstName] [varchar](255) NULL,
	[focusId] [numeric](19, 0) NULL,
	[importDate] [datetime] NULL,
	[label] [varchar](255) NULL,
	[lastName] [varchar](255) NULL,
	[newValue] [varchar](255) NULL,
	[oldValue] [varchar](255) NULL,
	[dataRestricted] [tinyint] NULL,
PRIMARY KEY CLUSTERED 
(
	[HRISCHANGELOGREPORTDATA_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO

SET ANSI_PADDING OFF
GO

ALTER TABLE [dbo].[HRIS_CHANGE_LOG_REPORT_DATA] ADD  DEFAULT ((0)) FOR [dataRestricted]
GO

/****** Object:  Table [dbo].[HRIS_CHANGE_LOG_REPORT_DATA_REV]    Script Date: 2013-08-15 2:23:15 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[HRIS_CHANGE_LOG_REPORT_DATA_REV](
	[REVISION_ID] [int] NOT NULL,
UNIQUE NONCLUSTERED 
(
	[REVISION_ID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]

GO














