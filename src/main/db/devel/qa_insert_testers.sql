insert into users (US<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LASTNA<PERSON>, EMAIL, <PERSON><PERSON><PERSON>OR<PERSON>, SALT, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, <PERSON>NA<PERSON><PERSON>, deleted, failedLoginAttempts, CREATEDBY, CREATE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, UP<PERSON><PERSON><PERSON><PERSON><PERSON>, UPDA<PERSON><PERSON><PERSON>, DE<PERSON>RTMENT_ID) 
    select 'grahamc5', 'grahamc5', '<PERSON><PERSON>', '', '<PERSON>', '<EMAIL>', 'password', 'salt', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id 
    from department where name ='Dept of Lab. Medicine & Pathobiology';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, EMAIL, PASSWORD, SALT, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, <PERSON><PERSON><PERSON><PERSON>, deleted, failed<PERSON><PERSON><PERSON><PERSON>ttempts, CREATED<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>NT_ID) 
    select 'moirevan', 'moirevan', '<PERSON>', '', '<PERSON>ir', '<EMAIL>', 'password', 'salt', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id 
    from department where name ='Dept of Lab. Medicine & Pathobiology';
-- group members (add users to Users group)
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g 
    where u.username = 'grahamc5' and g.name = 'Users';
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g
    where u.username = 'moirevan' and g.name = 'Users';
-- custom authorities for Carolanne
insert into CUSTOM_AUTHORITY (USERS_ID, AUTHORITY_ID) select u.users_id, a.authority_id from users u, authority a
where u.username = 'grahamc5' and a.authority = 'ROLE_DEPARTMENT_ADMINISTRATOR';
insert into CUSTOM_AUTHORITY (USERS_ID, AUTHORITY_ID) select u.users_id, a.authority_id from users u, authority a
where u.username = 'grahamc5' and a.authority = 'ROLE_VIEW_SENSITIVE_DATA';
insert into CUSTOM_AUTHORITY (USERS_ID, AUTHORITY_ID) select u.users_id, a.authority_id from users u, authority a
where u.username = 'grahamc5' and a.authority = 'ROLE_VIEW_STAFF';
insert into CUSTOM_AUTHORITY (USERS_ID, AUTHORITY_ID) select u.users_id, a.authority_id from users u, authority a
where u.username = 'grahamc5' and a.authority = 'ROLE_EDIT_DATA';
-- custom authorities for Evan
insert into CUSTOM_AUTHORITY (USERS_ID, AUTHORITY_ID) select u.users_id, a.authority_id from users u, authority a
where u.username = 'moirevan' and a.authority = 'ROLE_DEPARTMENT_ADMINISTRATOR';
insert into CUSTOM_AUTHORITY (USERS_ID, AUTHORITY_ID) select u.users_id, a.authority_id from users u, authority a
where u.username = 'moirevan' and a.authority = 'ROLE_VIEW_SENSITIVE_DATA';
insert into CUSTOM_AUTHORITY (USERS_ID, AUTHORITY_ID) select u.users_id, a.authority_id from users u, authority a
where u.username = 'moirevan' and a.authority = 'ROLE_VIEW_STAFF';
insert into CUSTOM_AUTHORITY (USERS_ID, AUTHORITY_ID) select u.users_id, a.authority_id from users u, authority a
where u.username = 'moirevan' and a.authority = 'ROLE_EDIT_DATA';


