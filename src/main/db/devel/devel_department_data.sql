-- DEVELOPMENT SEED DATA --

-- DEPARTMENTS
insert into department (CREATEDBY, CREATEDON, UPDATEDBY, UPDATEDON, OPTLOCK, DELETED, NAME) values ('SYSTEM', getdate(), null, null, 0, 0, 'Test Department A');
insert into department (CREATEDBY, CREATEDON, UPDATEDBY, UPDATEDON, OPTLOCK, DELETED, NAME) values ('SYSTEM', getdate(), null, null, 0, 0, 'Test Department B');

-- USERS
declare @DeptId int;

-- USER: Department Admin for Test Department A
select @DeptId = DEPARTMENT_ID from "dbo"."DEPARTMENT" where name like 'Test Department A'; 
insert into users (USERNAME, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, 
EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, 
deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, 
UPDATEDON, DEPARTMENT_ID) values ('depthead1', 'Foo', 'E', 'Depthead1', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, @DeptId);

-- USER: User for Test Department A
insert into users (USERNAME, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, 
EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, 
deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, 
UPDATEDON, DEPARTMENT_ID) values ('user1', 'User', 'A', 'Numberone', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, @DeptId);

-- USER: Admin for Test Department B
select @DeptId = DEPARTMENT_ID from "dbo"."DEPARTMENT" where name like 'Test Department B'; 
insert into users (USERNAME, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, 
EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, 
deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, 
UPDATEDON, DEPARTMENT_ID) values ('depthead2', 'Bar', 'F', 'Depthead2', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, @DeptId);

-- USER: User for Test Department B
insert into users (USERNAME, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, 
EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, 
deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, 
UPDATEDON, DEPARTMENT_ID) values ('user2', 'User', 'B', 'Numbertwo', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, @DeptId);

-- group members
insert into group_members select users_id, 2 from users where username = 'depthead1';
insert into group_members select users_id, 2 from users where username = 'depthead2';

--
insert into group_members select users_id, 3 from users where username = 'user1';
insert into group_members select users_id, 3 from users where username = 'user2';
