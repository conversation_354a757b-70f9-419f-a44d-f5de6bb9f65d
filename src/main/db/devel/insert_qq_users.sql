insert into users (USERNA<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LASTNA<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, SALT, <PERSON><PERSON>IL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, <PERSON>NA<PERSON><PERSON>, deleted, failedLoginAttempts, CREATEDBY, CREATE<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, UPDATEDBY, UPDATED<PERSON>, DEPARTMENT_ID) select 'qq057919', 'qq057919', 'LMPADMIN', '', 'QQ057919', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name ='Dept of Lab. Medicine & Pathobiology';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTI<PERSON><PERSON>ONEXPIRED, <PERSON><PERSON><PERSON><PERSON>, deleted, failedLogin<PERSON>ttempts, CREA<PERSON>DB<PERSON>, CREATED<PERSON>, OP<PERSON>OC<PERSON>, UPDA<PERSON>D<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>PARTMENT_ID) select 'qq057920', 'qq057920', 'LMPUSER', '', 'QQ057920', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name ='Dept of Lab. Medicine & Pathobiology';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'qq057921', 'qq057921', 'DOMADMIN', '', 'QQ057921', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name ='Dept of Medicine';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'qq057922', 'qq057922', 'DOMUSER', '', 'QQ057922', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name ='Dept of Medicine';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'qq057923', 'qq057923', 'DFCMADMIN', '', 'QQ057923', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name ='Dept of Family & Community Medicine';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'qq057924', 'qq057924', 'DFCMUSER', '', 'QQ057924', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name ='Dept of Family & Community Medicine';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'qq057925', 'qq057925', 'MEDIMGADMIN', '', 'QQ057925', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name ='Dept of Medical Imaging';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'qq057926', 'qq057926', 'MEDIMGUSER', '', 'QQ057926', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name ='Dept of Medical Imaging';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'qq053694', 'qq053694', 'DLSPHADMIN', '', 'QQ053694', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name ='Dalla Lana School of Public Health';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'qq053695', 'qq053695', 'DLSPHUSER', '', 'QQ053695', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name ='Dalla Lana School of Public Health';
insert into users (USERNAME, UTORID, FIRSTNAME, MIDDLENAME, LASTNAME, PASSWORD, SALT, EMAIL, ACCOUNTNONEXPIRED, ACCOUNTNONLOCKED, CREDENTIALSNONEXPIRED, ENABLED, deleted, failedLoginAttempts, CREATEDBY, CREATEDON, OPTLOCK, UPDATEDBY, UPDATEDON, DEPARTMENT_ID) select 'qq058025', 'qq058025', 'HRADMIN', '', 'QQ058025', 'password', 'salt', '<EMAIL>', 1, 1, 1, 1, 0, 0, 'SYSTEM', getdate(), 0, null, null, department_id from department where name ='Discovery Commons';

-- group members (add users to Dept Admins group)
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g where u.username = 'qq057919' and g.name = 'Department Administrators';
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g where u.username = 'qq057921' and g.name = 'Department Administrators';
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g where u.username = 'qq057923' and g.name = 'Department Administrators';
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g where u.username = 'qq057925' and g.name = 'Department Administrators';
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g where u.username = 'qq053694' and g.name = 'Department Administrators';
-- set HRIS_RESTRICTED authority for department admins
insert into CUSTOM_AUTHORITY select u.users_id, a.AUTHORITY_ID from USERS u, AUTHORITY a where u.username = 'qq057919' and a.AUTHORITY = 'ROLE_VIEW_HRIS_RESTRICTED'
insert into CUSTOM_AUTHORITY select u.users_id, a.AUTHORITY_ID from USERS u, AUTHORITY a where u.username = 'qq057921' and a.AUTHORITY = 'ROLE_VIEW_HRIS_RESTRICTED'
insert into CUSTOM_AUTHORITY select u.users_id, a.AUTHORITY_ID from USERS u, AUTHORITY a where u.username = 'qq057923' and a.AUTHORITY = 'ROLE_VIEW_HRIS_RESTRICTED'
insert into CUSTOM_AUTHORITY select u.users_id, a.AUTHORITY_ID from USERS u, AUTHORITY a where u.username = 'qq057925' and a.AUTHORITY = 'ROLE_VIEW_HRIS_RESTRICTED'
insert into CUSTOM_AUTHORITY select u.users_id, a.AUTHORITY_ID from USERS u, AUTHORITY a where u.username = 'qq053694' and a.AUTHORITY = 'ROLE_VIEW_HRIS_RESTRICTED'

-- group members (add users to Users group)
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g where u.username = 'qq057920' and g.name = 'Users';
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g where u.username = 'qq057922' and g.name = 'Users';
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g where u.username = 'qq057924' and g.name = 'Users';
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g where u.username = 'qq057926' and g.name = 'Users';
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g where u.username = 'qq053695' and g.name = 'Users';

-- group members (add users to HR Admins group)
insert into group_members select u.users_id, g.GROUP_ID from users u, groups g where u.username = 'qq058025' and g.name = 'HR Administrators';