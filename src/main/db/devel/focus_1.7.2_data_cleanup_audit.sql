-- See <PERSON>IR<PERSON> ticket DCFOCUS-321
-- This query was used to preview data prior to deletion. Business analysts confirmed with clients based on the data produced by this query.
select p.PROFILE_ID, xf.PRIMARY_DEPT, xf.HOME_ORG_UNIT, pd.lastName, pd.firstName, xf.IMPORT_DATE, cf.Label as 'Custom Field', fv.VALUE_STRING as 'Custom Value', ca.ADDRESS_ID, ce.EMAIL_ID
from profile p
join PERSONALDATA pd on p.PERSONALDATA_ID=pd.PERSONALDATA_ID
join EXTRACTED_FIELDS xf on p.EXTRACTEDFIELDS_ID=xf.EXTRACTEDFIELD_ID
left join PROFILE_TAG pt on p.PROFILE_ID=pt.PROFILE_ID
left join PROFILEGROUP_PROFILE lp on p.PROFILE_ID=lp.PROFILE_ID
left join PROFILE_GROUP l on l.PROFILEGROUP_ID=lp.PROFILEGROUP_ID
left join FIELD_VALUE fv on fv.PROFILE_ID=p.PROFILE_ID
left join FIELD_DEF cf on fv.FIELDDEF_ID = cf.FIELDDEF_ID
left join ADDRESS ca on p.PROFILE_ID=ca.PROFILE_ID and ca.CUSTOMFIELD_ID is not null
left join EMAIL ce on p.PROFILE_ID=ce.PROFILE_ID and ce.CUSTOMFIELD_ID is not null
where p.FEEDSOURCE_ID=1 and xf.IMPORT_DATE < '2014-01-01'
