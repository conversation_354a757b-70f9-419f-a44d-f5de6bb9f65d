use FOCUS_DEMO;

declare @deptName varchar(255);
set @deptName = 'Dept of Lab. Medicine & Pathobiology';

declare @deptkey varchar(255);
select @deptkey = DEPT_KEY from DEPARTMENT where NAME=@deptName;

declare @profileids table (profile_id int not null);
insert into @profileids
select p.PROFILE_ID 
from PROFILE p 
left outer join APPOINTMENTDETAILS ad on p.PROFILE_ID=ad.PROFILE_ID 
left outer join OTHERUNIVERSITYAPPOINTMENT oua on p.PROFILE_ID=oua.PROFILE_ID 
where (
	ad.orgUnit=@deptkey
	or oua.organizationUnit=@deptkey
);

declare @extractedfields table (extractedfield_id int not null);
insert into @extractedfields
select p.EXTRACTEDFIELDS_ID from PROFILE p 
where p.PROFILE_ID in (select PROFILE_ID from @profileids);

declare @personaldata table (personaldata_id int not null)
insert into @personaldata
select p.PERSONALDATA_ID from PROFILE p 
where p.PROFILE_ID in (select PROFILE_ID from @profileids);

declare @facultywidedata table (facultywidedata_id int not null)
insert into @facultywidedata
select p.<PERSON>CU<PERSON>Y<PERSON>DEDATA_ID from PRO<PERSON><PERSON> p 
where p.PROFILE_ID in (select PROFILE_ID from @profileids);

delete from APPOINTMENTDETAILS where PROFILE_ID not in (select PROFILE_ID from @profileids);
TRUNCATE TABLE APPOINTMENTDETAILS_AUD;
TRUNCATE TABLE APPOINTMENTDETAILS_HISTORY;
delete from OTHERUNIVERSITYAPPOINTMENT where PROFILE_ID not in (select PROFILE_ID from @profileids);
TRUNCATE TABLE OTHERUNIVERSITYAPPOINTMENT_AUD;
TRUNCATE TABLE OTHERUNIVERSITYAPPOINTMENT_HISTORY;
delete from ACTION where PROFILE_ID not in (select PROFILE_ID from @profileids);
TRUNCATE TABLE ACTION_AUD;
TRUNCATE TABLE ACTION_HISTORY;
delete from COMMPREF where PROFILE_ID not in (select PROFILE_ID from @profileids);
delete cp from COMMPREF cp
inner join DEPARTMENT dept on dept.DEPARTMENT_ID = cp.DEPARTMENT_ID
where dept.NAME <> @deptName 
delete from ADDRESS where PROFILE_ID not in (select PROFILE_ID from @profileids);
TRUNCATE TABLE ADDRESS_AUD;
TRUNCATE TABLE ADDRESS_HISTORY;
TRUNCATE TABLE AUDIT_LOG;
TRUNCATE TABLE AUTHORITY_AUD;

ALTER TABLE PROFILE
DROP CONSTRAINT FK__PROFILE__UTORAUTH_DATA;

TRUNCATE TABLE UTORAUTH_DATA
UPDATE PROFILE set UTORAUTH_DATA_ID = NULL

ALTER TABLE PROFILE  WITH CHECK ADD  CONSTRAINT [FK__PROFILE__UTORAUTH_DATA] FOREIGN KEY(UTORAUTH_DATA_ID)
REFERENCES UTORAUTH_DATA (UTORAUTH_DATA_ID);

ALTER TABLE PROFILE CHECK CONSTRAINT FK__PROFILE__UTORAUTH_DATA;

delete from AWARD where EDUCATION_ID not in (select EDUCATION_ID from EDUCATION where PROFILE_ID in (select PROFILE_ID from @profileids));
TRUNCATE TABLE AWARD_AUD;
TRUNCATE TABLE AWARD_HISTORY;
TRUNCATE TABLE BASICPAY;
TRUNCATE TABLE BASICPAY_AUD;
TRUNCATE TABLE BASICPAY_HISTORY;

delete from BUDGETARY_CROSS_APPOINTMENT where PROFILE_ID not in (select PROFILE_ID from @profileids);
TRUNCATE TABLE BUDGETARY_CROSS_APPOINTMENT_AUD;

delete from CAREERPROGRESS where EDUCATION_ID not in (select EDUCATION_ID from EDUCATION where PROFILE_ID in (select PROFILE_ID from @profileids));
TRUNCATE TABLE CAREERPROGRESS_AUD;
TRUNCATE TABLE CAREERPROGRESS_HISTORY;

delete from CONTRACTELEMENT where PROFILE_ID not in (select PROFILE_ID from @profileids);
TRUNCATE TABLE CONTRACTELEMENT_AUD;
TRUNCATE TABLE CONTRACTELEMENT_HISTORY;

delete from CROSS_APPOINTMENT where PROFILE_ID not in (select PROFILE_ID from @profileids);
TRUNCATE TABLE CROSS_APPOINTMENT_AUD;

TRUNCATE TABLE CUSTOM_APPOINTMENT;
TRUNCATE TABLE CUSTOM_AUTHORITY;

delete from EDUCATION_AUD where PROFILE_ID not in (select PROFILE_ID from @profileids);

delete from PHONEBOOK_EMAIL where EMAIL_ID not in (select e.EMAIL_ID from EMAIL e join @profileids p on e.PROFILE_ID=p.PROFILE_ID);
delete from EMAIL where PROFILE_ID not in (select PROFILE_ID from @profileids);
TRUNCATE TABLE EMAIL_AUD;
TRUNCATE TABLE EMAIL_HISTORY;

delete from EMAIL_RECORD where PROFILE_ID not in (select PROFILE_ID from @profileids);
delete from FIELD_VALUE where PROFILE_ID not in (select PROFILE_ID from @profileids);
delete from GRADAPPT where EDUCATION_ID not in (select EDUCATION_ID from EDUCATION where PROFILE_ID in (select PROFILE_ID from @profileids));
TRUNCATE TABLE GRADAPPT_AUD;
TRUNCATE TABLE GRADAPPT_HISTORY;
delete from HRIS_RULE_EVENT where PROFILE_ID not in (select PROFILE_ID from @profileids);
TRUNCATE TABLE NOTE;
delete from POSTSECED where EDUCATION_ID not in (select EDUCATION_ID from EDUCATION where PROFILE_ID in (select PROFILE_ID from @profileids));
TRUNCATE TABLE POSTSECED_AUD;
TRUNCATE TABLE POSTSECED_HISTORY;
delete from PROFDESIGNATION where EDUCATION_ID not in (select EDUCATION_ID from EDUCATION where PROFILE_ID in (select PROFILE_ID from @profileids));
TRUNCATE TABLE PROFDESIGNATION_AUD;
TRUNCATE TABLE PROFDESIGNATION_HISTORY;
delete from FELLOWSHIP_CERTIFICATE where EDUCATION_ID not in (select EDUCATION_ID from EDUCATION where PROFILE_ID in (select PROFILE_ID from @profileids));
TRUNCATE TABLE FELLOWSHIP_CERTIFICATE_AUD;
TRUNCATE TABLE FELLOWSHIP_CERTIFICATE_HISTORY;
TRUNCATE TABLE FELLOWSHIP_CERTIFICATE_HISTORY_AUD;
delete from MEDIC_SPECIALITY where EDUCATION_ID not in (select EDUCATION_ID from EDUCATION where PROFILE_ID in (select PROFILE_ID from @profileids));
TRUNCATE TABLE MEDIC_SPECIALITY_AUD;
TRUNCATE TABLE MEDIC_SPECIALITY_HISTORY;
TRUNCATE TABLE MEDIC_SPECIALITY_HISTORY_AUD;
delete from EDUCATION where PROFILE_ID not in (select PROFILE_ID from @profileids);
delete from PROFILE_TAG where PROFILE_ID not in (select PROFILE_ID from @profileids);
delete from STATICGROUP_PROFILE where PROFILE_ID not in (select PROFILE_ID from @profileids);
delete from PHONEBOOK_TELEPHONE where TELEPHONE_ID not in (select t.TELEPHONE_ID from TELEPHONE t join @profileids p on t.PROFILE_ID=p.PROFILE_ID);
delete from TELEPHONE where PROFILE_ID not in (select PROFILE_ID from @profileids);
TRUNCATE TABLE TELEPHONE_AUD;
TRUNCATE TABLE TELEPHONE_HISTORY;
delete from WEBCV_BRIDGE where PROFILE_ID not in (select PROFILE_ID from @profileids);
delete from STATICGROUP_PROFILE where PROFILE_ID not in (select PROFILE_ID from @profileids);
delete from STATICGROUP_PROFILE where STATICGROUP_ID not in
	(select STATICGROUP_ID from STATIC_GROUP where DEPARTMENT_ID=(select DEPARTMENT_ID from DEPARTMENT where NAME=@deptName));
delete from STATIC_GROUP where DEPARTMENT_ID <> (select DEPARTMENT_ID from DEPARTMENT where NAME=@deptName);
TRUNCATE TABLE PROFILEGROUP_PROFILE;
delete er from EMAIL_RECORD er join PROFILE p on p.PROFILE_ID = er.PROFILE_ID where p.PROFILE_ID not in (select PROFILE_ID from @profileids);
delete pa from PROFILE_AUD pa inner join PROFILE p on p.PROFILE_ID = pa.PROFILE_ID where p.PROFILE_ID not in (select PROFILE_ID from @profileids);
delete from PROFILE where PROFILE_ID not in (select PROFILE_ID from @profileids);
delete from PERSONALDATA where PERSONALDATA_ID not in (select personaldata_id from @personaldata);
delete from FACULTY_WIDE_DATA where FACULTYWIDEDATA_ID not in (select facultywidedata_id from @facultywidedata);
delete from EXTRACTED_FIELDS where EXTRACTEDFIELD_ID not in (select extractedfield_id from @extractedfields);
delete ef from EXTRACTED_FIELDS ef inner join PROFILE p on p.EXTRACTEDFIELDS_ID = ef.EXTRACTEDFIELD_ID where PROFILE_ID not in (select PROFILE_ID from @profileids);

delete from DEPARTMENT_FILTER where DEPARTMENT_ID not in (select DEPARTMENT_ID from DEPARTMENT where NAME = @deptName);
delete from DEPARTMENT_SETTINGS where DEPARTMENT_ID not in (select DEPARTMENT_ID from DEPARTMENT where NAME = @deptName);
delete from DEPT_ADDRESS_PREF where DEPARTMENT_ID not in (select DEPARTMENT_ID from DEPARTMENT where NAME = @deptName);
delete from DEPT_EMAIL_PREF where DEPARTMENT_ID not in (select DEPARTMENT_ID from DEPARTMENT where NAME = @deptName);
delete from DEPT_EMAIL_SETTING where DEPARTMENT_ID not in (select DEPARTMENT_ID from DEPARTMENT where NAME = @deptName);
delete from DEPT_PHONE_PREF where DEPARTMENT_ID not in (select DEPARTMENT_ID from DEPARTMENT where NAME = @deptName);
delete from FEED_FIELD_PERMISSION where DEPARTMENT_ID not in (select DEPARTMENT_ID from DEPARTMENT where NAME = @deptName);

TRUNCATE TABLE FEED_ERROR;
--TRUNCATE TABLE FEED
declare @userids table (USERS_ID int not null)
insert into @userids
select u.users_id from USERS u 
where u.username in ('vanbeenc', 'commons1', 'kreutzer');

declare @customfields table (CUSTOMFIELD_ID int not null)
insert into @customfields
select def.FIELDDEF_ID from FIELD_DEF def 
join FEED_SOURCE fs on fs.FEEDSOURCE_ID = def.FEEDSOURCE_ID
where fs.SOURCE = 'CUSTOM FIELD';

delete ef from EMAIL_FIELD ef
join EMAIL_TEMPLATE et on et.EMAILTEMPLATE_ID = ef.EMAILTEMPLATE_ID
where et.USERS_ID not in (select USERS_ID from @userids);
delete from EMAIL_FIELD where FIELDDEF_ID in (select CUSTOMFIELD_ID from @customfields);
delete EMAIL_TEMPLATE where USERS_ID not in (select USERS_ID from @userids);
update EMAIL_TEMPLATE set DEPARTMENT_ID = (select DEPARTMENT_ID from DEPARTMENT where NAME = @deptName)

delete rf from REPORT_FIELD rf
join REPORT_TEMPLATE rt on rt.REPORTTEMPLATE_ID = rf.REPORTTEMPLATE_ID
where rt.USERS_ID not in (select USERS_ID from @userids);
delete from REPORT_FIELD where FIELDDEF_ID in (select CUSTOMFIELD_ID from @customfields);
delete REPORT_TEMPLATE where USERS_ID not in (select USERS_ID from @userids);
update REPORT_TEMPLATE set DEPARTMENT_ID = (select DEPARTMENT_ID from DEPARTMENT where NAME = @deptName)

delete sc from SEARCH_CRITERION sc
join SEARCH_CRITERIA_GANG scg on scg.SEARCH_CRITERIA_GANG_ID = sc.SEARCH_CRITERIA_GANG_ID
join SEARCH s on s.SEARCH_ID = scg.SEARCH_ID
where s.AUTHUSER_ID not in (select USERS_ID from @userids);

delete from SEARCH_CRITERION where SEARCHABLE_FIELDDEF_ID in (select CUSTOMFIELD_ID from @customfields)

delete scg from SEARCH_CRITERIA_GANG scg
join SEARCH s on s.SEARCH_ID = scg.SEARCH_ID
where s.AUTHUSER_ID not in (select USERS_ID from @userids);

delete from SEARCH where AUTHUSER_ID not in (select USERS_ID from @userids);
update SEARCH set DEPARTMENT_ID = (select DEPARTMENT_ID from DEPARTMENT where NAME = @deptName)
delete from GROUP_MEMBERS where USERS_ID not in (select USERS_ID from @userids);

delete from  je from JOB j
inner join JOB_ERROR je on je.JOB_ID = j.JOB_ID
where j.USERS_ID not in (select USERS_ID from @userids);
delete from JOB where USERS_ID not in (select USERS_ID from @userids);
delete pt from PROFILE_TAG pt inner join TAG t on t.TAG_ID = pt.TAG_ID where t.USERS_ID not in (select USERS_ID from @userids);
delete from TAG where USERS_ID not in (select USERS_ID from @userids);
--TRUNCATE TABLE email_record;
delete from EMAIL_RECORD where SENDER_ID not in (select USERS_ID from @userids);

delete tf  from TAB_FIELD tf
inner join CONTAINER_TAB ct on ct.CONTAINERTAB_ID = tf.CONTAINERTAB_ID
inner join CONTAINER_DEF cd on cd.CONTAINERDEF_ID = ct.CONTAINER_ID
inner join USER_CONTAINERDEF uc on uc.CONTAINERDEF_ID = cd.CONTAINERDEF_ID
inner join USERS u on u.users_id = uc.users_id
where uc.users_id not in (select USERS_ID from @userids);

delete ct  from CONTAINER_TAB ct
inner join CONTAINER_DEF cd on cd.CONTAINERDEF_ID = ct.CONTAINER_ID
inner join USER_CONTAINERDEF uc on uc.CONTAINERDEF_ID = cd.CONTAINERDEF_ID
inner join USERS u on u.users_id = uc.users_id
where uc.users_id not in (select USERS_ID from @userids);

delete uc  from USER_CONTAINERDEF uc
inner join USERS u on u.users_id = uc.users_id
where uc.users_id not in (select USERS_ID from @userids);

delete cd  from CONTAINER_DEF cd
inner join USER_CONTAINERDEF uc on uc.CONTAINERDEF_ID = cd.CONTAINERDEF_ID
inner join USERS u on u.users_id = uc.users_id
where uc.users_id not in (select USERS_ID from @userids);

delete sgp from static_group sg inner join STATICGROUP_PROFILE sgp on sgp.STATICGROUP_ID = sg.STATICGROUP_ID where sg.AUTHUSER_ID not in (select USERS_ID from @userids);
delete from static_group where AUTHUSER_ID not in (select USERS_ID from @userids);
delete from USERS where users_id not in (select USERS_ID from @userids);

delete from FIELD_VALUE where FIELDDEF_ID in (select CUSTOMFIELD_ID from @customfields);
delete from FIELDDEFS_FDAPPLICATIONS where FIELDDEF_ID in (select CUSTOMFIELD_ID from @customfields);
delete from TAB_FIELD where FIELDDEF_ID in (select CUSTOMFIELD_ID from @customfields);
delete from ADDRESS where CUSTOMFIELD_ID in (select CUSTOMFIELD_ID from @customfields);
delete from EMAIL where CUSTOMFIELD_ID in (select CUSTOMFIELD_ID from @customfields);
delete from TELEPHONE where CUSTOMFIELD_ID in (select CUSTOMFIELD_ID from @customfields);
delete from HRIS_RULE_EVENT where FIELDDEF_ID in (select CUSTOMFIELD_ID from @customfields);
delete from FIELD_DEF where FIELDDEF_ID in (select CUSTOMFIELD_ID from @customfields);

TRUNCATE TABLE HRIS_CHANGE_LOG_REPORT_DATA;
TRUNCATE TABLE HRIS_CHANGE_LOG_REPORT_DATA_REV;
--TRUNCATE TABLE JOB_ERROR;
--TRUNCATE TABLE JOB

TRUNCATE TABLE PROFILE_GROUP;
TRUNCATE TABLE PROFILE_GROUP_CRIT;

delete from tf from TAB_FIELD tf
inner join CONTAINER_TAB ct on ct.CONTAINERTAB_ID = tf.CONTAINERTAB_ID
inner join CONTAINER_DEF cd on cd.CONTAINERDEF_ID = ct.CONTAINER_ID
inner join DEPARTMENT dept on dept.DEPARTMENT_ID = cd.DEPARTMENT_ID
where dept.NAME <> @deptName

delete ct from CONTAINER_TAB ct
inner join CONTAINER_DEF cd on cd.CONTAINERDEF_ID = ct.CONTAINER_ID
inner join DEPARTMENT dept on dept.DEPARTMENT_ID = cd.DEPARTMENT_ID
where dept.NAME <> @deptName

delete uc  from USER_CONTAINERDEF uc
inner join CONTAINER_DEF cd on cd.CONTAINERDEF_ID = uc.CONTAINERDEF_ID
inner join DEPARTMENT dept on dept.DEPARTMENT_ID = cd.DEPARTMENT_ID
where dept.NAME <> @deptName

delete cd from CONTAINER_DEF cd
inner join DEPARTMENT dept on dept.DEPARTMENT_ID = cd.DEPARTMENT_ID
where dept.NAME <> @deptName


delete sc from SEARCH_CRITERION sc
inner join SEARCH_CRITERIA_GANG scg on scg.SEARCH_CRITERIA_GANG_ID = sc.SEARCH_CRITERIA_GANG_ID
inner join SEARCH s on s.SEARCH_ID = scg.SEARCH_ID
inner join DEPARTMENT dept on dept.DEPARTMENT_ID = s.DEPARTMENT_ID where dept.NAME <> @deptName;

delete scg from SEARCH_CRITERIA_GANG scg
inner join SEARCH s on s.SEARCH_ID = scg.SEARCH_ID
inner join DEPARTMENT dept on dept.DEPARTMENT_ID = s.DEPARTMENT_ID 
where dept.NAME <> @deptName

delete s from SEARCH s inner join DEPARTMENT dept on dept.DEPARTMENT_ID = s.DEPARTMENT_ID where dept.NAME <> @deptName;

delete ef from EMAIL_FIELD ef
inner join EMAIL_TEMPLATE et on et.EMAILTEMPLATE_ID = ef.EMAILTEMPLATE_ID
where et.DEPARTMENT_ID in (select DEPARTMENT_ID from DEPARTMENT where name<> @deptName);

delete EMAIL_TEMPLATE where DEPARTMENT_ID in (select DEPARTMENT_ID from DEPARTMENT where name<> @deptName);

delete rf from REPORT_FIELD rf
join REPORT_TEMPLATE rt on rt.REPORTTEMPLATE_ID = rf.REPORTTEMPLATE_ID
where rt.DEPARTMENT_ID in (select DEPARTMENT_ID from DEPARTMENT where name <> @deptName);

delete REPORT_TEMPLATE where DEPARTMENT_ID in (select DEPARTMENT_ID from DEPARTMENT where name <> @deptName);

update USERS set DEPARTMENT_ID = (select DEPARTMENT_ID from DEPARTMENT where NAME = @deptName)

-- BSL disabled
--delete u from USERS u inner join DEPARTMENT dept on dept.DEPARTMENT_ID = u.DEPARTMENT_ID where dept.NAME <> @deptName

-- BSL disabled
--delete from DEPARTMENT where NAME <> @deptName;

--scramble data
update DEPARTMENT set NAME='Department of Demo' where NAME=@deptName
set @deptName='Department of Demo';

update PERSONALDATA set middleInitial='';
update PERSONALDATA set middleName='';
update PERSONALDATA set knownAs='';
update PERSONALDATA set birthName='';
update PERSONALDATA set nationality='';
update PERSONALDATA set startDate=NULL;
update PERSONALDATA set endDate=NULL;
update PERSONALDATA set birthDate='9999-12-31';
update FACULTY_WIDE_DATA set EMAIL_ADDRESS=NULL;
update OTHERUNIVERSITYAPPOINTMENT set cpsoNumber = 0;

-- Post Secondary Education
update POSTSECED set instutionText = 'Post Secondary Institution #' + cast((RECORD_NUMBER + 1) as varchar(255)), yearConfirmed=(select floor(rand(education_id)*12) + yearConfirmed);

--Address
update ADDRESS set streetAndHouseNumber='1 King''s College Circle';
update ADDRESS set secondAddressLine='';
update ADDRESS set city='Toronto';
update ADDRESS set postalCode='M5S 1A8';
update ADDRESS set province='ON';
update ADDRESS set country='Canada';

--focusid
declare @focusId int;
set @focusId=1;
declare @extractedfield_focusId table(focus_id int not null);
declare @focus_id_min int;
WHILE (select COUNT(*) from EXTRACTED_FIELDS) >= @focusId
BEGIN
  select @focus_id_min= MIN(FOCUS_ID) from EXTRACTED_FIELDS where FOCUS_ID not in (select focus_id from @extractedfield_focusId)
  insert into @extractedfield_focusId(focus_id) values (@focus_id_min)
  insert into @extractedfield_focusId(focus_id) values (@focusId)
  update EXTRACTED_FIELDS set FOCUS_ID=@focusId where FOCUS_ID=@focus_id_min
  set @focusId += 1
END
update EXTRACTED_FIELDS set CPSO_NUMBER = null;
update EXTRACTED_FIELDS set PRIMARY_DEPT = null;
update EXTRACTED_FIELDS set PAY_SCALE_GROUP = null;
update EXTRACTED_FIELDS set PAY_SCALE_LEVEL = null;
update EXTRACTED_FIELDS set PAY_SCALE_TYPE = null;
update EXTRACTED_FIELDS set WAGE_TYPE_TEXT = null;

--first name, last name, full name, gender
declare @row_count int;

IF OBJECT_ID('tempdb..#focus_demo_random_csv') IS NOT NULL DROP TABLE #focus_demo_random_csv;
create table #focus_demo_random_csv(id int not null, firstname varchar(255), lastname varchar(255), gender varchar(10));

BULK INSERT #focus_demo_random_csv
FROM 'C:\Backups\generatednames.csv'
WITH
(
FIELDTERMINATOR = ',',
ROWTERMINATOR = '0x0a',
FIRSTROW = 2
)

declare @csv_profile_in_use table (dummy_id int not null);
declare @processed_personaldata table (personaldata_id int not null);
declare @personaldata_id_min int;
declare @id int;
declare @firstname varchar(255);
declare @lastname varchar(255);
declare @gender varchar(10);

declare @counter int;
set @counter=1;
select @row_count=COUNT(*) from PERSONALDATA;

WHILE @row_count >= @counter
BEGIN
  select @personaldata_id_min=MIN(PERSONALDATA_ID) from PERSONALDATA where PERSONALDATA_ID not in (select personaldata_id from @processed_personaldata)
  IF @personaldata_id_min is not null
    BEGIN
	  select @id=id, @firstname=firstname, @lastname=lastname, @gender=gender from #focus_demo_random_csv where id not in (select dummy_id from @csv_profile_in_use)
	  IF @firstname is null AND @lastname is null AND @gender is null
	    BEGIN
	      set @counter = @row_count + 1
	    END
	  ELSE
	    BEGIN
	      update PERSONALDATA set firstName=@firstname, lastName=@lastname, gender=@gender where PERSONALDATA_ID = @personaldata_id_min
	      insert into @csv_profile_in_use(dummy_id) values(@id)
	      insert into @processed_personaldata(personaldata_id) values(@personaldata_id_min)
	      set @counter += 1
	    END
    END
  ELSE
    BEGIN
      set @counter = @row_count + 1
    END
END

update PERSONALDATA set fullName=firstName + ' ' + lastName;
update PERSONALDATA set gender='Male' where gender='male';
update PERSONALDATA set gender='Male' where gender='female';

--personnel number
declare @personnelnumber int;
set @personnelnumber=1;
declare @personaldata_personnelnumber table(personnel_number int not null);
declare @personnelnumber_min int;
--declare @row_count int
select @row_count=COUNT(*) from PERSONALDATA;
WHILE @row_count >= @personnelnumber
BEGIN
  select @personnelnumber_min= MIN(personnelNumber) from PERSONALDATA where personnelNumber not in (select personnel_number from @personaldata_personnelnumber)
  IF @personnelnumber_min is not null
	begin
	insert into @personaldata_personnelnumber(personnel_number) values (@personnelnumber_min)
	insert into @personaldata_personnelnumber(personnel_number) values (@personnelnumber)
	update PERSONALDATA set personnelNumber=@personnelnumber where personnelNumber=@personnelnumber_min
	set @personnelnumber += 1
	end
  ELSE
	begin
    set @personnelnumber = @row_count + 1
    end
END

--email
update EMAIL set emailAddress='';
update e set e.emailAddress=lower(pd.firstName) + '.' + lower(pd.lastName) + '@example.com' from PROFILE p
join EMAIL e on e.PROFILE_ID = p.PROFILE_ID
join PERSONALDATA pd on pd.PERSONALDATA_ID = p.PERSONALDATA_ID;

update e set e.emailAddress=lower(pd.firstName) + '.' + lower(pd.lastName) + '@example.com' from PROFILE p
join EMAIL e on e.PROFILE_ID = p.PROFILE_ID
join PERSONALDATA pd on pd.PERSONALDATA_ID = p.PERSONALDATA_ID;

update PHONEBOOK_EMAIL set DEPARTMENT_NAME = 'PHONEBOOK DEPARMENT';
-- telephone
update TELEPHONE set PHONENUMBER='************';
update PHONEBOOK_TELEPHONE set DEPARTMENT_NAME = 'PHONEBOOK DEPARTMENT';
