package ca.utoronto.med.dc.focus.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity
@Table(name = PhoneBookEmail.TABLENAME)
public class PhoneBookEmail implements Serializable {
	private static final long serialVersionUID = 1L;
	public static final String TABLENAME = "PHONEBOOK_EMAIL";
	public static final String COLUMN_ID = "PHONEBOOK_EMAIL_ID";
	/**
	 * HRIS Email entity requires a non-null Record Number
	 */
	public static final Long RECORD_NUMBER = -99L;

	private Long id;
	private Long alphaId;
	private Email email;
	private String type;
	private Boolean hrisOnly;
	private String departmentName;

	public PhoneBookEmail() {
	}

	/**
	 * Construct from PhoneBook import data
	 * 
	 * @param departmentName
	 * @param type
	 * @param hrisOnly
	 */
	public PhoneBookEmail(String departmentName, String type, boolean hrisOnly,
			Profile p, String emailAddress) {
		this();
		this.type = type;
		this.departmentName = departmentName;
		this.hrisOnly = hrisOnly;
		this.email = new Email();
		email.setRecordNumber(RECORD_NUMBER);
		email.setProfile(p);
		email.setEmailAddress(emailAddress);
		email.setPhoneBookEmail(this);
	}

	@Id
	@Column(name = COLUMN_ID)
	@GeneratedValue(strategy = GenerationType.AUTO)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column
	public Long getAlphaId() {
		return alphaId;
	}

	public void setAlphaId(Long alphaId) {
		this.alphaId = alphaId;
	}

	@OneToOne
	@JoinColumn(name = "EMAIL_ID", nullable = false)
	public Email getEmail() {
		return email;
	}

	public void setEmail(Email email) {
		this.email = email;
	}

	@Column(name = "EMAIL_TYPE")
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "HRIS_ONLY")
	public Boolean isHrisOnly() {
		return hrisOnly;
	}

	public void setHrisOnly(Boolean hrisOnly) {
		this.hrisOnly = hrisOnly;
	}

	@Column(name = "DEPARTMENT_NAME")
	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}

	@Override
	public String toString() {
		return "PhoneBookEmail [id=" + id + ", email=" + nullSafeEmail()
				+ ", type=" + type + ", hrisOnly=" + hrisOnly
				+ ", departmentName=" + departmentName + "]";
	}

	private String nullSafeEmail() {
		return email == null ? "NULL" : email.getEmailAddress();
	}

	@Override
	public boolean equals(Object obj) {
		if (obj instanceof PhoneBookEmail) {
			PhoneBookEmail other = (PhoneBookEmail) obj;
			if (departmentName != null && other.departmentName != null
					&& type != null && other.type != null) {
				return departmentName.equals(other.departmentName)
						&& type.equals(other.type);
			}
		}
		return false;
	}
	
	@Override
	public int hashCode() {
		return (departmentName + "___" + type).hashCode();
	}

}
