package ca.utoronto.med.dc.focus.domain;

import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Pattern;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Index;
import org.hibernate.validator.constraints.NotEmpty;

@Entity
@Table(name = "TAG", uniqueConstraints = @UniqueConstraint(columnNames = {
		"NAME", "USERS_ID" }))
public class Tag extends BaseEntity {
	private Long id;

	@NotEmpty(message = "Tag cannot be empty.")
	@Pattern(regexp = "^[A-Za-z0-9-_].*", message = "Please only Alphanumeric and hyphens/underscores")
	private String name;
	private AuthUser tagOwner;
	private Set<Profile> taggedProfiles = new HashSet<Profile>();

	/**
	 * 
	 */
	public Tag() {
	}

	/**
	 * @param name
	 * @param tagOwner
	 */
	public Tag(String name, AuthUser tagOwner) {
		super();
		this.name = name;
		this.tagOwner = tagOwner;
	}

	public Tag(Long id, String name) {
		super();
		this.id = id;
		this.name = name;
	}

	public Tag(String name, AuthUser tagOwner, Set<Profile> taggedProfiles) {
		super();
		this.name = name;
		this.tagOwner = tagOwner;
		this.taggedProfiles = taggedProfiles;
	}

	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "TAG_ID")
	public Long getId() {
		return id;
	}

	public void setName(String name) {
		this.name = name;
	}

	/**
	 * @return the displayOrder
	 */
	@Column(name = "NAME", nullable = false)
	public String getName() {
		return name;
	}

	/**
	 * @return the tagOwner
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USERS_ID", nullable = false)
	@ForeignKey(name = "FK_TAG__USERS")
	@Index(name = "IDX__TAG__USERS_ID")
	public AuthUser getTagOwner() {
		return tagOwner;
	}

	/**
	 * @param tagOwner
	 *            the tagOwner to set
	 */
	public void setTagOwner(AuthUser tagOwner) {
		this.tagOwner = tagOwner;
	}

	/**
	 * @return the taggedProfiles
	 */

	@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.MERGE })
	@JoinTable(name = "PROFILE_TAG", joinColumns = @JoinColumn(name = "TAG_ID"), inverseJoinColumns = @JoinColumn(name = "PROFILE_ID"), uniqueConstraints = @UniqueConstraint(columnNames = {
			"PROFILE_ID", "TAG_ID" }))
	@ForeignKey(name = "FK__TAG__PROFILE", inverseName = "FK__PROFILE__TAG")
	public Set<Profile> getTaggedProfiles() {
		return taggedProfiles;
	}

	/**
	 * @param taggedProfiles
	 *            the taggedProfiles to set
	 */
	public void setTaggedProfiles(Set<Profile> taggedProfiles) {
		this.taggedProfiles = taggedProfiles;
	}

	/**
	 * Helper to manage both sides of the relationship
	 * 
	 * @param p
	 */
	public void addProfile(Profile p) {
		p.getTags().add(this);
		taggedProfiles.add(p);
	}

	public void removeProfile(Profile profile) {
		taggedProfiles.remove(profile);
	}

	/**
	 * 
	 */
	public void removeProfiles(Set<Profile> profiles) {
		for (Profile p : profiles) {
			taggedProfiles.remove(p);
		}
	}

	/**
	 * 
	 */
	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" id: " + id + newLine);
		result.append(" Name: " + name + newLine);
		result.append("}");

		return result.toString();
	}


}
