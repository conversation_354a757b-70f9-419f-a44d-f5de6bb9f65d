package ca.utoronto.med.dc.focus.domain;

import java.io.Serializable;

public class UserContainerDefStateId implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = 384458503199151419L;

	/**
	 * 
	 */
	private Long authUser;
	private Long container;

	public UserContainerDefStateId() {
		super();
	}

	/**
	 * @param user
	 * @param container
	 */
	public UserContainerDefStateId(Long userId, Long containerId) {
		super();
		this.authUser = userId;
		this.container = containerId;
	}

	/**
	 * @return the authUserId
	 */
	public Long getAuthUser() {
		return authUser;
	}

	/**
	 * @param authUserId
	 *            the authUserId to set
	 */
	public void setAuthUser(Long authUser) {
		this.authUser = authUser;
	}

	/**
	 * @return the containerId
	 */
	public Long getContainer() {
		return container;
	}

	/**
	 * @param containerId
	 *            the containerId to set
	 */
	public void setContainer(Long container) {
		this.container = container;
	}

	/**
	 * @return the serialversionuid
	 */
	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public int hashCode() {
		return (int) (authUser + container);
	}

	public boolean equals(Object object) {
		if (object instanceof UserContainerDefStateId) {
			UserContainerDefStateId otherId = (UserContainerDefStateId) object;
			return (otherId.authUser == this.authUser)
					&& (otherId.container == this.container);
		}
		return false;
	}

}
