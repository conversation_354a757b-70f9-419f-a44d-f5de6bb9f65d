package ca.utoronto.med.dc.focus.domain;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

@Entity
@Table(name = "TAB_FIELD", uniqueConstraints = @UniqueConstraint(columnNames = {
		"CONTAINERTAB_ID", "FIELDDEF_ID" }, name = "UQ__TAB_FIELD__TAB_ID__FIELDDEF_ID"))
public class TabField extends BaseEntity {
	private Long id;
	private ContainerTab parent;
	private FieldDef fieldDef;
	private Integer displayOrder;

	/**
	 * 
	 */
	public TabField() {
	}

	/**
	 * 
	 */
	public TabField(Integer displayOrder) {
		this.displayOrder = displayOrder;
	}

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "TABFIELD_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public TabField setId(Long id) {
		this.id = id;
		return this;
	}

	/**
	 * @return the parent
	 */
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "CONTAINERTAB_ID")
	public ContainerTab getParent() {
		return parent;
	}

	/**
	 * @param parent
	 *            the parent to set
	 */
	public TabField setParent(ContainerTab parent) {
		this.parent = parent;
		return this;
	}

	/**
	 * @return the fieldDef
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FIELDDEF_ID", nullable = false)
	public FieldDef getFieldDef() {
		return fieldDef;
	}

	/**
	 * @param fieldDef
	 *            the fieldDef to set
	 */
	public TabField setFieldDef(FieldDef fieldDef) {
		this.fieldDef = fieldDef;
		return this;
	}

	/**
	 * @return the displayOrder
	 */
	@Column(name = "DISPLAY_ORDER", nullable = false)
	public Integer getDisplayOrder() {
		return displayOrder;
	}

	/**
	 * @param displayOrder
	 *            the displayOrder to set
	 */
	public TabField setDisplayOrder(Integer displayOrder) {
		this.displayOrder = displayOrder;
		return this;
	}
}
