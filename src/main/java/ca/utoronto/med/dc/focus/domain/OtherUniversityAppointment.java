package ca.utoronto.med.dc.focus.domain;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.envers.Audited;
import org.slf4j.ext.XLogger;
import org.slf4j.ext.XLogger.Level;
import org.slf4j.ext.XLoggerFactory;

@Entity
@Table(name = "OTHERUNIVERSITYAPPOINTMENT")
@Audited
public class OtherUniversityAppointment extends BaseEntity {

	XLogger logger = XLoggerFactory.getXLogger(this.getClass());

	public static final String SUBTYPE_ADJUNCT = "ADJ";
	public static final String SUBTYPE_CLINICAL_CROSS = "CCA";
	public static final String SUBTYPE_CLINICAL_MD = "CLN";
	public static final String SUBTYPE_HOSPITAL = "HOS";
	public static final String SUBTYPE_NON_BUDGETARY_CROSS = "NON";
	public static final String SUBTYPE_PRIMARY = "PRI";
	public static final String SUBTYPE_STATUS_ONLY = "STA";
	public static final String SUBTYPE_VISITING_PROF = "VIS";
	public static final String SUBTYPE_EMERITUS_EMERITA = "EMR";

	/**
	 * appointmentTypeText values
	 */
	public static final String APPT_TYPE_TXT_ADJUNCT_CLINICAL = "Clinical (MD) Adjunct Appt";
	public static final String APPT_TYPE_TXT_ADJUNCT_LECTURER = "Adjunct: Lecturer";
	public static final String APPT_TYPE_TXT_ADJUNCT_PROF = "Adjunct Professor";
	public static final String APPT_TYPE_TXT_CROSS_CLINICAL = "Clinical (MD) Cross-Appt";
	public static final String APPT_TYPE_TXT_FULL_TIME_CLINICAL = "Clinical (MD) Full Time Appt";
	public static final String APPT_TYPE_TXT_PART_TIME_CLINICAL = "Clinical (MD) Part Time Appt";
	public static final String APPT_TYPE_TXT_PRIMARY = "Primary Department";
	public static final String APPT_TYPE_TXT_RESEARCHER = "PhD Researcher[Medicine Only]";
	public static final String APPT_TYPE_TXT_STATUS_ONLY = "Status Only";
	public static final String APPT_TYPE_TXT_VISITING_CLINICAL = "Clinical (MD) Visiting Appt";
	public static final String APPT_TYPE_TXT_VISITING_PROF = "Visiting Professor";
	public static final String APPT_TYPE_TXT_FULL_TIME_EQUIVALENT = "Clinical (MD) Full Time Equivalent";
	public static final String APPT_TYPE_TXT_NON_Budgetary = "Non-Budgetary";


	private Long id;
	private Date startDate;
	private Date endDate;
	private Long recordNumber;

	private Date changedOn;
	private String subtype;
	private String subtypeText;
	private String appointmentType;
	private String appointmentTypeText;
	private String organizationUnit;
	private String organizationUnitText;
	private String emeritusOrEmeritaRank;
	private String mainIndicator;
	private String academicRank;
	private String academicRankText;
	private String careerPathTitle;
	private String hospitalCode;
	private String hospitalAddress;
	private String clinContractStatus;
	private String clinContractStatusText;
	private Long cpsoNumber;

	private Profile profile;

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "OTHERUNIVERSITYAPPOINTMENT_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the cpsoNumber
	 */
	public Long getCpsoNumber() {
		return cpsoNumber;
	}

	/**
	 * @param cpsoNumber
	 *            the cpsoNumber to set
	 */
	public void setCpsoNumber(Long cpsoNumber) {
		this.cpsoNumber = cpsoNumber;
	}

	/**
	 * @return the start
	 */
	@Temporal(TemporalType.DATE)
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * @param start
	 *            the start to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * @return the to
	 */
	@Temporal(TemporalType.DATE)
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * @param to
	 *            the to to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/**
	 * @return the emeritusOrEmeritaRank
	 */
	public String getEmeritusOrEmeritaRank() {
		return emeritusOrEmeritaRank;
	}

	/**
	 * @param emeritusOrEmeritaRank
	 *            the emeritusOrEmeritaRank to set
	 */
	public void setEmeritusOrEmeritaRank(String emeritusOrEmeritaRank) {
		this.emeritusOrEmeritaRank = emeritusOrEmeritaRank;
	}

	/**
	 * @return the hospitalCode
	 */
	public String getHospitalCode() {
		return hospitalCode;
	}

	/**
	 * @param hospitalCode
	 *            the hospitalCode to set
	 */
	public void setHospitalCode(String hospitalCode) {
		this.hospitalCode = hospitalCode;
	}

	/**
	 * @return the organizationUnit
	 */
	public String getOrganizationUnit() {
		return organizationUnit;
	}

	/**
	 * @param organizationUnit
	 *            the organizationUnit to set
	 */
	public void setOrganizationUnit(String organizationUnit) {
		this.organizationUnit = organizationUnit;
	}

	/**
	 * @return the careerPathTitle
	 */
	public String getCareerPathTitle() {
		return careerPathTitle;
	}

	/**
	 * @param careerPathTitle
	 *            the careerPathTitle to set
	 */
	public void setCareerPathTitle(String careerPathTitle) {
		this.careerPathTitle = careerPathTitle;
	}

	/**
	 * @return the mainIndicator
	 */
	public String getMainIndicator() {
		return mainIndicator;
	}

	/**
	 * @param mainIndicator
	 *            the mainIndicator to set
	 */
	public void setMainIndicator(String mainIndicator) {
		this.mainIndicator = mainIndicator;
	}

	/**
	 * @return the profile
	 */
	@ManyToOne(targetEntity = ca.utoronto.med.dc.focus.domain.Profile.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "PROFILE_ID", nullable = false)
	@ForeignKey(name = "FK_OTHERUNIVERSITYAPPOINTMENT__PROFILE")
	@org.hibernate.annotations.Index(name = "IDX__OTHERUNIVERSITYAPPOINTMENT__PROFILE_ID")
	public Profile getProfile() {
		return profile;
	}

	/**
	 * @param profile
	 *            the profile to set
	 */
	public void setProfile(Profile profile) {
		this.profile = profile;
	}

	/**
	 * 
	 */
	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" id: " + id + newLine);

		result.append(" cpsoNumber: " + cpsoNumber + newLine);
		result.append(" startDate: " + startDate + newLine);
		result.append(" endDate: " + endDate + newLine);
		result.append(" emeritusOrEmeritaRank: " + emeritusOrEmeritaRank
				+ newLine);
		result.append(" hospitalCode: " + hospitalCode + newLine);
		result.append(" organizationUnit: " + organizationUnit + newLine);
		result.append(" careerPathTitle: " + careerPathTitle + newLine);
		result.append(" mainIndicator: " + mainIndicator + newLine);
		result.append("}");

		return result.toString();
	}

	/**
	 * @return the recordNumber
	 */
	@Column(name = "RECORD_NUMBER"/* , unique = true */, nullable = false)
	public Long getRecordNumber() {
		return recordNumber;
	}

	/**
	 * @param recordNumber
	 *            the recordNumber to set
	 */
	public void setRecordNumber(Long recordNumber) {
		this.recordNumber = recordNumber;
	}

	/**
	 * @return the changedOn
	 */
	@Temporal(TemporalType.DATE)
	public Date getChangedOn() {
		return changedOn;
	}

	/**
	 * @param changedOn
	 *            the changedOn to set
	 */
	public void setChangedOn(Date changedOn) {
		this.changedOn = changedOn;
	}

	/**
	 * @return the subtype
	 */
	public String getSubtype() {
		return subtype;
	}

	/**
	 * @param subtype
	 *            the subtype to set
	 */
	public void setSubtype(String subtype) {
		this.subtype = subtype;
	}

	/**
	 * @return the subtypeText
	 */
	public String getSubtypeText() {
		return subtypeText;
	}

	/**
	 * @param subtypeText
	 *            the subtypeText to set
	 */
	public void setSubtypeText(String subtypeText) {
		this.subtypeText = subtypeText;
	}

	/**
	 * @return the hospitalAddress
	 */
	public String getHospitalAddress() {
		return hospitalAddress;
	}

	/**
	 * @param hospitalAddress
	 *            the hospitalAddress to set
	 */
	public void setHospitalAddress(String hospitalAddress) {
		this.hospitalAddress = hospitalAddress;
	}

	/**
	 * @return the appointmentType
	 */
	public String getAppointmentType() {
		return appointmentType;
	}

	/**
	 * @param appointmentType
	 *            the appointmentType to set
	 */
	public void setAppointmentType(String appointmentType) {
		this.appointmentType = appointmentType;
	}

	/**
	 * @return the appointmentTypeText
	 */
	public String getAppointmentTypeText() {
		return appointmentTypeText;
	}

	/**
	 * @param appointmentTypeText
	 *            the appointmentTypeText to set
	 */
	public void setAppointmentTypeText(String appointmentTypeText) {
		this.appointmentTypeText = appointmentTypeText;
	}

	/**
	 * @return the organizationUnitText
	 */
	public String getOrganizationUnitText() {
		return organizationUnitText;
	}

	/**
	 * @param organizationUnitText
	 *            the organizationUnitText to set
	 */
	public void setOrganizationUnitText(String organizationUnitText) {
		this.organizationUnitText = organizationUnitText;
	}

	/**
	 * @return the academicRank
	 */
	public String getAcademicRank() {
		return academicRank;
	}

	/**
	 * @param academicRank
	 *            the academicRank to set
	 */
	public void setAcademicRank(String academicRank) {
		this.academicRank = academicRank;
	}

	/**
	 * @return the academicRankText
	 */
	public String getAcademicRankText() {
		return academicRankText;
	}

	/**
	 * @param academicRankText
	 *            the academicRankText to set
	 */
	public void setAcademicRankText(String academicRankText) {
		this.academicRankText = academicRankText;
	}

	/**
	 * @return the clinContractStatus
	 */
	public String getClinContractStatus() {
		return clinContractStatus;
	}

	/**
	 * @param clinContractStatus
	 *            the clinContractStatus to set
	 */
	public void setClinContractStatus(String clinContractStatus) {
		this.clinContractStatus = clinContractStatus;
	}

	/**
	 * @return the clinContractStatusText
	 */
	public String getClinContractStatusText() {
		return clinContractStatusText;
	}

	/**
	 * @param clinContractStatusText
	 *            the clinContractStatusText to set
	 */
	public void setClinContractStatusText(String clinContractStatusText) {
		this.clinContractStatusText = clinContractStatusText;
	}

	/**
	 * 
	 * @param oua
	 */
	public void copyFrom(OtherUniversityAppointment oua) {
		this.startDate = oua.startDate;
		this.endDate = oua.endDate;
		this.recordNumber = oua.recordNumber;
		this.changedOn = oua.changedOn;
		this.subtype = oua.subtype;
		this.subtypeText = oua.subtypeText;
		this.appointmentType = oua.appointmentType;
		this.appointmentTypeText = oua.appointmentTypeText;
		this.organizationUnit = oua.organizationUnit;
		this.organizationUnitText = oua.organizationUnitText;
		this.emeritusOrEmeritaRank = oua.emeritusOrEmeritaRank;
		this.mainIndicator = oua.mainIndicator;
		this.academicRank = oua.academicRank;
		this.academicRankText = oua.academicRankText;
		this.careerPathTitle = oua.careerPathTitle;
		this.hospitalCode = oua.hospitalCode;
		this.hospitalAddress = oua.hospitalAddress;
		this.clinContractStatus = oua.clinContractStatus;
		this.clinContractStatusText = oua.clinContractStatusText;
		this.cpsoNumber = oua.cpsoNumber;
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public String getStartDateAsString() {
		if (startDate == null) {
			return "";
		} else {
			return new SimpleDateFormat("dd-MM-yyyy").format(startDate);
		}
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public String getEndDateAsString() {
		if (endDate == null) {
			return "";
		} else {
			return new SimpleDateFormat("dd-MM-yyyy").format(endDate);
		}
	}

	/**
	 * 
	 * @param ha
	 * @return
	 */
	public boolean isSame(OtherUniversityAppointment ho) {
		if (ho == null || !ho.getClass().equals(this.getClass())) {
			return false;
		}

		boolean b = false;
		try {
			if (ho.getSubtype().equals(subtype)
					&& ho.getStartDate().equals(startDate)
					&& ho.getEndDate().equals(endDate)
					&& ho.getRecordNumber().equals(recordNumber)) {
				b = true;
			}
		} catch (NullPointerException npe) {
			logger.catching(Level.DEBUG, npe);
		}

		return b;
	}

	@Override
	@Transient
	public String getPath() {
		return "otherUnivAppts.otherUnivAppt";
	}

	/**
	 * 
	 * @param ha
	 */
	public void updateFromHris(OtherUniversityAppointment ho) {
		changedOn = ho.getChangedOn();
		subtype = ho.getSubtype();
		subtypeText = ho.getSubtypeText();
		appointmentType = ho.getAppointmentType();
		appointmentTypeText = ho.getAppointmentTypeText();
		organizationUnit = ho.getOrganizationUnit();
		organizationUnitText = ho.getOrganizationUnitText();
		emeritusOrEmeritaRank = ho.getEmeritusOrEmeritaRank();
		mainIndicator = ho.getMainIndicator();
		academicRank = ho.getAcademicRank();
		academicRankText = ho.getAcademicRankText();
		careerPathTitle = ho.getCareerPathTitle();
		hospitalCode = ho.getHospitalCode();
		hospitalAddress = ho.getHospitalAddress();
		clinContractStatus = ho.getClinContractStatus();
		clinContractStatusText = ho.getClinContractStatusText();
		cpsoNumber = ho.getCpsoNumber();

	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public boolean isPrimaryAppointment() {
		return (appointmentTypeText != null)
				&& appointmentTypeText.equals(APPT_TYPE_TXT_PRIMARY);
	}

	@Transient
	public List<String> toAuditRecordStrings() {
		List<String> strings = new ArrayList<String>();

		strings.add("Start Date: " + this.startDate);
		strings.add("End Date: " + this.endDate);
		strings.add("Changed On: " + this.changedOn);
		strings.add("Academic Rank: " + this.academicRankText);
		strings.add("Appointment Type: " + this.appointmentTypeText);
		strings.add("Career Path Title: " + this.careerPathTitle);
		strings.add("Clinical Contract Status: " + this.clinContractStatusText);
		strings.add("CPSO Number: " + this.cpsoNumber);
		strings.add("Emeritus Or Emerita Rank: " + this.emeritusOrEmeritaRank);
		strings.add("Hospital Address: " + this.hospitalAddress);
		strings.add("Hospital Code: " + this.hospitalCode);
		strings.add("Main?: " + this.mainIndicator);
		strings.add("Organization Unit: " + this.organizationUnitText);
		strings.add("Sub Type: " + this.subtypeText);

		return strings;
	}

	@Override
	public void appendRecordStrings(List<String> strings) {
		if (strings != null) {
			strings.addAll(this.toAuditRecordStrings());
		}
	}

	@Transient
	public boolean isCandidateForActiveFaculty() {

		/*(subtype.equals(SUBTYPE_CLINICAL_MD)
				|| subtype.equals(SUBTYPE_ADJUNCT)
				|| subtype.equals(SUBTYPE_CLINICAL_CROSS)
				|| subtype.equals(SUBTYPE_NON_BUDGETARY_CROSS)
				|| subtype.equals(SUBTYPE_STATUS_ONLY) || subtype //NOSONAR
					.equals(SUBTYPE_VISITING_PROF)*/
		
		boolean b = false;
		if (subtype != null
				&& Arrays.asList(SUBTYPE_CLINICAL_MD, SUBTYPE_ADJUNCT,
						SUBTYPE_CLINICAL_CROSS, SUBTYPE_NON_BUDGETARY_CROSS,
						SUBTYPE_STATUS_ONLY, SUBTYPE_VISITING_PROF).contains(
						subtype)) {
			b = true;
		}

		return b;
	}
}
