/**
 * 
 */
package ca.utoronto.med.dc.focus.domain;

import java.io.Serializable;
import java.util.LinkedHashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Index;

/**
 *
 */
@Entity
@Table(name = "SEARCH_CRITERIA_GANG")
public class SearchCriteriaGang extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 3339839140804007777L;
	private Long id;
	private Search search;
	private SearchCriteriaJunction junction;
	private Boolean negated = false;
	private Long displayOrder;
	private Set<SearchCriterion> criteria = new LinkedHashSet<SearchCriterion>();

	public SearchCriteriaGang() {
		junction = SearchCriteriaJunction.ALL;
	}

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "SEARCH_CRITERIA_GANG_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the search
	 */
	@ManyToOne()
	@JoinColumn(name = "SEARCH_ID")
	@ForeignKey(name = "FK__SEARCHCRITERIAGANG__SEARCH")
	@Index(name = "IDX__SEARCHCRITERIAGANG__SEARCH_ID")
	public Search getSearch() {
		return search;
	}

	/**
	 * @param search
	 *            the search to set
	 */
	public void setSearch(Search search) {
		this.search = search;
	}

	/**
	 * @return the junction
	 */
	@Enumerated(EnumType.STRING)
	@Column(name = "JUNCTION")
	public SearchCriteriaJunction getJunction() {
		return junction;
	}

	/**
	 * @param junction
	 *            the junction to set
	 */
	public void setJunction(SearchCriteriaJunction junction) {
		this.junction = junction;
	}

	/**
	 * This gang will be evaluated as a NOT subquery
	 * 
	 * @return
	 */
	@Column(name = "IS_NEGATION")
	public Boolean isNegated() {
		return negated;
	}

	public void setNegated(Boolean negated) {
		this.negated = negated;
	}

	/**
	 * @return the order
	 */
	@Column(name = "DISPLAY_ORDER", nullable = false)
	public Long getDisplayOrder() {
		return displayOrder;
	}

	/**
	 * @param order
	 *            the order to set
	 */
	public void setDisplayOrder(Long order) {
		this.displayOrder = order;
	}

	/**
	 * @return the criteria
	 */
	@OneToMany(mappedBy = "searchCriteriaGang", cascade = {
			CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH,
			CascadeType.REMOVE })
	public Set<SearchCriterion> getCriteria() {
		return criteria;
	}

	/**
	 * @param criteria
	 *            the criteria to set
	 */
	public void setCriteria(Set<SearchCriterion> criteria) {
		this.criteria = criteria;
	}

	public void addCriterion(SearchCriterion criterion) {
		criterion.setSearchCriteriaGang(this);
		this.criteria.add(criterion);
	}

	@Override
	public String toString() {
		String newLine = System.getProperty("line.separator");
		StringBuilder sb = new StringBuilder(this.getClass().getSimpleName())
				.append(" {").append(newLine);
		sb.append("id: ").append(id).append(newLine);
		sb.append("displayOrder: ").append(displayOrder).append(newLine);
		sb.append("junction: ").append(junction).append(newLine);
		sb.append("negated?: ").append(negated).append(newLine);
		sb.append("}");
		return sb.toString();
	}
}
