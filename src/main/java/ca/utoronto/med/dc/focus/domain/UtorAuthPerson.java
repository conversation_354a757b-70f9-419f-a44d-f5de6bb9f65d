package ca.utoronto.med.dc.focus.domain;

import java.io.Serializable;

public class Utor<PERSON>uth<PERSON>erson implements Serializable {
	private static final long serialVersionUID = 3511827659146776039L;
	private Long personnelNumber;
	private String utorId;
	private String telephone;
	private String emailAddress;

	public UtorAuthPerson(Long personnelNumber) {
		this.personnelNumber = personnelNumber;
		valid();
	}

	public Long getPersonnelNumber() {
		return personnelNumber;
	}

	public void setPersonnelNumber(Long personnelNumber) {
		this.personnelNumber = personnelNumber;
	}

	public String getUtorId() {
		return utorId;
	}

	public void setUtorId(String utorId) {
		this.utorId = utorId;
	}

	public String getTelephone() {
		return telephone;
	}

	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	public String getEmailAddress() {
		return emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	private void valid() {
		if (personnelNumber == null || personnelNumber < 1) {
			throw new IllegalArgumentException(
					"Personnel number is not valid: " + personnelNumber);
		}
	}

	@Override
	public String toString() {
		return "UtorAuthPerson [personnelNumber=" + personnelNumber
				+ ", utorId=" + utorId + ", telephone=" + telephone
				+ ", emailAddress=" + emailAddress + "]";
	}
}
