package ca.utoronto.med.dc.focus.domain;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Index;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RelationTargetAuditMode;

import ca.utoronto.med.dc.common.util.Utils;

@Entity
@Table(name = "TELEPHONE")
@Audited
public class Telephone extends BaseEntity {

	private Long id;
	private Date startDate;
	private Date endDate;
	private Date changedOn;

	private String phoneType;
	private String areaCode;
	private String phoneNumber;
	private String extension;
	private Boolean longDistance;
	private Boolean telPrimary;
	private String telecomOrg;

	private Profile profile;
	private FieldDef customField;
	private PhoneBookTelephone phoneBookTelephone;

	private Set<CommPref> commPrefList = new HashSet<CommPref>();

	/**
	 * 
	 */
	public Telephone() {
		customField = null;
		startDate = new Date();
		endDate = Utils.getMaxDate();
	}

	/**
	 * 
	 * @param phone
	 * @param profileIn
	 * @param fd
	 */
	public Telephone(Telephone phone, Profile profileIn, FieldDef fd) {
		this.profile = profileIn;
		customField = fd;
		phoneNumber = phone.getPhoneNumber();
		startDate = new Date();
		endDate = Utils.getMaxDate();
	}

	/**
	 * 
	 * @param profileId
	 * @param fieldId
	 */
	public Telephone(Long profileId, Long fieldId) {
		profile = new Profile(profileId);
		customField = new FieldDef(fieldId);
		startDate = new Date();
		endDate = Utils.getMaxDate();
	}

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "TELEPHONE_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the phoneType
	 */
	public String getPhoneType() {
		return phoneType;
	}

	/**
	 * @param phoneType
	 *            the phoneType to set
	 */
	public void setPhoneType(String phoneType) {
		this.phoneType = phoneType;
	}

	/**
	 * @return the areaCode
	 */
	public String getAreaCode() {
		return areaCode;
	}

	/**
	 * @param areaCode
	 *            the areaCode to set
	 */
	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	/**
	 * @return the phoneNumber
	 */
	public String getPhoneNumber() {
		return phoneNumber;
	}

	/**
	 * @param phoneNumber
	 *            the phoneNumber to set
	 */
	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	/**
	 * @return the extension
	 */
	public String getExtension() {
		return extension;
	}

	/**
	 * @param extension
	 *            the extension to set
	 */
	public void setExtension(String extension) {
		this.extension = extension;
	}

	/**
	 * @return the longDistance
	 */
	public Boolean getLongDistance() {
		return longDistance;
	}

	/**
	 * @param longDistance
	 *            the longDistance to set
	 */
	public void setLongDistance(Boolean longDistance) {
		this.longDistance = longDistance;
	}

	/**
	 * @return the telPrimary
	 */
	public Boolean getTelPrimary() {
		return telPrimary;
	}

	/**
	 * @param telPrimary
	 *            the telPrimary to set
	 */
	public void setTelPrimary(Boolean telPrimary) {
		this.telPrimary = telPrimary;
	}

	/**
	 * @return the telecomOrg
	 */
	public String getTelecomOrg() {
		return telecomOrg;
	}

	/**
	 * @param telecomOrg
	 *            the telecomOrg to set
	 */
	public void setTelecomOrg(String telecomOrg) {
		this.telecomOrg = telecomOrg;
	}

	/**
	 * @return the profile
	 */
	@ManyToOne(targetEntity = ca.utoronto.med.dc.focus.domain.Profile.class, fetch = FetchType.LAZY)
	@JoinColumn(name = "PROFILE_ID", nullable = false)
	@org.hibernate.annotations.ForeignKey(name = "FK_TELEPHONE__PROFILE")
	@org.hibernate.annotations.Index(name = "IDX_PROFILE_ID")
	public Profile getProfile() {
		return profile;
	}

	/**
	 * @param profile
	 *            the profile to set
	 */
	public void setProfile(Profile profile) {
		this.profile = profile;
	}

	/**
	 * 
	 * @return
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CUSTOMFIELD_ID")
	@ForeignKey(name = "FK__TELEPHONE__FIELDDEF")
	@Index(name = "IDX_TELEPHONE__FIELDDEF_ID")
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public FieldDef getCustomField() {
		return customField;
	}

	/**
	 * 
	 * @param customFieldIn
	 */
	public void setCustomField(FieldDef customFieldIn) {
		customField = customFieldIn;
	}

	public void update(Telephone phoneIn) {
		phoneNumber = phoneIn.getPhoneNumber();
	}

	/**
	 * 
	 */
	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" id: " + id + newLine);
		result.append(" phoneType: " + phoneType + newLine);
		result.append(" areaCode: " + areaCode + newLine);
		result.append(" phoneNumber: " + phoneNumber + newLine);
		result.append(" extension: " + extension + newLine);
		result.append(" longDistance: " + longDistance + newLine);
		result.append(" telPrimary: " + telPrimary + newLine);
		result.append(" telecomOrg: " + telecomOrg + newLine);
		result.append(" startDate: " + startDate + newLine);
		result.append(" endDate: " + endDate + newLine);

		result.append("}");

		return result.toString();
	}

	/**
	 * @return the startDate
	 */
	@Temporal(TemporalType.DATE)
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * @param startDate
	 *            the startDate to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * @return the endDate
	 */
	@Temporal(TemporalType.DATE)
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * @param endDate
	 *            the endDate to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/**
	 * @return the changedOn
	 */
	@Temporal(TemporalType.DATE)
	public Date getChangedOn() {
		return changedOn;
	}

	/**
	 * @param changedOn
	 *            the changedOn to set
	 */
	public void setChangedOn(Date changedOn) {
		this.changedOn = changedOn;
	}
	
	@OneToOne(mappedBy="telephone", cascade={CascadeType.MERGE, CascadeType.REMOVE, CascadeType.PERSIST}, optional=true)
	public PhoneBookTelephone getPhoneBookTelephone() {
		return phoneBookTelephone;
	}
	
	public void setPhoneBookTelephone(PhoneBookTelephone phoneBookTelephone) {
		this.phoneBookTelephone = phoneBookTelephone;
	}

	/**
	 * @return the commPrefList
	 */
	@OneToMany(mappedBy = "telephone")
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public Set<CommPref> getCommPrefList() {
		return commPrefList;
	}

	/**
	 * @param commPrefList
	 *            the commPrefList to set
	 */
	public void setCommPrefList(Set<CommPref> commPrefList) {
		this.commPrefList = commPrefList;
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public boolean isPhoneMarkedSensitive() {
		if (customField == null) {
			// HRIS addresses will have a null customField
			// We mark those addresses as non-sensitive
			return false;
		} else {
			return customField.isSensitiveDataField();
		}
	}
	
	@Transient
	public boolean isPrimaryEligible() {
		boolean eligible = true;
		if (phoneNumber == null || phoneNumber.isEmpty()
				|| isPhoneMarkedSensitive()) {
			eligible = false;
		}
		return eligible;
	}

	@Transient
	public Object asJson() {
		Map<String, Object> jsonMap = new HashMap<String, Object>();
		jsonMap.put("telephoneId", id);
		jsonMap.put("phoneNumber", phoneNumber);
		jsonMap.put("primaryEligible", isPrimaryEligible());
		return (Object) jsonMap;
	}
	
	@Transient
	@Override
	public String getPath() {
		return "telephones.telephone";
	}

	@Override
	public void appendRecordStrings(List<String> strings) {
		strings.add("Start Date: " + startDate);
		strings.add("End Date: " + endDate);
		strings.add("Phone Number: " + phoneNumber);
	}
}
