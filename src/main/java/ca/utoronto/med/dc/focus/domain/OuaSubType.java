package ca.utoronto.med.dc.focus.domain;

public enum OuaSubType {

    SUBTYPE_PRIMARY(1, OtherUniversityAppointment.SUBTYPE_PRIMARY),
    SUBTYPE_CLINICAL_MD(2, OtherUniversityAppointment.SUBTYPE_CLINICAL_MD),
    SUBTYPE_CLINICAL_CROSS(3, OtherUniversityAppointment.SUBTYPE_CLINICAL_CROSS),
    SUBTYPE_STATUS_ONLY(4, OtherUniversityAppointment.SUBTYPE_STATUS_ONLY),
    SUBTYPE_ADJUNCT(5, OtherUniversityAppointment.SUBTYPE_ADJUNCT),
    SUBTYPE_NON_BUDGETARY_CROSS(6, OtherUniversityAppointment.SUBTYPE_NON_BUDGETARY_CROSS),
    SUBTYPE_EMERITUS_EMERITA(7, OtherUniversityAppointment.SUBTYPE_EMERITUS_EMERITA),
    SUBTYPE_VISITING_PROF(8, OtherUniversityAppointment.SUBTYPE_VISITING_PROF);

    private final int value;
    private final String name;

    private OuaSubType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static OuaSubType getOuaSubTypeByName(String name) {
        for (OuaSubType ouaSubType : OuaSubType.values()) {
            if (ouaSubType.getName().equals(name)) {
                return ouaSubType;
            }
        }
        return null;
    }
}
