package ca.utoronto.med.dc.focus.domain;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "USER_CONTAINERDEF")
@IdClass(UserContainerDefStateId.class)
public class UserContainerDefState {

	private ContainerDef container;
	private AuthUser authUser;
	private boolean visible;

	public UserContainerDefState() {
	}

	public UserContainerDefState(ContainerDef c, AuthUser u, boolean visible) {
		this.container = c;
		this.authUser = u;
		this.visible = visible;
	}

	@Id
	@ManyToOne
	@JoinColumn(name = "CONTAINERDEF_ID", referencedColumnName = "CONTAINERDEF_ID", nullable = false)
	public ContainerDef getContainer() {
		return container;
	}

	/**
	 * @param container
	 *            the container to set
	 */
	public void setContainer(ContainerDef container) {
		this.container = container;
	}

	@Id
	@ManyToOne
	@JoinColumn(name = "users_id", referencedColumnName = "users_id", nullable = false)
	public AuthUser getAuthUser() {
		return authUser;
	}

	/**
	 * @param user
	 *            the user to set
	 */
	public void setAuthUser(AuthUser user) {
		this.authUser = user;
	}

	/**
	 * See stackoverflow question 197045 re. columnDefinition
	 * 
	 * @return the collapsed
	 */
	@Column(name = "VISIBLE", columnDefinition = "TINYINT DEFAULT 1", nullable = false)
	public boolean isVisible() {
		return visible;
	}

	/**
	 * @param collapsed
	 *            the collapsed to set
	 */
	public void setVisible(boolean visible) {
		this.visible = visible;
	}

}
