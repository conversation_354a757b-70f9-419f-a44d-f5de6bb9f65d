package ca.utoronto.med.dc.focus.domain;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import ca.utoronto.med.dc.focus.domain.Telephone;

@Entity
@Table(name = PhoneBookTelephone.TABLENAME)
public class PhoneBookTelephone implements Serializable {
	private static final long serialVersionUID = 1L;
	public static final String TABLENAME = "PHONEBOOK_TELEPHONE";
	public static final String COLUMN_ID = "PHONEBOOK_TELEPHONE_ID";

	private Long id;
	private Long alphaId;
	private String departmentName;
	private Telephone telephone;
	private String type;
	private Boolean hrisOnly;
	public static final String TELEPHONE_EXT_LABEL = " ext. ";

	public PhoneBookTelephone() {
	}

	public PhoneBookTelephone(String departmentName, String type,
			boolean publishable, Profile profile, String phoneNumber) {
		this();
		this.departmentName = departmentName;
		this.type = type;
		this.hrisOnly = publishable;
		this.telephone = new Telephone();
		this.telephone.setProfile(profile);
		this.telephone.setPhoneNumber(phoneNumber);
		this.telephone.setPhoneBookTelephone(this);
	}

	@Id
	@Column(name=COLUMN_ID)
	@GeneratedValue(strategy=GenerationType.AUTO)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	@Column
	public Long getAlphaId() {
		return alphaId;
	}
	
	public void setAlphaId(Long alphaId) {
		this.alphaId = alphaId;
	}
	
	@Column(name="DEPARTMENT_NAME")
	public String getDepartmentName() {
		return departmentName;
	}

	public void setDepartmentName(String departmentName) {
		this.departmentName = departmentName;
	}	
	
	@OneToOne(cascade = {CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REMOVE})
	@JoinColumn(name="TELEPHONE_ID", nullable = false)
	public Telephone getTelephone() {
		return telephone;
	}

	public void setTelephone(Telephone telephone) {
		this.telephone = telephone;
	}

	@Column(name="TELEPHONE_TYPE")
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name="HRIS_ONLY")
	public Boolean isHrisOnly() {
		return hrisOnly;
	}

	public void setHrisOnly(Boolean publishable) {
		this.hrisOnly = publishable;
	}

	private String nullSafeTelephone() {
		return this.telephone == null ? "NULL" : this.telephone
				.getPhoneNumber();
	}
	

	@Override
	public String toString() {
		return "PhoneBookTelephone [id=" + id + ", telephone="
				+ nullSafeTelephone() + ", type=" + type + ", publishable="
				+ hrisOnly + ", phoneBookDepartment="
				+ departmentName + "]";
	}

	public static String mergePhoneNumberAndExtension(String phoneNumber, String extensionNumber) {
		StringBuilder sb = new StringBuilder(phoneNumber);
		if(extensionNumber != null && !extensionNumber.isEmpty()) {
			sb.append(PhoneBookTelephone.TELEPHONE_EXT_LABEL).append(extensionNumber);
		}
		return sb.toString();
	}
	
	@Override
	public boolean equals(Object obj) {
		if (obj instanceof PhoneBookTelephone) {
			PhoneBookTelephone other = (PhoneBookTelephone) obj;
			if (departmentName != null && other.departmentName != null
					&& type != null && other.type != null) {
				return departmentName.equals(other.departmentName)
						&& type.equals(other.type);
			}
		}
		return false;
	}
	
	@Override
	public int hashCode() {
		return (departmentName + "___" + type).hashCode();
	}

}
