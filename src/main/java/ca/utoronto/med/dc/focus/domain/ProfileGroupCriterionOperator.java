package ca.utoronto.med.dc.focus.domain;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.criterion.Criterion;
import org.hibernate.criterion.MatchMode;
import org.hibernate.criterion.Restrictions;
import org.slf4j.ext.XLogger;
import org.slf4j.ext.XLoggerFactory;

@Entity
@Table(name = "PROFILE_GROUP_CRIT_OP")
public class ProfileGroupCriterionOperator implements Serializable {
	XLogger logger = XLoggerFactory.getXLogger(this.getClass());
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1476930043890236923L;

	public static final String NAME_IS_NOT_NULL = "isNotNull";
	public static final String NAME_IS_NULL = "isNull";
	public static final String NAME_IS_NOT_EQUALS = "ne";
	public static final String NAME_IS_EQUALS = "eq";
	public static final String NAME_IS_LIKE = "iLike";
	public static final String CRIT_OP_BEGINS = "Begins";
	public static final String CRIT_OP_ENDS = "Ends";

	private Long id;
	private String htmlEntity;
	private String restrictionOpName;
	private String description;
	private Set<FieldDataType> fieldDataTypes = new HashSet<FieldDataType>();
	private int displayWeight;

	public ProfileGroupCriterionOperator() {
	}

	public ProfileGroupCriterionOperator(Long id) {
		this();
		this.id = id;
	}

	@Id
	@Column(name = "PROFILEGROUPCRITOP_ID")
	@GeneratedValue(strategy = GenerationType.AUTO)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = "HTML_ENTITY")
	public String getHtmlEntity() {
		return htmlEntity;
	}

	public void setHtmlEntity(String htmlEntity) {
		this.htmlEntity = htmlEntity;
	}

	@Column(name = "RESTRICTION_OP_NAME")
	public String getRestrictionOpName() {
		return restrictionOpName;
	}

	public void setRestrictionOpName(String restrictionOpName) {
		this.restrictionOpName = restrictionOpName;
	}

	@Column(name = "DESCRIPTION")
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	/**
	 * For display order. Higher values "sink" in the list
	 * 
	 * @return
	 */
	@Column(name = "DISPLAY_WEIGHT")
	public int getDisplayWeight() {
		return displayWeight;
	}

	public void setDisplayWeight(int displayWeight) {
		this.displayWeight = displayWeight;
	}

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "OPERATOR_DATATYPE", joinColumns = { @JoinColumn(name = "PROFILEGROUPCRITOP_ID") }, inverseJoinColumns = { @JoinColumn(name = "FIELDDATATYPE_ID") })
	public Set<FieldDataType> getFieldDataTypes() {
		return fieldDataTypes;
	}

	public void setFieldDataTypes(Set<FieldDataType> fieldDataTypes) {
		this.fieldDataTypes = fieldDataTypes;
	}

	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" id: " + id + newLine);
		result.append(" htmlEntity: " + htmlEntity + newLine);
		result.append(" restrictionOpName: " + restrictionOpName + newLine);
		result.append(" description: " + description + newLine);

		result.append("}");

		return result.toString();

	}

	/**
	 * Return a Hibernate Restrictions instance whose type is determined by the value of the operator. 
	 * 
	 * @param op the operator instance
	 * @param propertyName the fully qualified object path
	 * @param valueObject the search term
	 * @return
	 */
	public static Criterion asRestrictionsObject(ProfileGroupCriterionOperator op, String propertyName,
			Object valueObject) {
		//Default
		Criterion r = Restrictions.eq(propertyName, valueObject);
		if (op.getRestrictionOpName().equals(NAME_IS_NOT_EQUALS)) {
			r = Restrictions.ne(propertyName, valueObject);
		} else if ("lt".equals(op.getRestrictionOpName())) {
			r = Restrictions.lt(propertyName, valueObject);
		} else if ("gt".equals(op.getRestrictionOpName())) {
			r = Restrictions.gt(propertyName, valueObject);
		} else if ("ge".equals(op.getRestrictionOpName())) {
			r = Restrictions.ge(propertyName, valueObject);
		} else if ("le".equals(op.getRestrictionOpName())) {
			r = Restrictions.le(propertyName, valueObject);
		} else if (NAME_IS_NULL.equals(op.getRestrictionOpName())) {
			r = Restrictions.isNull(propertyName);
		} else if (NAME_IS_NOT_NULL.equals(op.getRestrictionOpName())) {
			r = Restrictions.isNotNull(propertyName);
		} else if (NAME_IS_LIKE.equals(op.getRestrictionOpName())) {
			if (CRIT_OP_BEGINS.equals(op.getHtmlEntity())) {
				r = Restrictions.ilike(propertyName, (String) valueObject,
						MatchMode.START);
			} else if (CRIT_OP_ENDS.equals(op.getHtmlEntity())) {
				r = Restrictions.ilike(propertyName, (String) valueObject,
						MatchMode.END);
			} else {
				r = Restrictions.ilike(propertyName, (String) valueObject,
						MatchMode.ANYWHERE);
			}
		}
		return r;
	}

	/**
	 * When parsing a value for this criterion, do not throw an error unless
	 * this method returns <code>true</code>.
	 * 
	 * @return
	 */
	public static boolean requiresValue(String restrictionOpName) {
		if (restrictionOpName.equals(NAME_IS_NULL)
				|| restrictionOpName.equals(NAME_IS_NOT_NULL)) {
			return false;
		}
		return true;
	}

	@Transient
	public boolean isNullOperator() {
		if (restrictionOpName.equals(NAME_IS_NULL)) {
			return true;
		}
		return false;
	}

	@Transient
	public Boolean isNotEquals() {
		if (restrictionOpName.equals(NAME_IS_NOT_EQUALS)) {
			return true;
		}
		return false;
	}
}
