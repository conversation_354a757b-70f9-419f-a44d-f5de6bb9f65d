package ca.utoronto.med.dc.focus.domain;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Pattern;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Index;

import ca.utoronto.med.dc.common.exception.FocusException;
import ca.utoronto.med.dc.focus.dto.ProfileDTO;

@Entity
@Table(name = "STATIC_GROUP", uniqueConstraints = @UniqueConstraint(columnNames = {
		"AUTHUSER_ID", "NAME" }, name = "UQ__STATICGROUP_ID__AUTHUSER_ID__NAME"))
public class StaticGroup extends BaseEntity implements Serializable {
	private static final long serialVersionUID = -246230693202375892L;

	private Long id;

	@Pattern(regexp = "[a-zA-Z_0-9-\\(\\)\\[\\]' ]{1,60}", message = "Group Name must be between 1 and 60 characters. Only letters, numbers, spaces, square brackets, parentheses, quotation, underscores and hyphens are allowed.")
	private String name;

	private AuthUser authUser;
	private Department department;
	private int deptVisibility; // 0 = private; 1 = public
	private Set<Profile> profiles = new HashSet<Profile>();

	// Transient
	private List<ProfileDTO> profileDTOs = new ArrayList<ProfileDTO>();
	private String authUserName;
	private int readOnly = 0;

	public static final int PROFILE_GROUP_VISIBILITY_ALL = -1;

	public static final int PROFILE_GROUP_VISIBILITY_PUBLIC = 1;

	// is ProfileGroup visible to other Department users?
	public static final int PROFILE_GROUP_VISIBILITY_PRIVATE = 0;

	public StaticGroup() {
		deptVisibility = 0;
	}

	/**
	 * Constructor with WebRequest parameterMap
	 * 
	 * @param paramMap
	 */
	public StaticGroup(Map<String, String[]> paramMap) {
		if (paramMap.containsKey("profileGroupId")) {
			try {
				Long pgId = Long.parseLong(paramMap.get("profileGroupId")[0]);
				this.setId(pgId);
			} catch (NumberFormatException nfe) {
				throw new FocusException(
						"Could not determine id of Profile Group");
			}
		}
		if (paramMap.containsKey("name")) {
			// TODO validate
			this.setName(paramMap.get("name")[0]);
		}
		if (paramMap.containsKey("profiles")) {
			try {
				for (String idStr : paramMap.get("profiles")) {
					Long pId = Long.parseLong(idStr);
					Profile p = new Profile(pId);
					this.addProfile(p);
				}
			} catch (NumberFormatException nfe) {
				throw new FocusException("Could not determine id of Profile");
			}
		}

	}

	/**
	 * Transient use of StaticGroup
	 * 
	 * @param profileDtos
	 */
	public StaticGroup(List<ProfileDTO> profileDtos) {
		this();
		this.profileDTOs = profileDtos;
	}

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "STATICGROUP_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the name
	 */
	@Column(name = "NAME", nullable = false)
	public String getName() {
		return name;
	}

	/**
	 * @param name
	 *            the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * @return the owner
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "AUTHUSER_ID", nullable = false)
	@ForeignKey(name = "FK__STATIC_GROUP__USERS")
	@Index(name = "IDX__STATIC_GROUP__USERS")
	public AuthUser getAuthUser() {
		return authUser;
	}

	/**
	 * @param owner
	 *            the owner to set
	 */
	public void setAuthUser(AuthUser authUser) {
		this.authUser = authUser;
	}

	/**
	 * @return the department
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DEPARTMENT_ID", nullable = false)
	@ForeignKey(name = "FK__STATIC_GROUP__DEPARTMENT")
	@Index(name = "IDX__STATIC_GROUP__DEPARTMENT_ID")
	public Department getDepartment() {
		return department;
	}

	/**
	 * @param department
	 *            the department to set
	 */
	public void setDepartment(Department department) {
		this.department = department;
	}

	@Column(name = "DEPT_VISIBILITY", nullable = false)
	public int getDeptVisibility() {
		return deptVisibility;
	}

	public void setDeptVisibility(int deptVisibility) {
		this.deptVisibility = deptVisibility;
	}

	/**
	 * @return the profiles
	 */
	@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.MERGE })
	@JoinTable(name = "STATICGROUP_PROFILE", joinColumns = { @JoinColumn(name = "STATICGROUP_ID") }, inverseJoinColumns = { @JoinColumn(name = "PROFILE_ID") })
	@ForeignKey(name = "FK__STATICGROUP__PROFILE_ID", inverseName = "FK__PROFILE__STATICGROUP_ID")
	public Set<Profile> getProfiles() {
		return profiles;
	}

	/**
	 * @param profiles
	 *            the profiles to set
	 */
	public void setProfiles(Set<Profile> profiles) {
		this.profiles = profiles;
	}

	public void addProfile(Profile p) {
		profiles.add(p);
		p.addStaticGroup(this);
	}

	public void removeProfile(Profile p) {
		profiles.remove(p);
	}

	@Transient
	public String getUpdatedOnFormatted() {
		String dateStr = "";
		if (getUpdatedOn() != null) {
			SimpleDateFormat df = new SimpleDateFormat("MMMM dd, yyyy");
			dateStr = df.format(getUpdatedOn());
		}
		return dateStr;
	}

	@Transient
	public List<ProfileDTO> getProfileDTOs() {
		return profileDTOs;
	}

	public void setProfileDTOs(List<ProfileDTO> profileDtos) {
		this.profileDTOs = profileDtos;
	}

	@Transient
	public boolean isDepartmental() {
		return deptVisibility == StaticGroup.PROFILE_GROUP_VISIBILITY_PUBLIC;
	}

	@Transient
	public boolean isPrivate() {
		return deptVisibility == StaticGroup.PROFILE_GROUP_VISIBILITY_PRIVATE;
	}

	/**
	 * @deprecated
	 * @return
	 */
	@Deprecated
	@Transient
	public String getViewButtonText() {
		return "View Members";
	}

	/**
	 * @deprecated
	 * @return
	 */
	@Deprecated
	@Transient
	public String getEditButtonText() {
		return "Edit Members";
	}

	
	@Transient
	public String getAuthUserName() {
		return authUserName;
	}

	public void setAuthUserName(String authUserName) {
		this.authUserName = authUserName;
	}

	@Transient
	public int getReadOnly() {
		return readOnly;
	}

	public void setReadOnly(int readOnly) {
		this.readOnly = readOnly;
	}

	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" id: " + id + newLine);
		result.append(" name: ").append(name).append(newLine);
		result.append(" departmental: ").append(deptVisibility)
				.append(newLine);

		result.append("}");

		return result.toString();
	}

	@Transient
	public Object toJson() {
		Map<String, Object> json = new HashMap<String, Object>();
		json.put("id", id);
		json.put("name", name);
		json.put("updatedOn", getUpdatedOnFormatted());
		json.put("owner", getAuthUserName());
		json.put("departmental", isDepartmental());
		json.put("viewButtonText", getViewButtonText());
		json.put("editButtonText", getEditButtonText());
		return json;
	}

}