package ca.utoronto.med.dc.focus.domain;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Index;
import org.hibernate.envers.Audited;
import org.slf4j.ext.XLogger;
import org.slf4j.ext.XLogger.Level;
import org.slf4j.ext.XLoggerFactory;

@Entity
@Table(name = "PROFDESIGNATION")
@Audited
public class ProfDesignation extends BaseEntity {
	private XLogger logger = XLoggerFactory.getXLogger(this.getClass()
			.getName());
	
	private Long id;
	private Date startDate;
	private Date endDate;
	private Long recordNumber;

	private Date changedOn;
	private String designationCode;
	private String designationCodeText;
	private Long yearConferred;

	private Education education;

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "PROFDESIGNATION_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the startDate
	 */
	@Temporal(TemporalType.DATE)
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * @param startDate
	 *            the startDate to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * @return the endDate
	 */
	@Temporal(TemporalType.DATE)
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * @param endDate
	 *            the endDate to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/**
	 * @return the recordNumber
	 */
	@Column(name = "RECORD_NUMBER"/* , unique = true */, nullable = false)
	public Long getRecordNumber() {
		return recordNumber;
	}

	/**
	 * @param recordNumber
	 *            the recordNumber to set
	 */
	public void setRecordNumber(Long recordNumber) {
		this.recordNumber = recordNumber;
	}

	/**
	 * @return the designationCode
	 */
	public String getDesignationCode() {
		return designationCode;
	}

	/**
	 * @param designationCode
	 *            the designationCode to set
	 */
	public void setDesignationCode(String designationCode) {
		this.designationCode = designationCode;
	}

	/**
	 * @return the designationCodeText
	 */
	public String getDesignationCodeText() {
		return designationCodeText;
	}

	/**
	 * @param designationCodeText
	 *            the designationCodeText to set
	 */
	public void setDesignationCodeText(String designationCodeText) {
		this.designationCodeText = designationCodeText;
	}

	/**
	 * @return the yearConferred
	 */
	public Long getYearConferred() {
		return yearConferred;
	}

	/**
	 * @param yearConferred
	 *            the yearConferred to set
	 */
	public void setYearConferred(Long yearConferred) {
		this.yearConferred = yearConferred;
	}

	/**
	 * @return the changedOn
	 */
	@Temporal(TemporalType.DATE)
	public Date getChangedOn() {
		return changedOn;
	}

	/**
	 * @param changedOn
	 *            the changedOn to set
	 */
	public void setChangedOn(Date changedOn) {
		this.changedOn = changedOn;
	}

	/**
	 * @return the education
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EDUCATION_ID", nullable = false)
	@ForeignKey(name = "FK__PROFDESIGNATION__EDUCATION")
	@Index(name = "IDX__PROFDESIGNATION__EDUCATION_ID")
	public Education getEducation() {
		return education;
	}

	/**
	 * @param education
	 *            the education to set
	 */
	public void setEducation(Education education) {
		this.education = education;
	}

	/**
	 * 
	 */
	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" id: " + id + newLine);
		result.append(" designation Code: " + designationCode + newLine);
		result.append(" yearConferred: " + yearConferred + newLine);
		result.append(" changedOn: " + changedOn + newLine);

		result.append("}");

		return result.toString();
	}

	/**
	 * 
	 * @param pd
	 */
	public void copyFrom(ProfDesignation pd) {
		this.startDate = pd.startDate;
		this.endDate = pd.endDate;
		this.recordNumber = pd.recordNumber;
		this.changedOn = pd.changedOn;
		this.designationCode = pd.designationCode;
		this.designationCodeText = pd.designationCodeText;
		this.yearConferred = pd.yearConferred;
	}

	/**
	 * 
	 * @param ha
	 * @return
	 */
	public boolean isSame(ProfDesignation hp) {
		if (hp == null || !hp.getClass().equals(this.getClass())) {
			return false;
		}
		
		try {
			if (hp.getStartDate().equals(startDate)
					&& hp.getEndDate().equals(endDate)
					&& hp.getRecordNumber().equals(recordNumber)) {
				return true;
			}
		} catch (NullPointerException e) {
			logger.catching(Level.DEBUG, e);
		}

		return false;

	}

	/**
	 * 
	 * @param ha
	 */
	public void updateFromHris(ProfDesignation hp) {
		changedOn = hp.getChangedOn();
		designationCode = hp.getDesignationCode();
		designationCodeText = hp.getDesignationCodeText();
		yearConferred = hp.getYearConferred();
	}

	@Transient
	@Override
	public String getPath() {
		return "education.profDesignation";
	}

	@Override
	public void appendRecordStrings(List<String> strings) {
		strings.add("Start Date: " + startDate);
		strings.add("End Date: " + endDate);
		strings.add("Desig Code: " + designationCodeText);
		strings.add("Year Conferred: " + yearConferred);
	}
}
