package ca.utoronto.med.dc.focus.domain;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity
@Table(name = "UTORAUTH_DATA")
public class UtorAuthData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7017002344336652776L;
	private Long id;
	private String emailAddress;
	private String utorId;
	private String telephone;
	private Profile profile;

	public UtorAuthData() {
	}

	@Id
	@GeneratedValue(strategy=GenerationType.AUTO)
	@Column(name = "UTORAUTH_DATA_ID")
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name="EMAIL_ADDRESS")
	public String getEmailAddress() {
		return emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}
		
	/**
	 * @return the utorId
	 */
	@Column(name="UTORID")
	public String getUtorId() {
		return utorId;
	}

	/**
	 * @param utorId the utorId to set
	 */
	public void setUtorId(String utorId) {
		this.utorId = utorId;
	}

	/**
	 * @return the telephone
	 */
	@Column(name="TELEPHONE")
	public String getTelephone() {
		return telephone;
	}

	/**
	 * @param telephone the telephone to set
	 */
	public void setTelephone(String telephone) {
		this.telephone = telephone;
	}

	@OneToOne(mappedBy = "utorAuthData")
	public Profile getProfile() {
		return profile;
	}

	public void setProfile(Profile profile) {
		this.profile = profile;
	}

	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName()).append(" Object {")
				.append(newLine);
		result.append(" id: ").append(id).append(newLine);
		result.append(" emailAddress: ").append(emailAddress).append(newLine);
		result.append(" utorid: ").append(utorId).append(newLine);		
		result.append(" telephone: ").append(telephone).append(newLine);
		
		result.append("}");

		return result.toString();
	}

}
