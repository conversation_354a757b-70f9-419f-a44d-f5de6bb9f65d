package ca.utoronto.med.dc.focus.domain;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Index;

@Entity
@Table(name = "WEBCV_BRIDGE")
public class WebCVBridge {
	private Long id;
	private Profile profile;
	private Long webcvId;
	private Date createdOn;

	/**
	 * 
	 */
	public WebCVBridge() {
	}

	/**
	 * @param profile
	 * @param webcvId
	 * @param createdOn
	 */
	public WebCVBridge(Profile profile, Long webcvId) {
		super();
		this.profile = profile;
		this.webcvId = webcvId;
		this.createdOn = new Date();
	}

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "WEBCVBRIDGE_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the profile
	 */
	@OneToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "PROFILE_ID", nullable = false)
	@Index(name = "IDX_WEBCV_BRIDGE_PROFILE")
	@ForeignKey(name = "FK_WEBCV_BRIDGE_PROFILE")
	public Profile getProfile() {
		return profile;
	}

	/**
	 * @param profile
	 *            the profile to set
	 */
	public void setProfile(Profile profile) {
		this.profile = profile;
	}

	/**
	 * @return the webcvId
	 */
	@Column(name = "WEBCV_ID", nullable = false, unique = true)
	public Long getWebcvId() {
		return webcvId;
	}

	/**
	 * @param webcvId
	 *            the webcvId to set
	 */
	public void setWebcvId(Long webcvId) {
		this.webcvId = webcvId;
	}

	/**
	 * @return the createdOn
	 */
	@Temporal(TemporalType.DATE)
	@Column(name = "CREATED_ON")
	public Date getCreatedOn() {
		return createdOn;
	}

	/**
	 * @param createdOn
	 *            the createdOn to set
	 */
	public void setCreatedOn(Date createdOn) {
		this.createdOn = createdOn;
	}

	/**
	 * 
	 */
	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" WebCV IDd: " + webcvId + newLine);
		result.append(" CreatedOn: " + createdOn + newLine);
		result.append("}");

		return result.toString();
	}

}
