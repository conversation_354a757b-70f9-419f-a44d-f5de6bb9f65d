/**
 * 
 */
package ca.utoronto.med.dc.focus.domain;

import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import ca.utoronto.med.dc.common.exception.FocusServiceException;
import ca.utoronto.med.dc.common.util.Constants;
import ca.utoronto.med.dc.common.util.Utils;
import ca.utoronto.med.dc.focus.persistence.audit.AuditMe;

@Entity
@Table(name = "PERSONALDATA")
public class PersonalData extends BaseEntity implements Auditable {
	private Long id;
	private Long personnelNumber;
	private Date startDate;
	private Date endDate;
	private Long recordNumber;
	@AuditMe
	private Date changedOn;
	@AuditMe
	private String formOfAddress;
	@AuditMe
	private String lastName;
	@AuditMe
	private String firstName;
	@AuditMe
	private String middleName;
	@AuditMe
	private String designation;
	@AuditMe
	private String fullName;
	@AuditMe
	private String birthName;
	@AuditMe
	private String middleInitial;
	@AuditMe
	private String knownAs;
	@AuditMe
	private Date birthDate;
	@AuditMe
	private String nationality;
	@AuditMe
	private String gender;

	public PersonalData() {
	}

	public PersonalData(String firstName, String lastName) {
		this.firstName = firstName;
		this.lastName = lastName;
	}

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "PERSONALDATA_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the personnelNumber
	 */
	public Long getPersonnelNumber() {
		return personnelNumber;
	}

	/**
	 * @param personnelNumber
	 *            the personnelNumber to set
	 */
	public void setPersonnelNumber(Long personnelNumber) {
		this.personnelNumber = personnelNumber;
	}

	/**
	 * @return the start
	 */
	@Temporal(TemporalType.DATE)
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * @param start
	 *            the start to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * @return the to
	 */
	@Temporal(TemporalType.DATE)
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * @param to
	 *            the to to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/**
	 * @return the recordNumber
	 */
	@Column(name = "RECORD_NUMBER"/* , unique = true */, nullable = false)
	public Long getRecordNumber() {
		return recordNumber;
	}

	/**
	 * @param recordNumber
	 *            the recordNumber to set
	 */
	public void setRecordNumber(Long recordNumber) {
		this.recordNumber = recordNumber;
	}

	/**
	 * @return the changedOn
	 */
	@Temporal(TemporalType.DATE)
	public Date getChangedOn() {
		return changedOn;
	}

	/**
	 * @param changedOn
	 *            the changedOn to set
	 */
	public void setChangedOn(Date changedOn) {
		this.changedOn = changedOn;
	}

	/**
	 * @return the formOfAddress
	 */
	public String getFormOfAddress() {
		return nullSafeString(formOfAddress);
	}

	/**
	 * @param formOfAddress
	 *            the formOfAddress to set
	 */
	public void setFormOfAddress(String formOfAddress) {
		this.formOfAddress = formOfAddress;
	}

	/**
	 * @return the lastName
	 */
	public String getLastName() {
		return nullSafeString(lastName);
	}

	/**
	 * @param lastName
	 *            the lastName to set
	 */
	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	/**
	 * @return the firstName
	 */
	public String getFirstName() {
		return nullSafeString(firstName);
	}

	/**
	 * @param firstName
	 *            the firstName to set
	 */
	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	/**
	 * @return the middleName
	 */
	public String getMiddleName() {
		return nullSafeString(middleName);
	}

	/**
	 * @param middleName
	 *            the middleName to set
	 */
	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	/**
	 * @return the designation
	 */
	public String getDesignation() {
		return nullSafeString(designation);
	}

	/**
	 * @param designation
	 *            the designation to set
	 */
	public void setDesignation(String designation) {
		this.designation = designation;
	}

	/**
	 * @return the fullName
	 */
	public String getFullName() {
		return nullSafeString(fullName);
	}

	/**
	 * @param fullName
	 *            the fullName to set
	 */
	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	/**
	 * @return the birthName
	 */
	public String getBirthName() {
		return nullSafeString(birthName);
	}

	/**
	 * @param birthName
	 *            the birthName to set
	 */
	public void setBirthName(String birthName) {
		this.birthName = birthName;
	}

	/**
	 * @return the middleInitial
	 */
	public String getMiddleInitial() {
		return nullSafeString(middleInitial);
	}

	/**
	 * @param middleInitial
	 *            the middleInitial to set
	 */
	public void setMiddleInitial(String middleInitial) {
		this.middleInitial = middleInitial;
	}

	/**
	 * @return the knownAs
	 */
	public String getKnownAs() {
		return nullSafeString(knownAs);
	}

	/**
	 * @param knownAs
	 *            the knownAs to set
	 */
	public void setKnownAs(String knownAs) {
		this.knownAs = knownAs;
	}

	/**
	 * @return the birthDate
	 */
	@Temporal(TemporalType.DATE)
	public Date getBirthDate() {
		return birthDate;
	}

	/**
	 * @param birthDate
	 *            the birthDate to set
	 */
	public void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}

	/**
	 * @return the nationality
	 */
	public String getNationality() {
		return nullSafeString(nationality);
	}

	/**
	 * @param nationality
	 *            the nationality to set
	 */
	public void setNationality(String nationality) {
		this.nationality = nationality;
	}

	/**
	 * @return the gender
	 */
	public String getGender() {
		return nullSafeString(gender);
	}

	/**
	 * @param gender
	 *            the gender to set
	 */
	public void setGender(String gender) {
		this.gender = gender;
	}

	/**
	 * 
	 */
	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" id: " + id + newLine);
		result.append(" personnelNumber: " + personnelNumber + newLine);
		result.append(" startDate: " + startDate + newLine);
		result.append(" endDate: " + endDate + newLine);
		result.append(" formOfAddress: " + formOfAddress + newLine);
		result.append(" lastName: " + lastName + newLine);
		result.append(" firstName: " + firstName + newLine);
		result.append(" middleName: " + middleName + newLine);
		result.append(" designation: " + designation + newLine);
		result.append(" fullName: " + fullName + newLine);
		result.append(" birthName: " + birthName + newLine);
		result.append(" middleInitial: " + middleInitial + newLine);
		result.append(" knownAs: " + knownAs + newLine);
		result.append(" birthDate: " + birthDate + newLine);
		result.append(" nationality: " + nationality + newLine);
		result.append(" gender: " + gender + newLine);
		result.append("}");

		return result.toString();
	}

	/*
	 * Returns value using reflection
	 */
	@Transient
	public String getFieldValueFromReflection(FieldDef fd) {
		String value = "";

		try {
			Field field = this.getClass().getDeclaredField(fd.getFieldNameFromPath());
			field.setAccessible(true);
			Object v = field.get(this);

			if ("startDate".equals(field.getName()) || "endDate".equals(field.getName())
					|| "birthDate".equals(field.getName())) {
				value = new SimpleDateFormat(Constants.DATE_PICTURE).format(v);

			} else {
				value = v.toString();
			}

		} catch (SecurityException e) {
			throw new FocusServiceException(e);
		} catch (NoSuchFieldException e) {
			throw new FocusServiceException(e);
		} catch (IllegalArgumentException e) {
			throw new FocusServiceException(e);
		} catch (IllegalAccessException e) {
			throw new FocusServiceException(e);
		}
		return value;
	}

	/*
	 * Returns value using reflection
	 */
	@Transient
	public String getFieldValueFromReflection(String name) {
		String value = "";

		try {
			Field field = this.getClass().getDeclaredField(name);
			field.setAccessible(true);
			Object o = field.get(this);
			if (o != null) {
				if ("startDate".equals(field.getName()) || "endDate".equals(field.getName())) {
					value = new SimpleDateFormat("dd-mm-yyyy").format(o);

				} else {
					value = o.toString();
				}
			}
		} catch (SecurityException e) {
			throw new FocusServiceException(e);
		} catch (NoSuchFieldException e) {
			throw new FocusServiceException(e);
		} catch (IllegalArgumentException e) {
			throw new FocusServiceException(e);
		} catch (IllegalAccessException e) {
			throw new FocusServiceException(e);
		}
		return value;
	}

	/**
	 * Set values for Custom Profile records.
	 * 
	 * @param name
	 * @param value
	 * @return
	 * @throws ParseException
	 */
	@Transient
	public String setFieldValueFromReflection(String name, String value) throws ParseException {
		try {
			Field field = this.getClass().getDeclaredField(name);
			field.setAccessible(true);

			if ("personnelNumber".equals(name)) {
				field.set(this, Integer.valueOf(value));
			} else if ("startDate".equals(name) || "endDate".equals(name)) {
				field.set(this, new SimpleDateFormat(Constants.DATE_PICTURE).parse(value));
			} else if ("birthDate".equals(name)) {
				SimpleDateFormat sdf = new SimpleDateFormat(Constants.DATE_PICTURE);
				Date birthDate = sdf.parse(value);
				field.set(this, birthDate);
			} else {
				field.set(this, value);
			}
		} catch (SecurityException e) {
			throw new FocusServiceException(e);
		} catch (NoSuchFieldException e) {
			throw new FocusServiceException(e);
		} catch (IllegalArgumentException e) {
			throw new FocusServiceException(e);
		} catch (IllegalAccessException e) {
			throw new FocusServiceException(e);
		}
		return value;
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public String getPersonnelNumberAsString() {
		if (personnelNumber == null) {
			return "";
		} else {
			return personnelNumber.toString();
		}
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public String getStartDateAsString() {
		if (startDate == null) {
			return "";
		} else {
			return Utils.defaultDateFormat(startDate);
		}
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public String getEndDateAsString() {
		if (endDate == null) {
			return "";
		} else {
			return Utils.defaultDateFormat(endDate);
		}
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public String getBirthDateAsString() {
		if (birthDate == null) {
			return "";
		} else {
			return Utils.defaultDateFormat(birthDate);
		}
	}

	/**
	 * 
	 * @param s
	 * @return
	 */
	private String nullSafeString(String s) {
		if (s == null) {
			return "";
		} else {
			return s;
		}

	}

	/**
	 * 
	 * @param fieldName
	 * @return
	 */
	@Transient
	public Object getLabelFromName(String fieldName) {
		Map<String, String> fieldLabelMap = new HashMap<String, String>();
		fieldLabelMap.put("personnelNumber", "Personnel Number");
		fieldLabelMap.put("startDate", "Start Date");
		fieldLabelMap.put("endDate", "End Date");
		fieldLabelMap.put("formOfAddress", "Form Of Address");
		fieldLabelMap.put("lastName", "Last Name");
		fieldLabelMap.put("firstName", "First Name");
		fieldLabelMap.put("middleName", "Middle Name");
		fieldLabelMap.put("designation", "Designation");
		fieldLabelMap.put("fullName", "Full Name");
		fieldLabelMap.put("birthName", "Birth Name");
		fieldLabelMap.put("middleInitial", "Middle Initial");
		fieldLabelMap.put("knownAs", "Known As");
		fieldLabelMap.put("birthDate", "Birth Date");
		fieldLabelMap.put("nationality", "Nationality");
		fieldLabelMap.put("gender", "Gender");

		if (fieldLabelMap.containsKey(fieldName)) {
			return fieldLabelMap.get(fieldName);
		} else {
			return "";
		}
	}

	/**
	 * 
	 * @param pd
	 */
	public void updateFromHrisFeed(PersonalData pd) {
		changedOn = pd.getChangedOn();
		formOfAddress = pd.getFormOfAddress();
		lastName = pd.getLastName();
		firstName = pd.getFirstName();
		middleName = pd.getMiddleName();
		designation = pd.getDesignation();
		fullName = pd.getFullName();
		birthName = pd.getBirthName();
		middleInitial = pd.getMiddleInitial();
		knownAs = pd.getKnownAs();
		birthDate = pd.getBirthDate();
		nationality = pd.getNationality();
		gender = pd.getGender();
	}

	@Transient
	public void constructFullName() {
		fullName = new StringBuilder(firstName).append(" ").append(lastName).toString();
	}
}
