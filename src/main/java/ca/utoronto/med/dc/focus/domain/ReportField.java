package ca.utoronto.med.dc.focus.domain;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * Entity for Report templates
 * 
 */

@Entity
@Table(name = "REPORT_FIELD", uniqueConstraints = @UniqueConstraint(columnNames = {
		"REPORTFIELD_ID", "FIELDDEF_ID", "DISPLAY_ORDER" }))
public class ReportField {

	private Long id;

	// @OrderColumn
	private FieldDef field;
	private ReportTemplate reportTemplate;
	private int order;

	/**
	 * 
	 */
	public ReportField() {
	}

	/**
	 * 
	 * @param reportTemplate
	 * @param field
	 * @param order
	 */
	public ReportField(ReportTemplate reportTemplate, FieldDef field, int order) {
		this.reportTemplate = reportTemplate;
		this.field = field;
		this.order = order;
	}

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "REPORTFIELD_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the field
	 */
	@ManyToOne
	@JoinColumn(name = "FIELDDEF_ID")
	public FieldDef getField() {
		return field;
	}

	/**
	 * @param field
	 *            the field to set
	 */
	public void setField(FieldDef field) {
		this.field = field;
	}

	/**
	 * @return the report
	 */
	@ManyToOne()
	@JoinColumn(name = "REPORTTEMPLATE_ID")
	public ReportTemplate getReportTemplate() {
		return reportTemplate;
	}

	/**
	 * @param report
	 *            the report to set
	 */
	public void setReportTemplate(ReportTemplate reportTemplate) {
		this.reportTemplate = reportTemplate;
	}

	/**
	 * @return the order
	 */
	@Column(name = "DISPLAY_ORDER", nullable = false)
	public int getOrder() {
		return order;
	}

	/**
	 * @param order
	 *            the order to set
	 */
	public void setOrder(int order) {
		this.order = order;
	}

}
