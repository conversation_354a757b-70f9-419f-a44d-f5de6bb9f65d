package ca.utoronto.med.dc.focus.domain;

import org.hibernate.criterion.Junction;
import org.hibernate.criterion.Restrictions;


public enum SearchCriteriaJunction {

	ANY,
	ALL;
	
	public static final SearchCriteriaJunction fromName(String name) {
		SearchCriteriaJunction junction = null;
		if (name == null || name.isEmpty()) {
			return junction;
		}
		
		for (SearchCriteriaJunction currentJunction : SearchCriteriaJunction.values()) {
			if (currentJunction.name().equalsIgnoreCase(name)) {
				junction = currentJunction;
				break;
			}
		}
		return junction;
	}
	
	public Junction asCriteriaJunction() {
		Junction junction = Restrictions.conjunction();
		if (this.equals(ANY)) {
			junction = Restrictions.disjunction();
		}
		return junction;
	}
	
}
