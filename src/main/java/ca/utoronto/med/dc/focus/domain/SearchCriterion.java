package ca.utoronto.med.dc.focus.domain;

import java.io.Serializable;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Index;

@Entity
@Table(name = "SEARCH_CRITERION")
public class SearchCriterion extends BaseEntity implements Serializable {
	
	private static final long serialVersionUID = 4766118322960187232L;

	private Long id;
	private String value;
	private ProfileGroupCriterionOperator operator;
	private SearchCriteriaGang searchCriteriaGang;
	private FieldDef searchableFieldDef;
	// == TRANSIENT for UI == //
	private int index;

	public SearchCriterion() {
	}

	public SearchCriterion(FieldDef fieldDef,
			ProfileGroupCriterionOperator operator, String value) {
		this();
		this.searchableFieldDef = fieldDef;
		this.operator = operator;
		this.value = value;
	}

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "SEARCHCRITERION_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the profileGroup
	 */
	@ManyToOne
	@JoinColumn(name = "SEARCH_CRITERIA_GANG_ID")
	@ForeignKey(name = "FK__SEARCHCRITERION__SEARCHCRITERIAGANG")
	@Index(name = "IDX__SEARCHCRITERION__SEARCHCRITERIAGANG_ID")
	public SearchCriteriaGang getSearchCriteriaGang() {
		return searchCriteriaGang;
	}

	/**
	 * @param dynamicGroup
	 *            the profileGroup to set
	 */
	public void setSearchCriteriaGang(SearchCriteriaGang critGang) {
		this.searchCriteriaGang = critGang;
	}

	@ManyToOne(fetch = FetchType.LAZY, cascade = { CascadeType.MERGE,
			CascadeType.PERSIST })
	@JoinColumn(name = "SEARCHABLE_FIELDDEF_ID")
	public FieldDef getSearchableFieldDef() {
		return searchableFieldDef;
	}

	public void setSearchableFieldDef(FieldDef searchableFieldDef) {
		this.searchableFieldDef = searchableFieldDef;
	}

	/**
	 * 
	 * @return
	 */
	@Column(name = "VALUE", nullable = true)
	public String getValue() {
		return value;
	}

	/**
	 * 
	 * @param value
	 */
	public void setValue(String value) {
		this.value = value;
	}

	@ManyToOne(fetch = FetchType.LAZY, cascade = { CascadeType.MERGE,
			CascadeType.PERSIST })
	@JoinColumn(name = "PROFILEGROUPCRITOP_ID")
	public ProfileGroupCriterionOperator getOperator() {
		return operator;
	}

	public void setOperator(ProfileGroupCriterionOperator operator) {
		this.operator = operator;
	}

	/**
	 * Where is this criterion in the list of criteria for the SearchGang?
	 * @deprecated
	 * @return
	 */
	@Deprecated
	@Transient
	public int getIndex() {
		return index;
	}

	/**
	 * @deprecated
	 * @param index
	 */
	@Deprecated
	public void setIndex(int index) {
		this.index = index;
	}

	@Transient
	public boolean isNestedSearch() {
		boolean nestedSearch = false;
		if (searchableFieldDef != null
				&& searchableFieldDef.isInternalDynamicGroups()) {
			nestedSearch = true;
		}
		return nestedSearch;
	}
	
	@Transient
	public Long getValueAsLong() {
		Long valueAsLong = null;

		try {
			valueAsLong = Long.parseLong(value);
		} catch (NumberFormatException e) {
			// allow valueAsLong to be null
		}

		return valueAsLong;
	}
	
	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" id: ").append(id).append(newLine);
		if(operator != null) {
			result.append(" operator: ").append(getOperator()).append(newLine);			
		}
		result.append(" value: ").append(value).append(newLine);

		result.append("}");

		return result.toString();
	}	
}
