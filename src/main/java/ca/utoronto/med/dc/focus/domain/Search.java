/**
 * 
 */
package ca.utoronto.med.dc.focus.domain;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import javax.validation.constraints.Pattern;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Index;

/**
 * 
 */
@Entity
@Table(name = "SEARCH", uniqueConstraints = @UniqueConstraint(columnNames = {
		"AUTHUSER_ID", "SEARCH_NAME" }, name = "UQ__SEARCH_ID__AUTHUSER_ID__NAME"))
public class Search extends BaseEntity implements Serializable {

	private static final long serialVersionUID = -4863689518889301833L;

	public static final String ENTITY_LABEL = "Group";
	public static final String ENTITY_LABEL_PLURAL = "Groups";
	public static final String ENTITY_SHARED_LABEL = "Shared " + ENTITY_LABEL;
	public static final String ENTITY_SHARED_LABEL_PLURAL = "Shared "
			+ ENTITY_LABEL_PLURAL;

	// is Search visible to other Department users?
	public static final boolean PROFILE_GROUP_VISIBILITY_PUBLIC = true;
	public static final boolean PROFILE_GROUP_VISIBILITY_PRIVATE = false;

	private Long id;
	@Pattern(regexp = "[a-zA-Z_0-9-\\(\\)\\[\\]' ]{1,60}", message = "Group Name must be between 1 and 60 characters. Only letters, numbers, spaces, square brackets, parentheses, quotation, underscores and hyphens are allowed.")
	private String name;
	private String description;
	private Boolean deptVisibility = false;
	private AuthUser authUser;
	private Department department;
	private String sharedKey;
	private Set<SearchCriteriaGang> criteriaGangs = new LinkedHashSet<SearchCriteriaGang>();

	// Transient
	private String authUserName;
	private int readOnly = 0;
	private boolean groupShared;
	private boolean shareable;

	public Search() {
	}

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "SEARCH_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the name
	 */
	@Column(name = "SEARCH_NAME", nullable = false)
	public String getName() {
		return name;
	}

	/**
	 * @param name
	 *            the name to set
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * @return the description
	 */
	@Column(name = "DESCRIPTION")
	public String getDescription() {
		return description;
	}

	/**
	 * @param description
	 *            the description to set
	 */
	public void setDescription(String description) {
		this.description = description;
	}

	/**
	 * @return the visible
	 */
	@Column(name = "DEPT_VISIBILITY")
	public Boolean isDeptVisibility() {
		return deptVisibility;
	}

	public void setDeptVisibility(Boolean deptVisibility) {
		this.deptVisibility = deptVisibility;
	}

	@ManyToOne
	@JoinColumn(name = "AUTHUSER_ID", nullable = false)
	@ForeignKey(name = "FK__SEARCH__USERS")
	@Index(name = "IDX__SEARCH__USERS")
	public AuthUser getAuthUser() {
		return authUser;
	}

	public void setAuthUser(AuthUser owner) {
		this.authUser = owner;
	}

	/**
	 * @return the department
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DEPARTMENT_ID", nullable = false)
	@ForeignKey(name = "FK__SEARCH__DEPARTMENT")
	@Index(name = "IDX__SEARCH__DEPARTMENT_ID")
	public Department getDepartment() {
		return department;
	}

	/**
	 * @param department
	 *            the department to set
	 */
	public void setDepartment(Department department) {
		this.department = department;
	}

	/**
	 * @return the sharedKey
	 */
	@Column(name = "SHARED_KEY")
	public String getSharedKey() {
		return sharedKey;
	}

	/**
	 * @param sharedKey
	 *            the sharedKey to set
	 */
	public void setSharedKey(String sharedKey) {
		this.sharedKey = sharedKey;
	}

	/**
	 * @return the criteriaGangs
	 */
	@OneToMany(mappedBy = "search", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.DETACH, CascadeType.REMOVE })
	public Set<SearchCriteriaGang> getCriteriaGangs() {
		return criteriaGangs;
	}

	/**
	 * @param criteriaGangs
	 *            the criteriaGangs to set
	 */
	public void setCriteriaGangs(Set<SearchCriteriaGang> criteriaGangs) {
		this.criteriaGangs = criteriaGangs;
	}

	/**
	 * @return the authUserName
	 */
	@Transient
	public String getAuthUserName() {
		return authUserName;
	}

	/**
	 * @param authUserName
	 *            the authUserName to set
	 */
	public void setAuthUserName(String authUserName) {
		this.authUserName = authUserName;
	}

	/**
	 * @return the readOnly
	 */
	@Transient
	public int getReadOnly() {
		return readOnly;
	}

	/**
	 * @param readOnly
	 *            the readOnly to set
	 */
	public void setReadOnly(int readOnly) {
		this.readOnly = readOnly;
	}

	/**
	 * @return the groupShared
	 */
	@Transient
	public boolean hasGroupShared() {
		groupShared = false;
		groupShared = this.getSharedKey() != null
				&& !this.getSharedKey().isEmpty();
		return groupShared;
	}

	/**
	 * @param groupShared
	 *            the groupShared to set
	 */
	public void setGroupShared(boolean groupShared) {
		this.groupShared = groupShared;
	}

	/**
	 * True means that this group may be shared to other Focus users.
	 */
	@Transient
	public boolean isShareable() {
		return shareable;
	}

	/**
	 * True means that this group may be shared to other Focus users.
	 */
	public void setShareable(boolean shareable) {
		this.shareable = shareable;
	}

	@Transient
	public Search addSearchCriteriaGang(SearchCriteriaGang gang) {
		this.criteriaGangs.add(gang);
		gang.setSearch(this);
		return this;
	}

	@Transient
	public String getUpdatedOnFormatted() {
		String dateStr = "";
		if (getUpdatedOn() != null) {
			SimpleDateFormat df = new SimpleDateFormat("MMMM dd, yyyy");
			dateStr = df.format(getUpdatedOn());
		}
		return dateStr;
	}

	@Transient
	public String getViewButtonText() {
		return "View Group Criteria";
	}

	@Transient
	public String getEditButtonText() {
		return "Edit Group Criteria";
	}

	@Override
	public String toString() {
		String newLine = System.getProperty("line.separator");
		StringBuilder sb = new StringBuilder(this.getClass().getSimpleName())
				.append(" {").append(newLine);
		sb.append("id:").append(id);
		sb.append("name: ").append(name).append(newLine);
		sb.append("owner: ").append(getAuthUserName()).append(newLine);
		sb.append("updatedOn: ").append(getUpdatedOnFormatted())
				.append(newLine);
		sb.append("departmental: ").append(isDeptVisibility()).append(newLine);
		sb.append("groupShared: ").append(hasGroupShared()).append(newLine);
		sb.append("sharedKey: ").append(getSharedKey()).append(newLine);
		sb.append("viewButtonText: ").append(getViewButtonText())
				.append(newLine);
		sb.append("editButtonText: ").append(getEditButtonText())
				.append(newLine);
		sb.append("}");
		return sb.toString();
	}

	@Transient
	public Object toJson() {
		Map<String, Object> json = new HashMap<String, Object>();
		json.put("id", id);
		json.put("name", name);
		json.put("owner", getAuthUserName());
		json.put("updatedOn", getUpdatedOnFormatted());
		json.put("departmental", isDeptVisibility());
		json.put("groupShared", hasGroupShared());
		json.put("sharedKey", getSharedKey());
		json.put("viewButtonText", getViewButtonText());
		json.put("editButtonText", getEditButtonText());
		return json;
	}

}
