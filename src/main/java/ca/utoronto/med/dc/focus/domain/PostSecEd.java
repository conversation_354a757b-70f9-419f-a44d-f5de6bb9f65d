package ca.utoronto.med.dc.focus.domain;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.envers.Audited;
import org.slf4j.ext.XLogger;
import org.slf4j.ext.XLogger.Level;
import org.slf4j.ext.XLoggerFactory;

@Entity
@Table(name = "POSTSECED")
@Audited
public class PostSecEd extends BaseEntity {

	private XLogger logger = XLoggerFactory.getXLogger(this.getClass()
			.getName());
	private Long id;
	private Date startDate;
	private Date endDate;
	private Long recordNumber;

	private Date changedOn;
	private Long educationCode;
	private String educationCodeText;
	private String instution;
	private String instutionText;
	private Long yearConfirmed;
	private String verified;
	private String sourceDocument;
	private String sourceDocumentText;

	private Education education;

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "POSTSECED_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the from
	 */
	@Temporal(TemporalType.DATE)
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * @param from
	 *            the from to set
	 */
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	/**
	 * @return the to
	 */
	@Temporal(TemporalType.DATE)
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * @param to
	 *            the to to set
	 */
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	/**
	 * @return the changedOn
	 */
	@Temporal(TemporalType.DATE)
	public Date getChangedOn() {
		return changedOn;
	}

	/**
	 * @param changedOn
	 *            the changedOn to set
	 */
	public void setChangedOn(Date changedOn) {
		this.changedOn = changedOn;
	}

	/**
	 * @return the verified
	 */
	public String getVerified() {
		return verified;
	}

	/**
	 * @param verified
	 *            the verified to set
	 */
	public void setVerified(String verified) {
		this.verified = verified;
	}

	/**
	 * @return the recordNumber
	 */
	@Column(name = "RECORD_NUMBER"/* , unique = true */, nullable = false)
	public Long getRecordNumber() {
		return recordNumber;
	}

	/**
	 * @param recordNumber
	 *            the recordNumber to set
	 */
	public void setRecordNumber(Long recordNumber) {
		this.recordNumber = recordNumber;
	}

	/**
	 * @return the educationCode
	 */
	public Long getEducationCode() {
		return educationCode;
	}

	/**
	 * @param educationCode
	 *            the educationCode to set
	 */
	public void setEducationCode(Long educationCode) {
		this.educationCode = educationCode;
	}

	/**
	 * @return the educationCodeText
	 */
	public String getEducationCodeText() {
		return educationCodeText;
	}

	/**
	 * @param educationCodeText
	 *            the educationCodeText to set
	 */
	public void setEducationCodeText(String educationCodeText) {
		this.educationCodeText = educationCodeText;
	}

	/**
	 * @return the instution
	 */
	public String getInstution() {
		return instution;
	}

	/**
	 * @param instution
	 *            the instution to set
	 */
	public void setInstution(String instution) {
		this.instution = instution;
	}

	/**
	 * @return the instutionText
	 */
	public String getInstutionText() {
		return instutionText;
	}

	/**
	 * @param instutionText
	 *            the instutionText to set
	 */
	public void setInstutionText(String instutionText) {
		this.instutionText = instutionText;
	}

	/**
	 * @return the yearConfirmed
	 */
	public Long getYearConfirmed() {
		return yearConfirmed;
	}

	/**
	 * @param yearConfirmed
	 *            the yearConfirmed to set
	 */
	public void setYearConfirmed(Long yearConfirmed) {
		this.yearConfirmed = yearConfirmed;
	}

	/**
	 * @return the sourceDocument
	 */
	public String getSourceDocument() {
		return sourceDocument;
	}

	/**
	 * @param sourceDocument
	 *            the sourceDocument to set
	 */
	public void setSourceDocument(String sourceDocument) {
		this.sourceDocument = sourceDocument;
	}

	/**
	 * @return the sourceDocumentText
	 */
	public String getSourceDocumentText() {
		return sourceDocumentText;
	}

	/**
	 * @param sourceDocumentText
	 *            the sourceDocumentText to set
	 */
	public void setSourceDocumentText(String sourceDocumentText) {
		this.sourceDocumentText = sourceDocumentText;
	}

	/**
	 * @return the education
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EDUCATION_ID", nullable = false)
	@ForeignKey(name = "FK__POSTSECED__EDUCATION")
	@org.hibernate.annotations.Index(name = "IDX__POSTSECED__EDUCATION_ID")
	public Education getEducation() {
		return education;
	}

	/**
	 * @param education
	 *            the education to set
	 */
	public void setEducation(Education education) {
		this.education = education;
	}

	/**
	 * 
	 */
	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" id: " + id + newLine);
		result.append(" startDate: " + startDate + newLine);
		result.append(" endDate: " + endDate + newLine);
		result.append(" changedOn: " + changedOn + newLine);
		result.append(" year: " + yearConfirmed + newLine);
		result.append(" verified: " + verified + newLine);

		result.append("}");

		return result.toString();
	}

	/**
	 * 
	 * @param pse
	 */
	public void copyFrom(PostSecEd pse) {
		this.startDate = pse.startDate;
		this.endDate = pse.endDate;
		this.recordNumber = pse.recordNumber;
		this.changedOn = pse.changedOn;
		this.educationCode = pse.educationCode;
		this.educationCodeText = pse.educationCodeText;
		this.instution = pse.instution;
		this.instutionText = pse.instutionText;
		this.yearConfirmed = pse.yearConfirmed;
		this.verified = pse.verified;
		this.sourceDocument = pse.sourceDocument;
		this.sourceDocumentText = pse.sourceDocumentText;
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public String getStartDateAsString() {
		if (startDate == null) {
			return "";
		} else {
			return new SimpleDateFormat("dd-MM-yyyy").format(startDate);
		}
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public String getEndDateAsString() {
		if (endDate == null) {
			return "";
		} else {
			return new SimpleDateFormat("dd-MM-yyyy").format(endDate);
		}
	}

	/**
	 * 
	 * @param ha
	 * @return
	 */
	public boolean isSame(PostSecEd hp) {
		if (hp == null || !hp.getClass().equals(this.getClass())) {
			return false;
		}
		
		try {
			if (hp.getStartDate().equals(startDate)
					&& hp.getEndDate().equals(endDate)
					&& hp.getRecordNumber().equals(recordNumber)) {
				return true;
			}
		} catch (NullPointerException e) {
			logger.catching(Level.DEBUG, e);
		}

		return false;

	}

	/**
	 * 
	 * @param ha
	 */
	public void updateFromHris(PostSecEd hp) {
		changedOn = hp.getChangedOn();
		educationCode = hp.getEducationCode();
		educationCodeText = hp.getEducationCodeText();
		instution = hp.getInstution();
		instutionText = hp.getInstutionText();
		yearConfirmed = hp.getYearConfirmed();
		verified = hp.getVerified();
		sourceDocument = hp.getSourceDocument();
		sourceDocumentText = hp.getSourceDocumentText();
	}

	@Transient
	@Override
	public String getPath() {
		return "education.postSecEd";
	}

	@Override
	public void appendRecordStrings(List<String> strings) {
		strings.add("Start Date: " + startDate);
		strings.add("End Date: " + endDate);
		strings.add("Education Code: " + educationCodeText);
		strings.add("Inst Desc: " + instutionText);
		strings.add("Year Confirmed: " + yearConfirmed);
		strings.add("Verified: " + verified);
	}

}
