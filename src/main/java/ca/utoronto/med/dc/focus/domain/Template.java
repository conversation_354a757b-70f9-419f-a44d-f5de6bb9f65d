/**
 * Super class for Template creation(Report/Email)
 */

package ca.utoronto.med.dc.focus.domain;

import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import javax.validation.constraints.Pattern;

import org.codehaus.jackson.annotate.JsonIgnore;
import org.hibernate.validator.constraints.NotEmpty;

@MappedSuperclass
public class Template extends BaseEntity {

	@NotEmpty(message = "Name cannot be empty.")
	@Pattern(regexp = "^$|[a-zA-Z_0-9-'(). ]{3,60}", message = "Name must be between 3 and 60 characters. Only letters, numbers, spaces, underscores, parentheses, periods and hyphens are allowed.")
	private String name;
	private Department department;

	/**
	 * @return
	 */
	@Column(name = "TEMPLATE_NAME", nullable = false)
	public String getName() {
		return name;
	}

	/**
	 * @param name
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * TODO how to put ForeignKey and Index annotations on this?
	 * 
	 * @return the department
	 */
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "DEPARTMENT_ID", nullable = true)
	public Department getDepartment() {
		return department;
	}

	/**
	 * @param department
	 *            the department to set
	 */
	public void setDepartment(Department department) {
		this.department = department;
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public boolean isDepartmental() {
		return department != null;
	}

	/**
	 * 
	 */
	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" Name: " + name + newLine);
		String dept = "";
		if (department != null) {
			dept = department.getName();
		}

		result.append(" Department: " + dept + newLine);
		result.append("}");

		return result.toString();
	}

}
