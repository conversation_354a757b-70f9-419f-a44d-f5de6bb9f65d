package ca.utoronto.med.dc.focus.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.ForeignKey;
import org.hibernate.annotations.Index;
import org.hibernate.envers.Audited;
import org.hibernate.envers.NotAudited;
import org.hibernate.envers.RelationTargetAuditMode;
import org.slf4j.ext.XLogger;
import org.slf4j.ext.XLoggerFactory;

import ca.utoronto.med.dc.common.util.Constants;
import ca.utoronto.med.dc.common.util.Utils;
import ca.utoronto.med.dc.focus.dto.NoteDTO;

@Entity
@Table(name = "PROFILE")
@Audited
public class Profile extends BaseEntity {

	private XLogger logger = XLoggerFactory.getXLogger(this.getClass()
			.getName());
	
	private Long id;
	private PersonalData personalData;
	private Set<Action> actions = new HashSet<Action>();
	private Set<Address> addresses = new HashSet<Address>();
	private Set<Email> emails = new HashSet<Email>();
	private Set<Telephone> telephones = new HashSet<Telephone>();
	private Set<AppointmentDetails> appointmentDetails = new HashSet<AppointmentDetails>();
	private Set<BasicPay> basicPay = new HashSet<BasicPay>();
	private Set<ContractElement> contractElements = new HashSet<ContractElement>();
	private Education education;
	private Set<OtherUniversityAppointment> otherUnivAppts = new HashSet<OtherUniversityAppointment>();

	private Set<Tag> tags = new HashSet<Tag>();

	// Custom Field
	private Set<FieldValue> customFieldValues = new HashSet<FieldValue>();

	// The Groups this profile is part of
	private Set<StaticGroup> staticGroups = new HashSet<StaticGroup>();

	// This profile's Communications preferences per Department
	private List<CommPref> commprefList = new ArrayList<CommPref>();

	// The source of this profile
	private FeedSource source;

	private Set<Note> notes;
	private List<NoteDTO> notesDto;
	private List<EmailRecord> emailsSent = new ArrayList<EmailRecord>();
	private ExtractedFields extractedFields;
	private FacultyWideData facultyWideData;
	
	// UtorAuthData from utorAuthPublisher
	private UtorAuthData utorAuthData;

	// HRIS Rules to validate completeness
	private Set<HrisRuleEvent> hrisRuleEventList = new HashSet<HrisRuleEvent>();

	// WebCV ID
	private WebCVBridge webcvBridge;

	private Set<CustomAppointment> customAppointments = new HashSet<CustomAppointment>();

	private Set<CrossAppointment> crossAppointments = new HashSet<CrossAppointment>();
	private Set<BudgetaryCrossAppointment> budgetaryCrossAppointments = new HashSet<BudgetaryCrossAppointment>();

	/**
	 * 
	 */
	public Profile() {
		education = new Education();
		education.setProfile(this);
		extractedFields = new ExtractedFields();
		facultyWideData = new FacultyWideData();
		utorAuthData = new UtorAuthData();
	}

	public Profile(Long id) {
		this();
		this.id = id;
	}

	public Profile(String firstName, String lastName) {
		this();
		personalData = new PersonalData(firstName, lastName);
		personalData.setRecordNumber(Constants.DUMMY_RECORD_NUMBER);
	}

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "PROFILE_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the personalData
	 */
	@OneToOne(optional = false, cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, fetch = FetchType.LAZY)
	@JoinColumn(name = "PERSONALDATA_ID", nullable = false)
	@ForeignKey(name = "FK__PROFILE_PERSONALDATA")
	@Index(name = "IDX__PROFILE__PERSONALDATA_ID")
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public PersonalData getPersonalData() {
		return personalData;
	}

	/**
	 * @param personalData
	 *            the personalData to set
	 */
	public void setPersonalData(PersonalData personalData) {
		this.personalData = personalData;
	}

	/**
	 * @return the action
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, orphanRemoval = true)
	public Set<Action> getActions() {
		return actions;
	}

	/**
	 * @param action
	 *            the action to set
	 */
	public void setActions(Set<Action> actions) {
		this.actions = actions;
	}

	/**
	 * @return the address
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, orphanRemoval = true)
	public Set<Address> getAddresses() {
		return addresses;
	}

	/**
	 * @param address
	 *            the address to set
	 */
	public void setAddresses(Set<Address> addresses) {
		this.addresses = addresses;
	}

	/**
	 * @return the email
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, orphanRemoval = true)
	public Set<Email> getEmails() {
		return emails;
	}

	/**
	 * @param email
	 *            the email to set
	 */
	public void setEmails(Set<Email> emails) {
		this.emails = emails;
	}

	/**
	 * @return the telephone
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, orphanRemoval = true)
	public Set<Telephone> getTelephones() {
		return telephones;
	}

	/**
	 * @param telephone
	 *            the telephone to set
	 */
	public void setTelephones(Set<Telephone> telephones) {
		this.telephones = telephones;
	}

	/**
	 * @return the appointmentDetails
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, orphanRemoval = true)
	public Set<AppointmentDetails> getAppointmentDetails() {
		return appointmentDetails;
	}

	/**
	 * @param appointmentDetails
	 *            the appointmentDetails to set
	 */
	public void setAppointmentDetails(Set<AppointmentDetails> appointmentDetails) {
		this.appointmentDetails = appointmentDetails;
	}

	/**
	 * @return the basicPay
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, orphanRemoval = true)
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	@NotAudited
	public Set<BasicPay> getBasicPay() {
		return basicPay;
	}

	/**
	 * @param basicPay
	 *            the basicPay to set
	 */
	public void setBasicPay(Set<BasicPay> basicPay) {
		this.basicPay = basicPay;
	}

	/**
	 * @return the contractElement
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, orphanRemoval = true)
	public Set<ContractElement> getContractElements() {
		return contractElements;
	}

	/**
	 * @param contractElement
	 *            the contractElement to set
	 */
	public void setContractElements(Set<ContractElement> contractElements) {
		this.contractElements = contractElements;
	}

	/**
	 * @return the otherUnivAppts
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, orphanRemoval = true)
	public Set<OtherUniversityAppointment> getOtherUnivAppts() {
		return otherUnivAppts;
	}

	/**
	 * @param otherUnivAppts
	 *            the otherUnivAppts to set
	 */
	public void setOtherUnivAppts(Set<OtherUniversityAppointment> otherUnivAppts) {
		this.otherUnivAppts = otherUnivAppts;
	}

	/**
	 * @return the crossAppointments
	 */
	@OneToMany(cascade = { CascadeType.PERSIST, CascadeType.MERGE,
			CascadeType.REMOVE }, orphanRemoval = true, mappedBy = "profile")
	public Set<CrossAppointment> getCrossAppointments() {
		return crossAppointments;
	}

	/**
	 * @param crossAppointments
	 *            the crossAppointments to set
	 */
	public void setCrossAppointments(Set<CrossAppointment> crossAppointments) {
		this.crossAppointments = crossAppointments;
	}

	public void addCrossAppointment(CrossAppointment ca) {
		ca.setProfile(this);
		crossAppointments.add(ca);
	}

	/**
	 * @return the budgetaryCrossAppointments
	 */
	@OneToMany(cascade = { CascadeType.PERSIST, CascadeType.MERGE,
			CascadeType.REMOVE }, orphanRemoval = true, mappedBy = "profile")
	public Set<BudgetaryCrossAppointment> getBudgetaryCrossAppointments() {
		return budgetaryCrossAppointments;
	}

	/**
	 * @param budgetaryCrossAppointments
	 *            the budgetaryCrossAppointments to set
	 */
	public void setBudgetaryCrossAppointments(
			Set<BudgetaryCrossAppointment> budgetaryCrossAppointments) {
		this.budgetaryCrossAppointments = budgetaryCrossAppointments;
	}

	public void addBudgetaryCrossAppointment(BudgetaryCrossAppointment bca) {
		bca.setProfile(this);
		budgetaryCrossAppointments.add(bca);
	}

	private void removeBudgetaryCrossAppointment(BudgetaryCrossAppointment bca) {
		budgetaryCrossAppointments.remove(bca);
		bca.setProfile(null);
	}

	/**
	 * Helpers to set both sides of the relationship
	 */
	public void addAction(Action a) {
		a.setProfile(this);
		actions.add(a);
	}

	public void addAddress(Address a) {
		a.setProfile(this);
		addresses.add(a);
	}

	public void addEmail(Email e) {
		e.setProfile(this);
		emails.add(e);
	}

	public void addTelephone(Telephone t) {
		t.setProfile(this);
		telephones.add(t);
	}

	public void addAppoinmentDetail(AppointmentDetails ad) {
		ad.setProfile(this);
		appointmentDetails.add(ad);
	}

	public void addBasicPay(BasicPay bp) {
		bp.setProfile(this);
		basicPay.add(bp);
	}

	public void addContractElement(ContractElement ce) {
		ce.setProfile(this);
		contractElements.add(ce);
	}

	public void addStaticGroup(StaticGroup staticGroup) {
		staticGroups.add(staticGroup);
	}

	public void removeStaticGroup(StaticGroup staticGroup) {
		staticGroups.remove(staticGroup);
	}

	/**
	 * @return the education
	 */
	@OneToOne(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, orphanRemoval = true)
	public Education getEducation() {
		return education;
	}

	/**
	 * @param education
	 *            the education to set
	 */
	public void setEducation(Education education) {
		this.education = education;
	}

	public void addOtherUniversityAppointment(OtherUniversityAppointment oa) {
		oa.setProfile(this);
		otherUnivAppts.add(oa);
	}

	/**
	 * @return the tags
	 */
	@ManyToMany(mappedBy = "taggedProfiles")
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public Set<Tag> getTags() {
		return tags;
	}

	/**
	 * @param tags
	 *            the tags to set
	 */
	public void setTags(Set<Tag> tags) {
		this.tags = tags;
	}

	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE })
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public Set<CustomAppointment> getCustomAppointments() {
		return customAppointments;
	}

	public void setCustomAppointments(Set<CustomAppointment> customAppointments) {
		this.customAppointments = customAppointments;
	}

	public void addCustomAppointment(CustomAppointment customAppointment) {
		if (customAppointments == null) {
			customAppointments = new HashSet<CustomAppointment>();
		}
		customAppointment.setProfile(this);
		customAppointments.add(customAppointment);
	}

	/**
	 * @return the customFieldValues
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE })
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public Set<FieldValue> getCustomFieldValues() {
		return customFieldValues;
	}

	/**
	 * @param customFieldValues
	 *            the customFieldValues to set
	 */
	public void setCustomFieldValues(Set<FieldValue> customFieldValues) {
		this.customFieldValues = customFieldValues;
	}

	@ManyToMany(mappedBy = "profiles")
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public Set<StaticGroup> getStaticGroups() {
		return staticGroups;
	}

	public void setStaticGroups(Set<StaticGroup> staticGroups) {
		this.staticGroups = staticGroups;
	}

	/**
	 * @return the commpref
	 */
	@OneToMany(cascade = { CascadeType.PERSIST, CascadeType.MERGE,
			CascadeType.REMOVE }, mappedBy = "profile")
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public List<CommPref> getCommprefList() {
		return commprefList;
	}

	/**
	 * @param commpref
	 *            the commpref to set
	 */
	public void setCommprefList(List<CommPref> commprefList) {
		this.commprefList = commprefList;
	}

	/**
	 * @return the source
	 */
	@ManyToOne(fetch = FetchType.LAZY, optional = false)
	@JoinColumn(name = "FEEDSOURCE_ID")
	@ForeignKey(name = "FK__PROFILE_FEEDSOURCE")
	@Index(name = "IDX__PROFILE_FEEDSOURCE_ID")
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public FeedSource getSource() {
		return source;
	}

	/**
	 * @param source
	 *            the source to set
	 */
	public void setSource(FeedSource source) {
		this.source = source;
	}

	/**
	 * @return the notes
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, orphanRemoval = true)
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public Set<Note> getNotes() {
		return notes;
	}

	/**
	 * @param notes
	 *            the notes to set
	 */
	public void setNotes(Set<Note> notes) {
		this.notes = notes;
	}

	/**
	 * @return the emailsSent
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE }, orphanRemoval = true)
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public List<EmailRecord> getEmailsSent() {
		return emailsSent;
	}

	/**
	 * @param emailsSent
	 *            the emailsSent to set
	 */
	public void setEmailsSent(List<EmailRecord> emailsSent) {
		this.emailsSent = emailsSent;
	}

	/**
	 * 
	 * @return
	 */
	@OneToOne(optional = false, cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, fetch = FetchType.LAZY)
	@JoinColumn(name = "EXTRACTEDFIELDS_ID", nullable = false)
	@ForeignKey(name = "FK__PROFILE_EXTRACTEDFIELDS")
	@Index(name = "IDX__PROFILE_ID_EXTRACTEDFIELDS")
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public ExtractedFields getExtractedFields() {
		return extractedFields;
	}

	public void setExtractedFields(ExtractedFields extractedFields) {
		this.extractedFields = extractedFields;
	}

	@OneToOne(optional = false, cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, fetch = FetchType.LAZY)
	@JoinColumn(name = "FACULTYWIDEDATA_ID", nullable = false)
	@ForeignKey(name = "FK__PROFILE_FACULTYWIDEDATA")
	@Index(name = "IDX__PROFILE_ID_FACULTYWIDEDATA")
	public FacultyWideData getFacultyWideData() {
		return facultyWideData;
	}

	public void setFacultyWideData(FacultyWideData facultyWideData) {
		this.facultyWideData = facultyWideData;
	}
	
	@OneToOne(cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, fetch = FetchType.LAZY)
	@JoinColumn(name = "UTORAUTH_DATA_ID")
	@ForeignKey(name = "FK__PROFILE__UTORAUTH_DATA")
	@Index(name = "IDX__PROFILE_ID_UTORAUTHDATA")
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public UtorAuthData getUtorAuthData() {
		return utorAuthData;
	}

	public void setUtorAuthData(UtorAuthData utorAuthData) {
		this.utorAuthData = utorAuthData;
	}	

	/**
	 * @return the hrisRuleEventList
	 */
	@OneToMany(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.REMOVE }, orphanRemoval = true)
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public Set<HrisRuleEvent> getHrisRuleEventList() {
		return hrisRuleEventList;
	}

	/**
	 * @param hrisRuleEventList
	 *            the hrisRuleEventList to set
	 */
	public void setHrisRuleEventList(Set<HrisRuleEvent> hrisRuleEventList) {
		this.hrisRuleEventList = hrisRuleEventList;
	}

	/**
	 * @return the webcvBridge
	 */
	@OneToOne(mappedBy = "profile", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE })
	@NotAudited
	@Audited(targetAuditMode = RelationTargetAuditMode.NOT_AUDITED)
	public WebCVBridge getWebcvBridge() {
		return webcvBridge;
	}

	/**
	 * @param webcvBridge
	 *            the webcvBridge to set
	 */
	public void setWebcvBridge(WebCVBridge webcvBridge) {
		this.webcvBridge = webcvBridge;
	}

	/**
	 * 
	 */
	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName()).append(" Object {")
				.append(newLine);
		result.append(" id: ").append(id).append(newLine);
		result.append(" personalData: ").append(personalData).append(newLine);
		result.append("extractedFields: ").append(extractedFields)
				.append(newLine);
		result.append(" actions: ").append(actions).append(newLine);
		result.append(" addresses: ").append(addresses).append(newLine);
		result.append(" emails: ").append(emails).append(newLine);
		result.append(" telephones: ").append(telephones).append(newLine);
		result.append(" appointmentDetails: ").append(appointmentDetails)
				.append(newLine);
		result.append(" basicPay: ").append(basicPay).append(newLine);
		result.append(" contractElements: ").append(contractElements)
				.append(newLine);
		result.append(" education: ").append(education).append(newLine);
		result.append(" otherUnivAppts: ").append(otherUnivAppts)
				.append(newLine);
		if (this.facultyWideData != null) {
			result.append(" facultyWideData: ").append(facultyWideData)
					.append(newLine);
		}
		result.append("}");

		return result.toString();
	}

	/**
	 * 
	 * @param fieldDefId
	 * @return
	 */
	@Transient
	public String getFieldValue(Long fieldDefId) {
		String val = "";

		for (FieldValue fv : customFieldValues) {
			if (fv.getFieldDef().getId().equals(fieldDefId)) {
				val = fv.getValue();
			}
		}

		return val;
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public List<Email> getCustomEmails() {
		List<Email> customEmails = new ArrayList<Email>();

		for (FieldValue fv : customFieldValues) {
			if (fv.getFieldDef().isEmail()) {
				Email e = new Email();
				e.setId(fv.getFieldDef().getId());
				e.setEmailAddress(fv.getStringValue());

				customEmails.add(e);
			}
		}

		return customEmails;
	}

	/**
	 * 
	 */
	public void createDummyAppointment() {
		AppointmentDetails ad = new AppointmentDetails();
		ad.setStartDate(new Date());
		ad.setEndDate(Utils.getMaxDate());
		ad.setOrgUnit(Utils.getAuthUser().getDepartment().getKey());
		ad.setOrgUnitText(Utils.getAuthUser().getDepartment().getName());
		ad.setPersSubArea(AppointmentDetails.CUSTOM_RECORD_ACADEMIC);
		ad.setPersSubAreaText(AppointmentDetails.CUSTOM_RECORD_ACADEMIC_TEXT);
		ad.setRecordNumber(Constants.DUMMY_RECORD_NUMBER);
		addAppoinmentDetail(ad);
	}

	/**
	 * 
	 */
	public void createDummyOtherUniversityAppointment() {
		OtherUniversityAppointment oua = new OtherUniversityAppointment();
		oua.setStartDate(new Date());
		oua.setEndDate(Utils.getMaxDate());
		oua.setOrganizationUnit(Utils.getAuthUser().getDepartment().getKey());
		oua.setOrganizationUnitText(Utils.getAuthUser().getDepartment()
				.getName());
		oua.setProfile(this);
		oua.setRecordNumber(Constants.DUMMY_RECORD_NUMBER);
		oua.setSubtype(OtherUniversityAppointment.SUBTYPE_PRIMARY);
		oua.setAppointmentType("");
		oua.setAppointmentTypeText("");
		otherUnivAppts.add(oua);
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public boolean isCustomRecord() {
		return source.getSource().equals(FeedSource.FEED_SOURCE_HRIS_PSEUDO);
	}

	@Override
	public boolean equals(Object o) {
		boolean retval = false;
		try {
			Profile other = (Profile) o;
			retval = this.id.equals(other.getId());
		} catch (ClassCastException cce) {
			logger.catching(cce);
		}
		return retval;
	}

	@Override
	public int hashCode() {
		if (id == null) {
			return 0;
		}
		return id.hashCode();
	}

	/**
	 * 
	 * @param note
	 */
	public void addNote(Note note) {
		note.setProfile(this);
		notes.add(note);
	}

	/**
	 * 
	 * @param cp
	 */
	public void addComPref(CommPref cp) {
		cp.setProfile(this);
		commprefList.add(cp);
	}

	/**
	 * Helper to return a particular ComPref by Department
	 * 
	 * @param department
	 * @return
	 */
	@Transient
	public CommPref getCommprefForDepartment(Department department) {
		CommPref cp = null;

		for (CommPref c : commprefList) {
			if (c.getDepartment().getId().equals(department.getId())) {
				cp = c;
				break;
			}
		}

		return cp;
	}

	/**
	 * 
	 * @param department
	 * @return
	 */
	@Transient
	public Long getPreferredEmailIdForDepartment(Department department) {
		for (CommPref cp : commprefList) {
			if (cp.getDepartment().getId().equals(department.getId()) && cp.getEmail() != null) {
				return cp.getEmail().getId();
			}
		}
		return null;
	}

	/**
	 * 
	 * @param department
	 * @return
	 */
	@Transient
	public Long getPreferredTelephoneIdForDepartment(Department department) {
		for (CommPref cp : commprefList) {
			if (cp.getDepartment().getId().equals(department.getId()) && cp.getTelephone() != null) {
				return cp.getTelephone().getId();
			}
		}
		return null;
	}

	/**
	 * 
	 * @param department
	 * @return
	 */
	@Transient
	public Long getPreferredAddressIdForDepartment(Department department) {
		for (CommPref cp : commprefList) {
			if (cp.getDepartment().getId().equals(department.getId()) && cp.getAddress() != null) {
				return cp.getAddress().getId();
			}
		}
		return null;
	}

	/**
	 * 
	 * @param fromHris
	 */
	public void updateFromHrisFeed(Profile fromHris) {
		personalData.updateFromHrisFeed(fromHris.getPersonalData());
		updateActions(fromHris);
		updateAddresses(fromHris);
		updateEmails(fromHris);
		updateAppointmentDetails(fromHris);
		updateBasicPay(fromHris);
		updateContractElements(fromHris);
		updateOtherUnivAppts(fromHris);
		education.updateEducation(fromHris.getEducation());

	}

	/**
	 * 
	 * @param fromHris
	 */
	private void updateActions(Profile fromHris) {
		removeHrisDeletedActions(fromHris);
		boolean newEntity;

		for (Action ha : fromHris.getActions()) {
			newEntity = true;
			for (Action a : actions) {
				if (a.isSame(ha)) {
					newEntity = false;
					a.updateFromHris(ha);
					break;
				}
			}

			if (newEntity) {
				addAction(ha);
			}
		}
	}

	private void removeHrisDeletedActions(Profile fromHris) {
		List<Action> removeList = new ArrayList<Action>();

		Iterator<Action> actionIter = actions.iterator();
		while (actionIter.hasNext()) {
			Action existing = actionIter.next();
			boolean hrisDeleted = true;

			for (Action hrisAction : fromHris.getActions()) {
				if (existing.isSame(hrisAction)) {
					hrisDeleted = false;
					break;
				}
			}

			if (hrisDeleted) {
				// remove the Action
				removeList.add(existing);
			}
		}

		for (Action actionToRemove : removeList) {
			removeAction(actionToRemove);
		}
	}

	private void removeHrisDeletedAddresses(Profile fromHris) {
		List<Address> removeList = new ArrayList<Address>();

		Iterator<Address> iter = addresses.iterator();
		while (iter.hasNext()) {
			Address existing = iter.next();
			if (existing.getCustomField() == null) {			
				boolean hrisDeleted = true;
	
				for (Address hrisAddress : fromHris.getAddresses()) {
					// this is for payroll address since we are updating payroll
					// address end date we need check it differently
					if ((hrisAddress.getAddressType().equals(
							Address.ADDRESS_TYPE_PAYROLL) && existing.isSamePayrollAddress(hrisAddress)) || existing.isSame(hrisAddress)) {
						hrisDeleted = false;
						break;
					}
				}
	
				if (hrisDeleted) {
					removeList.add(existing);
				}
			}
			
		}

		for (Address toRemove : removeList) {
			removeAddress(toRemove);
		}
	}

	private void removeHrisDeletedAppointmentDetails(Profile fromHris) {
		List<AppointmentDetails> removeList = new ArrayList<AppointmentDetails>();

		Iterator<AppointmentDetails> iter = appointmentDetails.iterator();
		while (iter.hasNext()) {
			AppointmentDetails existing = iter.next();
			boolean hrisDeleted = true;

			for (AppointmentDetails hrisEntity : fromHris
					.getAppointmentDetails()) {
				if (existing.matches(hrisEntity)) {
					hrisDeleted = false;
					break;
				}
			}

			if (hrisDeleted) {
				removeList.add(existing);
			}
		}

		for (AppointmentDetails toRemove : removeList) {
			removeAppointmentDetails(toRemove);
		}
	}

	private void removeHrisDeletedBasicPays(Profile fromHris) {
		List<BasicPay> removeList = new ArrayList<BasicPay>();

		Iterator<BasicPay> iter = basicPay.iterator();
		while (iter.hasNext()) {
			BasicPay existing = iter.next();
			boolean hrisDeleted = true;

			for (BasicPay hrisBasicPay : fromHris.getBasicPay()) {
				if (existing.isSame(hrisBasicPay)) {
					hrisDeleted = false;
					break;
				}
			}

			if (hrisDeleted) {
				removeList.add(existing);
			}
		}

		for (BasicPay toRemove : removeList) {
			removeBasicPay(toRemove);
		}
	}

	private void removeHrisDeletedContractElements(Profile fromHris) {
		List<ContractElement> removeList = new ArrayList<ContractElement>();

		Iterator<ContractElement> iter = contractElements.iterator();
		while (iter.hasNext()) {
			ContractElement existing = iter.next();
			boolean hrisDeleted = true;

			for (ContractElement hrisEntity : fromHris.getContractElements()) {
				if (existing.isSame(hrisEntity)) {
					hrisDeleted = false;
					break;
				}
			}

			if (hrisDeleted) {
				removeList.add(existing);
			}
		}

		for (ContractElement toRemove : removeList) {
			removeContractElement(toRemove);
		}
	}

	/**
	 * When updating a Profile we need to remove existing CrossAppointment
	 * entities which are no longer valid. This method removes any existing
	 * entity which does not appear in <code>fromHris</code>.
	 * 
	 * @param fromHris
	 */
	public void removeHrisDeletedCrossAppointments(
			Set<CrossAppointment> fromHris) {
		List<CrossAppointment> removeList = new ArrayList<CrossAppointment>();

		Iterator<CrossAppointment> iter = crossAppointments.iterator();
		while (iter.hasNext()) {
			CrossAppointment existing = iter.next();
			boolean hrisDeleted = true;

			for (CrossAppointment hrisEntity : fromHris) {
				if (existing.equals(hrisEntity)) {
					hrisDeleted = false;
					break;
				}
			}

			if (hrisDeleted) {
				removeList.add(existing);
			}
		}

		for (CrossAppointment toRemove : removeList) {
			removeCrossAppointment(toRemove);
		}
	}

	/**
	 * When updating a Profile we need to remove existing
	 * BudgetaryCrossAppointment entities which are no longer valid. This method
	 * removes any existing entity which does not appear in
	 * <code>fromHris</code> parameter.
	 * 
	 * @param fromHris
	 */
	public void removeHrisDeletedBudgetaryCrossAppointments(
			Set<BudgetaryCrossAppointment> fromHris) {
		List<BudgetaryCrossAppointment> removeList = new ArrayList<BudgetaryCrossAppointment>();

		Iterator<BudgetaryCrossAppointment> iter = budgetaryCrossAppointments
				.iterator();
		while (iter.hasNext()) {
			BudgetaryCrossAppointment existing = iter.next();
			boolean hrisDeleted = true;

			for (BudgetaryCrossAppointment hrisEntity : fromHris) {
				if (existing.equals(hrisEntity)) {
					hrisDeleted = false;
					break;
				}
			}

			if (hrisDeleted) {
				removeList.add(existing);
			}
		}

		for (BudgetaryCrossAppointment toRemove : removeList) {
			removeBudgetaryCrossAppointment(toRemove);
		}
	}

	private void removeHrisDeletedEmails(Profile fromHris) {
		List<Email> removeList = new ArrayList<Email>();

		Iterator<Email> iter = emails.iterator();
		while (iter.hasNext()) {
			Email existing = iter.next();
			if (existing.getCustomField() == null && existing.getPhoneBookEmail() == null) {			

				boolean hrisDeleted = true;
	
				for (Email hrisEntity : fromHris.getEmails()) {
					if (existing.isSame(hrisEntity)) {
						hrisDeleted = false;
						break;
					}
				}
	
				if (hrisDeleted) {
					removeList.add(existing);
				}
			}
		}

		for (Email toRemove : removeList) {
			removeEmail(toRemove);
		}
	}

	private void removeHrisDeletedOtherUnivAppts(Profile fromHris) {
		List<OtherUniversityAppointment> removeList = new ArrayList<OtherUniversityAppointment>();

		Iterator<OtherUniversityAppointment> iter = otherUnivAppts.iterator();
		while (iter.hasNext()) {
			OtherUniversityAppointment existing = iter.next();
			boolean hrisDeleted = true;

			for (OtherUniversityAppointment hrisEntity : fromHris
					.getOtherUnivAppts()) {
				if (existing.isSame(hrisEntity)) {
					hrisDeleted = false;
					break;
				}
			}

			if (hrisDeleted) {
				removeList.add(existing);
			}
		}

		for (OtherUniversityAppointment toRemove : removeList) {
			removeOtherUniversityAppointment(toRemove);
		}
	}

	/**
	 * 
	 * @param fromHris
	 */
	private void updateAddresses(Profile fromHris) {
		removeHrisDeletedAddresses(fromHris);
		boolean newEntity;

		for (Address ha : fromHris.getAddresses()) {
			newEntity = true;
			for (Address a : addresses) {
				// this is for payroll address since we are updating payroll
				// address end date we need check it differently
				if((a.getAddressType().equals(Address.ADDRESS_TYPE_PAYROLL) && a.isSamePayrollAddress(ha)) || a.isSame(ha)) {
					newEntity = false;
					a.updateFromHris(ha);
					break;
				}
			}

			if (newEntity) {
				addAddress(ha);
			}
		}
	}

	/**
	 * 
	 * @param fromHris
	 */
	private void updateEmails(Profile fromHris) {
		removeHrisDeletedEmails(fromHris);
		boolean newEntity;

		for (Email he : fromHris.getEmails()) {
			newEntity = true;
			for (Email e : emails) {
				if (e.isSame(he)) {
					newEntity = false;
					e.updateFromHris(he);
					break;
				}
			}

			if (newEntity) {
				addEmail(he);
			}
		}
	}
	
	/**
	 * 
	 * @param fromHris
	 */
	private void updateAppointmentDetails(Profile fromHris) {
		removeHrisDeletedAppointmentDetails(fromHris);
		boolean newEntity;

		for (AppointmentDetails ha : fromHris.getAppointmentDetails()) {
			newEntity = true;
			for (AppointmentDetails a : appointmentDetails) {
				if (a.matches(ha)) {
					newEntity = false;
					a.updateFromHris(ha);
					break;
				}
			}

			if (newEntity) {
				addAppoinmentDetail(ha);
			}
		}

	}

	/**
	 * 
	 * @param fromHris
	 */
	private void updateBasicPay(Profile fromHris) {
		removeHrisDeletedBasicPays(fromHris);
		boolean newEntity;

		for (BasicPay hb : fromHris.getBasicPay()) {
			newEntity = true;
			for (BasicPay b : basicPay) {
				if (b.isSame(hb)) {
					b.updateFromHris(hb);
					newEntity = false;
					break;
				}
			}

			if (newEntity) {
				addBasicPay(hb);
			}
		}
	}

	/**
	 * 
	 * @param fromHris
	 */
	private void updateContractElements(Profile fromHris) {
		removeHrisDeletedContractElements(fromHris);
		boolean newEntity;

		for (ContractElement hc : fromHris.getContractElements()) {
			newEntity = true;
			for (ContractElement c : contractElements) {
				if (c.isSame(hc)) {
					newEntity = false;
					c.updateFromHris(hc);
					break;
				}
			}

			if (newEntity) {
				addContractElement(hc);
			}
		}
	}

	/**
	 * 
	 * @param fromHris
	 */
	private void updateOtherUnivAppts(Profile fromHris) {
		removeHrisDeletedOtherUnivAppts(fromHris);
		boolean newEntity;

		for (OtherUniversityAppointment ho : fromHris.getOtherUnivAppts()) {
			newEntity = true;
			for (OtherUniversityAppointment o : otherUnivAppts) {
				if (o.isSame(ho)) {
					newEntity = false;
					o.updateFromHris(ho);
					break;
				}
			}

			if (newEntity) {
				addOtherUniversityAppointment(ho);
			}
		}

		List<OtherUniversityAppointment> removeOuaList = new ArrayList<OtherUniversityAppointment>();
		Iterator<OtherUniversityAppointment> itOua = otherUnivAppts.iterator();
		while (itOua.hasNext()) {
			OtherUniversityAppointment o = itOua.next();
			boolean hrisDeleted = true;

			for (OtherUniversityAppointment ho : fromHris.getOtherUnivAppts()) {
				if (o.isSame(ho)) {
					hrisDeleted = false;
					break;
				}
			}

			if (hrisDeleted) {
				removeOuaList.add(o);
			}
		}

		for (OtherUniversityAppointment oua : removeOuaList) {
			removeOtherUniversityAppointment(oua);
		}
	}

	private void removeAction(Action action) {
		actions.remove(action);
		action.setProfile(null);
	}

	private void removeAppointmentDetails(AppointmentDetails ad) {
		appointmentDetails.remove(ad);
		ad.setProfile(null);
	}

	private void removeAddress(Address address) {
		removeCommPrefsForAddress(address);
		addresses.remove(address);
		address.setProfile(null);
	}

	private void removeBasicPay(BasicPay bp) {
		basicPay.remove(bp);
		bp.setProfile(null);
	}

	private void removeCommPrefsForAddress(Address address) {
		for (CommPref cp : commprefList) {
			if (cp.getAddress() != null && cp.getAddress().equals(address)) {
				cp.setAddress(null);
				cp.setAddressType(null);
				address.getCommPrefList().remove(cp);
			}
		}
	}

	private void removeCommPrefsForEmail(Email email) {
		for (CommPref cp : commprefList) {
			if (cp.getEmail() != null && cp.getEmail().getId().equals(email.getId())) {
				cp.setEmail(null);
				cp.setEmailType(null);
				email.getCommPrefList().remove(cp);
			}
		}
	}

	private void removeContractElement(ContractElement ce) {
		contractElements.remove(ce);
		ce.setProfile(null);
	}

	private void removeCrossAppointment(CrossAppointment ca) {
		crossAppointments.remove(ca);
		ca.setProfile(null);
	}

	private void removeEmail(Email email) {
		removeCommPrefsForEmail(email);
		emails.remove(email);
		email.setProfile(null);
	}

	private void removeOtherUniversityAppointment(OtherUniversityAppointment oua) {
		otherUnivAppts.remove(oua);
		oua.setProfile(null);
	}

	/**
	 * @return the notesDto
	 */
	@Transient
	public List<NoteDTO> getNotesDto() {
		return notesDto;
	}

	/**
	 * @param notesDto
	 *            the notesDto to set
	 */
	public void setNotesDto(List<NoteDTO> notesDto) {
		this.notesDto = notesDto;
	}

	/**
	 * 
	 */
	public void setFullName() {
		String fullName = new StringBuilder(personalData.getFirstName())
				.append(" ").append(personalData.getLastName()).toString();
		personalData.setFullName(fullName);
	}

	/**
	 * 
	 */
	public void createNewExtractedFields() {
		extractedFields = new ExtractedFields(this);
	}

	public void addHrisRuleEvent(HrisRuleEvent hre) {
		hre.setProfile(this);
		hrisRuleEventList.add(hre);
	}

	public void clearHrisRuleEvents() {
		hrisRuleEventList.clear();
	}

	public void updateFacultyWideEmail(Email email) {
		facultyWideData.setEmailAddress(email.getEmailAddress());

	}

	@Transient
	public String getFacultyWideEmailAddress() {
		String fwEmailAddress = "";
		if (facultyWideData != null
				&& facultyWideData.getEmailAddress() != null) {
			fwEmailAddress = facultyWideData.getEmailAddress();
		}
		return fwEmailAddress;
	}

	public void addNoteDTO(NoteDTO dto) {
		if (dto != null) {
			if (notesDto == null) {
				notesDto = new ArrayList<NoteDTO>();
			}
			notesDto.add(dto);
		}
	}
}
