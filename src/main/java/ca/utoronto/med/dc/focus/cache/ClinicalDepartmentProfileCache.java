package ca.utoronto.med.dc.focus.cache;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import ca.utoronto.med.dc.focus.domain.Department;
import ca.utoronto.med.dc.focus.domain.Profile;

public class ClinicalDepartmentProfileCache {
	private List<Department> clinicalDepts = new ArrayList<Department>();
	private Map<Long, List<Long>> deptProfilesMap = new HashMap<Long, List<Long>>();
	private Map<Long, List<Long>> profileDeptMap = null;

	public ClinicalDepartmentProfileCache() {
	}

	public List<Department> getClinicalDepts() {
		return clinicalDepts;
	}

	public void setClinicalDepts(List<Department> clinicalDepts) {
		this.clinicalDepts = clinicalDepts;
	}

	public Map<Long, List<Long>> getDeptProfilesMap() {
		return deptProfilesMap;
	}

	public void setDeptProfilesMap(Map<Long, List<Long>> deptProfilesMap) {
		this.deptProfilesMap = deptProfilesMap;
		initProfileDeptMap();
	}

	private void initProfileDeptMap() {
		this.profileDeptMap = new HashMap<Long, List<Long>>();
		for(Long deptId: this.deptProfilesMap.keySet()) {
			for(Long profileId: this.deptProfilesMap.get(deptId)) {
				if(!this.profileDeptMap.containsKey(profileId)) {
					this.profileDeptMap.put(profileId, new ArrayList<Long>());
				}
				this.profileDeptMap.get(profileId).add(deptId);
			}
		}
	}

	public void clear() {
		profileDeptMap = null;
		if (clinicalDepts != null) {
			clinicalDepts.clear();
		}

		if (deptProfilesMap != null) {
			deptProfilesMap.clear();
		}
	}

	public List<Long> getDepartmentsForProfile(Profile p) {
		List<Long> depts = new ArrayList<Long>();
		if(profileDeptMap != null && !profileDeptMap.isEmpty() && profileDeptMap.containsKey(p.getId())) {
			depts = profileDeptMap.get(p.getId());
		}
		return depts;
	}

}
