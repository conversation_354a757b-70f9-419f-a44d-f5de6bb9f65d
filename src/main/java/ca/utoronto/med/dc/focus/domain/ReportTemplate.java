package ca.utoronto.med.dc.focus.domain;

import java.text.SimpleDateFormat;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;

import ca.utoronto.med.dc.common.exception.FocusException;

/**
 * Entity for Report templates
 * 
 */

@Entity
@Table(name = "REPORT_TEMPLATE")
public class ReportTemplate extends Template {

	private Long id;

	// @OrderColumn
	private Set<ReportField> reportFields = new HashSet<ReportField>();

	private AuthUser reportOwner;
	// Transient
	private boolean readOnly;

	/**
	 * @return the id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.AUTO)
	@Column(name = "REPORTTEMPLATE_ID")
	public Long getId() {
		return id;
	}

	/**
	 * @param id
	 *            the id to set
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * @return the reportOwner
	 */
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USERS_ID", nullable = false)
	@org.hibernate.annotations.ForeignKey(name = "FK_REPORT__USERS_ID")
	public AuthUser getReportOwner() {
		return reportOwner;
	}

	/**
	 * @param reportOwner
	 *            the reportOwner to set
	 */
	public void setReportOwner(AuthUser reportOwner) {
		this.reportOwner = reportOwner;
	}

	/**
	 * @return the reportFields
	 */
	@OneToMany(mappedBy = "reportTemplate", cascade = { CascadeType.PERSIST,
			CascadeType.MERGE, CascadeType.DETACH, CascadeType.REMOVE }, orphanRemoval = true)
	public Set<ReportField> getReportFields() {
		return reportFields;
	}

	/**
	 * @param reportFields
	 *            the reportFields to set
	 */
	public void setReportFields(Set<ReportField> reportFields) {
		this.reportFields = reportFields;
	}

	/**
	 * 
	 * @param field
	 */
	@Transient
	public void addReportField(FieldDef field, int order) {
		if (field == null) {
			throw new FocusException("Invalid field added to report");
		}
		ReportField rf = new ReportField(this, field, order);

		reportFields.add(rf);
		field.getReportFields().add(rf);
	}

	/**
	 * 
	 * @return
	 */
	@Transient
	public String getUpdateDateFormatted() {
		SimpleDateFormat sdf = new SimpleDateFormat("MMMM dd, yyyy");
		if (getUpdatedOn() != null) {
			return sdf.format(getUpdatedOn());
		} else {
			return "";
		}

	}

	/**
	 * 
	 * @param idList
	 */
	public void addReportFields(String[] idList) {
		int i = 1;
		if (idList != null) {
			for (String id : idList) {
				FieldDef fd = new FieldDef(Long.parseLong(id));
				addReportField(fd, i++);
			}
		}
	}
	
	@Transient
	public boolean isReadOnly() {
		return readOnly;
	}
	
	public void setReadOnly(boolean readOnly) {
		this.readOnly = readOnly;
	}

	/**
	 * 
	 */
	@Override
	public String toString() {
		StringBuilder result = new StringBuilder();
		String newLine = System.getProperty("line.separator");

		result.append(this.getClass().getName() + " Object {" + newLine);
		result.append(" id: " + id + newLine);
		result.append("}");

		return result.toString();
	}
}
