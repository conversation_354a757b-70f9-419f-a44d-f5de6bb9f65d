/**
 * 
 */
package ca.utoronto.med.dc.common.exception;

/**
 * The base class of all Data Access exceptions. This class may be sub-classed for more 
 * specific exceptions.
 * 
 * <AUTHOR>
 *
 */
public class FocusDAOException extends FocusException {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	public static final String UNIQUE_CONSTRAINT_VIOLATION = "UNIQUE_CONSTRAINT_VIOLATION";
	public static final String NO_RESULT_FOUND = "NO_RESULT_FOUND";
	public static final String VALIDATION_CONSTRAINT_VIOLATION = "VALIDATION_CONSTRAINT_VIOLATION";
	
	private String internalCause = "";
	
	/**
	 * @param s
	 */
	public FocusDAOException(String s) {
		super(s);
		// TODO Auto-generated constructor stub
	}

	/**
	 * @param s
	 * @param root
	 */
	public FocusDAOException(String s, Throwable root) {
		super(s, root);
		// TODO Auto-generated constructor stub
	}

	/**
	 * @param root
	 */
	public FocusDAOException(Throwable root) {
		super(root);
		// TODO Auto-generated constructor stub
	}

	public FocusDAOException(String s, Throwable root, String internalCause) {		
		super(s, root);
		this.internalCause = internalCause;
	}
	
	/**
	 * @return the internalCause
	 */
	public String getInternalCause() {
		return internalCause;
	}

	/**
	 * @param internalCause the internalCause to set
	 */
	public void setInternalCause(String internalCause) {
		this.internalCause = internalCause;
	}

	/**
	 * 
	 * @return True if this is actually a CVE
	 */
	public boolean isConstraintViolation() {
		return internalCause.equals(UNIQUE_CONSTRAINT_VIOLATION);
	}

	/**
	 * 
	 * @return
	 */
	public boolean isNoResultFoundException() {
		return internalCause.equals(NO_RESULT_FOUND);
	}
	
	public boolean isValidationException() {
		return internalCause.equalsIgnoreCase(VALIDATION_CONSTRAINT_VIOLATION);
	}

}
