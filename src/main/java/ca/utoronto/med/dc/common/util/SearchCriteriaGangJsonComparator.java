/**
 * 
 */
package ca.utoronto.med.dc.common.util;

import java.util.Comparator;

import ca.utoronto.med.dc.focus.domain.SearchCriteriaGang;
/**
 * <AUTHOR>
 *
 */
public class SearchCriteriaGangJsonComparator implements Comparator<SearchCriteriaGang>{

	@Override
	public int compare(SearchCriteriaGang gang1, SearchCriteriaGang gang2) {
		return gang1.getDisplayOrder().compareTo(gang2.getDisplayOrder());
	}
}
