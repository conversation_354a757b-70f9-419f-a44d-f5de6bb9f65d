package ca.utoronto.med.dc.common.util;

import java.math.BigInteger;
import java.util.Date;
import java.util.Set;

import javax.xml.datatype.DatatypeConfigurationException;

import org.slf4j.ext.XLogger;
import org.slf4j.ext.XLoggerFactory;

import ca.utoronto.med.dc.focus.domain.CustomAppointment;
import ca.utoronto.med.dc.focus.domain.OtherUniversityAppointment;
import ca.utoronto.med.dc.focus.domain.Profile;
import ca.utoronto.med.dc.focus.jaxb.insight.AppointmentJxb;
import ca.utoronto.med.dc.focus.jaxb.insight.Appointments;
import ca.utoronto.med.dc.focus.jaxb.insight.ProfileJxb;

public class Profile2XMLForInsightHelper {
	/**
	 * 
	 */
	private static XLogger logger = XLoggerFactory
			.getXLogger(Profile2XMLForInsightHelper.class.getName());

	/**
	 * 
	 * @param profile
	 * @param ouas
	 * @return
	 * @throws DatatypeConfigurationException
	 */
	public static ProfileJxb getXMLRepresentation(Profile profile,
			Set<OtherUniversityAppointment> ouas, Long cpso)
			throws DatatypeConfigurationException {
		logger.entry();

		ProfileJxb p = new ProfileJxb();
		p.setFocusId(BigInteger.valueOf(profile.getId()));
		if (profile.getWebcvBridge() != null) {
			p.setWebCVId(BigInteger.valueOf(profile.getWebcvBridge()
					.getWebcvId()));
		}
		p.setFirstName(profile.getPersonalData().getFirstName());
		p.setMiddleName(profile.getPersonalData().getMiddleName());
		p.setLastName(profile.getPersonalData().getLastName());
		if (cpso != null) {
			p.setCpso(BigInteger.valueOf(cpso));
		} else {
			p.setCpso(null);
		}
		p.setEmail(profile.getFacultyWideEmailAddress());

		p.setAppointments(new Appointments());

		if (ouas != null) {
			// Primary Appointment
			AppointmentJxb primaryAppointment = getPrimaryAppointment(ouas);
			if (primaryAppointment != null) {
				p.getAppointments().getAppointmentJxb().add(primaryAppointment);
			}

			for (OtherUniversityAppointment oua : ouas) {
				if ((primaryAppointment == null)
						|| (primaryAppointment != null && !oua.getId().equals(
								primaryAppointment.getFocusAppointmentId()
										.longValue()))) {
					if (subTypeOK(oua.getSubtype())) {
						AppointmentJxb apt = new AppointmentJxb();
						apt.setFocusAppointmentId(BigInteger.valueOf(oua
								.getId()));
						apt.setDepartment(oua.getOrganizationUnitText());
						apt.setStartDate(Utils.fromDateToXMLGregorian(oua
								.getStartDate()));
						apt.setEndDate(Utils.fromDateToXMLGregorian(oua
								.getEndDate()));
						apt.setSubtypeText(oua.getSubtypeText());
						apt.setPrimary(false);

						p.getAppointments().getAppointmentJxb().add(apt);
					}
				}
			}
		}
		logger.exit();
		return p;
	}

	public static ProfileJxb getXMLRepresentation(Profile profile,
			Set<OtherUniversityAppointment> ouas,
			Set<CustomAppointment> customAppts, Long cpso)
			throws DatatypeConfigurationException {
		logger.entry();

		ProfileJxb p = getXMLRepresentation(profile, ouas, cpso);
		for (CustomAppointment customAppointment : customAppts) {
			AppointmentJxb apt = new AppointmentJxb();
			apt.setFocusAppointmentId(BigInteger.valueOf(customAppointment
					.getId()));
			apt.setDepartment(customAppointment.getDepartmentName());
			apt.setStartDate(Utils.fromDateToXMLGregorian(customAppointment
					.getStartDate()));
			apt.setEndDate(Utils.fromDateToXMLGregorian(customAppointment
					.getEndDate()));
			apt.setSubtypeText(CustomAppointment.SUB_TYPE_TEXT);
			apt.setPrimary(customAppointment.isPrimary());

			p.getAppointments().getAppointmentJxb().add(apt);
		}
		logger.exit();
		return p;
	}

	/**
	 * 
	 * @param subtype
	 * @return
	 */
	private static boolean subTypeOK(String subtype) {
		if (subtype.equals("ADJ") || subtype.equals("CLN")
				|| subtype.equals("EMR") || subtype.equals("NON")
				|| subtype.equals("STA") || subtype.equals("CCA")) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 
	 * @param ouas
	 * @return
	 * @throws DatatypeConfigurationException
	 */
	private static AppointmentJxb getPrimaryAppointment(
			Set<OtherUniversityAppointment> ouas)
			throws DatatypeConfigurationException {
		Date today = new Date();

		String orgUnit = "";
		for (OtherUniversityAppointment oua : ouas) {
			if (oua.getEndDate().after(today)
					&& oua.getSubtype().equals(
							OtherUniversityAppointment.SUBTYPE_PRIMARY)) {
				orgUnit = oua.getOrganizationUnit();
				break;
			}
		}

		OtherUniversityAppointment primaryAppointment = null;
		if (!orgUnit.isEmpty()) {
			for (OtherUniversityAppointment oua : ouas) {
				if (!oua.getSubtype().equals(
						OtherUniversityAppointment.SUBTYPE_PRIMARY)
						&& oua.getOrganizationUnitText() != null
						&& !oua.getOrganizationUnitText().isEmpty()
						&& oua.getEndDate().after(new Date())
						&& oua.getOrganizationUnit().equals(orgUnit)) {
					primaryAppointment = oua;
				}
			}
		}

		if (primaryAppointment != null) {
			AppointmentJxb apt = new AppointmentJxb();
			apt.setFocusAppointmentId(BigInteger.valueOf(primaryAppointment
					.getId()));
			apt.setDepartment(primaryAppointment.getOrganizationUnitText());
			apt.setStartDate(Utils.fromDateToXMLGregorian(primaryAppointment
					.getStartDate()));
			apt.setEndDate(Utils.fromDateToXMLGregorian(primaryAppointment
					.getEndDate()));
			apt.setSubtypeText(primaryAppointment.getSubtypeText());
			apt.setPrimary(true);
			return apt;
		}
		return null;
	}
}
