package ca.utoronto.med.dc.common.util;

public class Constants {
	// RESOURCE_TYPE table
	public static final String ORGANIZATION = "ORGANIZATION";
	public static final String GROUP = "GROUP";
	public static final String FACULTY_MEMBER = "FACULTY_MEMBER";
	public static final String APPOINTMENT = "APPOINTMENT";	

	// JSP Model Attribute Names
	public static final String MODEL_ATTR_EXCEPTION = "exception";
	public static final String MODEL_ATTR_MESSAGE = "message";

	// JSP pages
	public static final String INLINE_ERRORS_JSP = "inline-errors.jsp";
	public static final String INLINE_ERRORS_STRING_JSP = "inline-errors-string.jsp";
	public static final String INLINE_HIGHLIGHT_JSP = "inline-highlight.jsp";

	// For concatenating error arrays
	public static final String ERRORS_MESSAGE_DELIMITER = "; ";

	// Navigation Structure
	public static final String ORGANIZATIONS = "organizations";
	public static final String ORGANIZATIONSITES = "organizationsites";
	public static final String GROUPS = "groups";
	public static final String SUBGROUPS = "subgroups";

	// Appointment Status
	public static final String STATUS_ACTIVE = "Active";
	public static final String STATUS_INACTIVE = "Inactive";

	// Mother of All Organizations
	public static final String FACULTY_OF_MEDICINE = "Faculty of Medicine";

	/**
	 * dd-MM-yyyy
	 */
	public static final String DATE_PICTURE = "dd-MM-yyyy";
	public static final String DATE_MAX_VALUE_STRING = "31-12-9999";

	public static final String DISCO = "Discovery Commons";
	public static final String DISCO_CONTACT_NAME = "Discovery Commons Helpdesk";
	public static final String DISCO_CONTACT_EMAIL = "<EMAIL>";
	public static final String DISCO_CONTACT_PHONE = "************";

	// used in advanced search/dynamic groups
	public static final Long FIELDDEF_UNSPECIFIED = -1L;

	public static final Long DUMMY_RECORD_NUMBER = 123123123L;

	// These strings allow help messages to refer to named elements visible to
	// the user.
	public static final String UI_SETTINGS_PANEL_TITLE = "My Settings";
	public static final String UI_MANAGE_YOUR_EMAIL_SETTINGS_TITLE = "Manage your Email Settings";
	public static final String UI_MANAGE_DEPT_EMAIL_SETTINGS_TITLE = "Manage Departmental Email Settings";

	// These messages are used for logging
	public static final String LOG_POTENTIAL_EXPLOITATION = "Potential exploitation attempt was made";

	// This is the maximum allowable number of parameters that may be bound for
	// a query.
	public static final int SQL_PARAMETER_LIMIT = 500;

	// Shibboleth headers
	public static final String EPPN = "eppn"; // email address

	// Offline jobs
	public static final String EMAIL_JOB = "EMAIL";
	public static final String FEED_IMPORT_REQUEST_MESSAGE = "Initiate a Feed Import";
	public static final String HRIS_CHANGE_LOG_EMAIL_JOB = "HRIS CHANGE LOG EMAIL";
	public static final String HRIS_CHANGE_LOG_REPORT_NAME = "HRIS_change_log_report";

	// Insight Integration
	public static final String APPOINTMENTS_EXPORT_REQUEST_MESSAGE = "Initiate Profile Appointments Export";

	public static final String UPDATED_BY_SYSTEM = "SYSTEM";
	// Default size of date range in days for searching and reporting HRIS Change Log
	public static final int HRIS_CHANGE_LOG_DATE_INTERVAL_DAYS = 7;
	
	public static final int EXCEL_ROW_LIMIT = 60000;
	
	public static final String RESTRICTED_DATA_MSG = "HRIS Restricted Data has changed";
	public static final String STRING_ACCESS_DENIED = "Access Denied";
	public static final String ERROR_MESSAGE_ACCESS_DENIED = "Your session has expired or you do not have sufficient authority to access this resource.";
	
	// The limit to return for autocomplete queries
	public static final int AUTOCOMPLETE_MAX = 500;
	
	// Cross appointment field def path indicator
	public static final String CROSS_APPOINTMENT_PATH = "crossAppointments.crossAppointment";
	
	// Budgetary appointment field def path indicator
	public static final String BUDGETARY_CROSS_APPOINTMENT_PATH = "budgetaryCrossAppointments.budgetaryCrossAppointment";
	public static final String GRAD_APPT_PATH = "education.gradAppt";

}
