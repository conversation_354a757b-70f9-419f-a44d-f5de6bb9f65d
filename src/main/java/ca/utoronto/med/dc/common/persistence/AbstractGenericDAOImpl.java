package ca.utoronto.med.dc.common.persistence;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityExistsException;
import javax.persistence.EntityManager;
import javax.persistence.LockModeType;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.PersistenceException;
import javax.persistence.TransactionRequiredException;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

import ca.utoronto.med.dc.common.exception.FocusDAOException;

/**
 * Implementation of <code>GenericDAO</code>.
 * 
 * @param <T>
 *            The type of the domain object for which this instance is to be
 *            used.
 * @param <ID>
 *            The type of the id of the domain object for which this instance is
 *            to be used.
 */
public abstract class AbstractGenericDAOImpl<T, ID extends Serializable>
		implements GenericDAO<T, ID> {
	/**
     *
     */
	private final transient Class<T> entityBeanType;

	/**
	 * @PersistenceContext will be set in the concrete sub-classes
	 */
	private EntityManager em;

	/**
     *
     */
	@SuppressWarnings("unchecked")
	public AbstractGenericDAOImpl() {
		this.entityBeanType = (Class<T>) ((ParameterizedType) getClass()
				.getGenericSuperclass()).getActualTypeArguments()[0];
	}

	/**
	 * @param entityManager
	 *            Entity Manager
	 */
	@PersistenceContext(unitName = "projectPU")
	protected void setEntityManager(final EntityManager em) {
		this.em = em;
	}

	/**
	 * @return EntityManager.
	 */
	protected final EntityManager getEntityManager() {
		return em;
	}

	/**
	 * @return .
	 */
	public final Class<T> getEntityBeanType() {
		return entityBeanType;
	}

	/**
	 * @param identifier
	 *            id.
	 * @param lock
	 *            lock.
	 * @return .
	 */
	public final T findById(final ID identifier, final boolean lock) {
		T entity;

		try {
			if (lock) {
				entity = (T) getEntityManager().find(getEntityBeanType(),
						identifier);
				getEntityManager().lock(entity, LockModeType.WRITE);
			} else {
				entity = (T) getEntityManager().find(getEntityBeanType(),
						identifier);
			}
		} catch (RuntimeException re) {
			throw new FocusDAOException("Cannot find entity", re);
		}

		return entity;
	}

	/**
	 * @param entity
	 *            entity.
	 * @return .
	 */
	public final T makePersistent(final T entity) {
		try {
			getEntityManager().persist(entity);
		} catch (EntityExistsException eee) {
			throw new FocusDAOException("Attempt to insert duplicate entity",
					eee);
		} catch (PersistenceException pe) {
			if (pe.getCause()
					.getClass()
					.getName()
					.equalsIgnoreCase(
							"org.hibernate.exception.ConstraintViolationException")) {
				throw new FocusDAOException(pe.getCause().getCause()
						.getMessage(), pe,
						FocusDAOException.UNIQUE_CONSTRAINT_VIOLATION);
			} else {
				throw new FocusDAOException(pe);
			}
		} catch (ConstraintViolationException cve) {
			List<ConstraintViolation<?>> cvList = new ArrayList<ConstraintViolation<?>>(cve.getConstraintViolations());
			throw new FocusDAOException(cvList.get(0).getMessage(), cve, FocusDAOException.VALIDATION_CONSTRAINT_VIOLATION);
		} catch (RuntimeException re) {
			throw new FocusDAOException(re);
		}
		return entity;
	}

	/**
	 * Deletes the DB row
	 * 
	 * @param entity
	 */
	public final void makeTransient(final T entity) {
		try {
			getEntityManager().remove(entity);
		} catch (RuntimeException re) {
			throw new FocusDAOException("Failed to delete entity. "
					+ re.getMessage(), re);
		}
	}

	/**
     *
     */
	public final void flush() {
		try {
			getEntityManager().flush();
		} catch (PersistenceException pe) {
			if (pe.getCause()
					.getClass()
					.getName()
					.equalsIgnoreCase(
							"org.hibernate.exception.ConstraintViolationException")) {
				throw new FocusDAOException(pe.getCause().getCause()
						.getMessage(), pe,
						FocusDAOException.UNIQUE_CONSTRAINT_VIOLATION);
			}
		} catch (RuntimeException re) {
			throw new FocusDAOException(re);
		}
	}

	/**
     *
     */
	public final void clear() {
		getEntityManager().clear();
	}

	/**
	 * 
	 */
	public T mergeState(T entity, boolean flush) {
		try {
			T t = getEntityManager().merge(entity);
			if (flush) {
				flush();
			}

			return t;
		} catch (IllegalStateException ise) {
			throw new FocusDAOException(ise);
		} catch (TransactionRequiredException tre) {
			throw new FocusDAOException(tre);
		} catch (PersistenceException pe) {
			if (pe.getCause()
					.getClass()
					.getName()
					.equalsIgnoreCase(
							"org.hibernate.exception.ConstraintViolationException")) {
				throw new FocusDAOException(pe.getCause().getCause()
						.getMessage(), pe,
						FocusDAOException.UNIQUE_CONSTRAINT_VIOLATION);
			} else {
				throw new FocusDAOException(pe);
			}
		} catch (IllegalArgumentException ile) {
			throw new FocusDAOException(ile);
		}
	}

	/**
	 * 
	 * @param entity
	 */
	public void detach(T entity) {
		try {
			getEntityManager().detach(entity);
		} catch (IllegalArgumentException iae) {
			throw new FocusDAOException(iae);
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * ca.utoronto.med.dc.common.persistence.GenericDAO#isFiledValueUnique(java.
	 * lang.String, java.lang.Object)
	 */
	@Override
	public boolean isFiledValueUnique(String entityClassName, String field,
			Object value) {
		boolean unique = true;

		try {
			getEntityManager()
					.createQuery(
							"select 1 from " + entityClassName + " e where e."
									+ field + " = :fieldValue")
					.setParameter("fieldValue", value).getSingleResult();

			unique = false;
		} catch (NoResultException nre) {
			// do nothing
		} catch (RuntimeException re) {
			throw new FocusDAOException(re);
		}

		return unique;
	}

}
