/**
 * 
 */
package ca.utoronto.med.dc.common.exception;

/**
 * Base class for all Service related (business logic) exceptions. 
 * <AUTHOR>
 *
 */
public class FocusServiceException extends FocusException {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * @param s
	 */
	public FocusServiceException(String s) {
		super(s);
		// TODO Auto-generated constructor stub
	}

	/**
	 * @param s
	 * @param root
	 */
	public FocusServiceException(String s, Throwable root) {
		super(s, root);
		// TODO Auto-generated constructor stub
	}

	/**
	 * @param root
	 */
	public FocusServiceException(Throwable root) {
		super(root);
		// TODO Auto-generated constructor stub
	}

}
