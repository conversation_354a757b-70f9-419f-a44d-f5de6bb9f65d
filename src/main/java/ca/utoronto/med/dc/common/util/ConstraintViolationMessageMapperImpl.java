package ca.utoronto.med.dc.common.util;

import java.util.Properties;

import javax.annotation.Resource;

import org.slf4j.ext.XLogger;
import org.slf4j.ext.XLoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class ConstraintViolationMessageMapperImpl implements
		ConstraintViolationMessageMapper {
	
	private XLogger logger = XLoggerFactory.getXLogger(ConstraintViolationMessageMapperImpl.class
			.getName());
	
	/**
	 * 
	 */
	@Resource(name = "projectProperties")
	private Properties props;

	/**
	 * 
	 */
	@Override
	public String getMessage(String cvMessage) {
		logger.entry();
		
		String delimiter = "'";
		String[] tokens = null;
		if (cvMessage != null && cvMessage.contains(delimiter)) {
			tokens = cvMessage.split(delimiter); 
		}
		
		logger.exit();
		return props.getProperty(tokens[1]);
	}	
	
	
/*	*//**
	 * returns a message string from project.properties
	 *//*
	@Override
	public String getMessage(String msg) {
		msg = msg.substring(msg.indexOf("'") + 1);

		int index = msg.indexOf("'");
		msg = msg.substring(0, index);

		String cv = props.getProperty(msg);
		if (cv == null) {
			cv = "Constraint Violation";
		}
		return cv;
	}*/

}
