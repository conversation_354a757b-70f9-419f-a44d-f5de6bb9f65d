package ca.utoronto.med.dc.common.util;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeConstants;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;

import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetails;

import ca.utoronto.med.dc.focus.domain.AuthUser;
import ca.utoronto.med.dc.focus.domain.Authority;
import ca.utoronto.med.dc.focus.domain.Department;
import ca.utoronto.med.dc.focus.domain.Profile;
import ca.utoronto.med.dc.focus.service.FeedImportStatus;

public class Utils {

	/**
	 * 
	 * @return
	 */
	public static Authentication getAuthentication() {
		return SecurityContextHolder.getContext().getAuthentication();
	}

	/**
	 * 
	 * @return
	 */
	public static WebAuthenticationDetails getAuthenticationDetails() {
		return (WebAuthenticationDetails) SecurityContextHolder.getContext()
				.getAuthentication().getDetails();
	}

	/**
	 * 
	 * @return
	 */
	public static AuthUser getAuthUser() {
		AuthUser aUser = null;
		SecurityContext context = SecurityContextHolder.getContext();
		Authentication authentication = context.getAuthentication();
		if (authentication != null) {
			Object principal = authentication.getPrincipal();

			aUser = (AuthUser) principal;
		}
		return aUser;
	}

	/**
	 * 
	 * @return
	 */
	public static String getUsername() {
		String auth = "";
		try {
			auth = ((UserDetails) SecurityContextHolder.getContext()
					.getAuthentication().getPrincipal()).getUsername();
		} catch (NullPointerException npe) {

		}
		return auth;
	}

	/**
	 * returns true if user has the authority "role"
	 */
	public static boolean isUserInRole(String role) {
		Authentication auth = SecurityContextHolder.getContext()
				.getAuthentication();

		for (GrantedAuthority ga : auth.getAuthorities()) {
			if (ga.getAuthority().equalsIgnoreCase(role)) {
				return true;
			}
		}

		return false;
	}

	/**
	 * returns true if user has the authority "role"
	 */
	public static boolean isUserInRole(List<String> roles) {
		Authentication auth = SecurityContextHolder.getContext()
				.getAuthentication();

		for (String role : roles) {
			for (GrantedAuthority ga : auth.getAuthorities()) {
				if (ga.getAuthority().equalsIgnoreCase(role)) {
					return true;
				}
			}
		}

		return false;
	}

	/**
	 * Returns a Date object for Dec 31, 9999
	 * 
	 * @return
	 */
	public static Date getMaxDate() {
		Calendar cal = Calendar.getInstance();
		cal.clear();
		cal.set(Calendar.YEAR, 9999);
		cal.set(Calendar.MONTH, Calendar.DECEMBER);
		cal.set(Calendar.DAY_OF_MONTH, 31);
		return cal.getTime();
	}

	/**
	 * e.g. 31-Dec-2001
	 * 
	 * @param date
	 * @return
	 */
	public static String defaultDateFormat(Date date) {
		String dateString = "";
		if (date != null) {
			dateString = DateFormat.getDateInstance(DateFormat.DEFAULT,
					Locale.CANADA).format(date);
		}
		return dateString;
	}

	public static boolean isUserInEditProfileRole() {
		boolean perm = false;

		if (isUserInRole(Authority.ROLE_EDIT_DATA)) {
			perm = true;
		}

		return perm;
	}

	/**
	 * Breaks the provided list of profile ids into chunks which are guaranteed
	 * to be no larger than the allowed number of parameters which can be bound
	 * in a query. <code><pre>
	 *     for (List<Long> profileIds: Utils.chunkifyProfileIdList(humungousListOfIds)) {
	 *         // perform query operation
	 *     }
	 * </pre></code>
	 * 
	 * @param profileIds
	 * @return
	 */
	public static List<List<Long>> chunkifyProfileIdList(List<Long> profileIds) {
		List<List<Long>> profileIdListList = new ArrayList<List<Long>>();

		if (profileIds != null
				&& profileIds.size() > Constants.SQL_PARAMETER_LIMIT) {
			int numOfLists = profileIds.size() / Constants.SQL_PARAMETER_LIMIT;
			int remainder = profileIds.size() % Constants.SQL_PARAMETER_LIMIT;
			for (int i = 0; i < numOfLists; i++) {
				int begin = i * Constants.SQL_PARAMETER_LIMIT;
				int end = begin + Constants.SQL_PARAMETER_LIMIT;
				profileIdListList.add(profileIds.subList(begin, end));
			}
			if (remainder > 0) {
				profileIdListList.add(profileIds.subList(profileIds.size()
						- remainder, profileIds.size()));
			}
		} else {
			profileIdListList.add(profileIds);
		}
		return profileIdListList;
	}

	public static boolean isFeedImportInProgress() {
		return FeedImportStatus.getFeedImportStatus().isImportInProgress();
	}

	public static void requireFeedImportInProgress() {
		if (!isFeedImportInProgress()) {
			throw new AccessDeniedException(
					"Access Denied: This method can only be called from within the Feed Import Process");
		}
	}

	/**
	 * Not thoroughly tested, KM
	 * 
	 * @param dt
	 * @return XMLGregorianCalendar
	 * @throws DatatypeConfigurationException
	 */
	public static XMLGregorianCalendar fromDateToXMLGregorian(Date dt)
			throws DatatypeConfigurationException {
		GregorianCalendar gc = new GregorianCalendar();
		gc.clear();
		gc.setTimeInMillis(dt.getTime());

		XMLGregorianCalendar xmlDate = DatatypeFactory.newInstance()
				.newXMLGregorianCalendarDate(gc.get(Calendar.YEAR),
						gc.get(Calendar.MONTH) + 1,
						gc.get(Calendar.DAY_OF_MONTH),
						DatatypeConstants.FIELD_UNDEFINED);

		return xmlDate;

	}

	/**
	 * @param user
	 * @param role
	 * @return
	 */
	public static boolean hasUserRole(AuthUser user, String role) {

		for (GrantedAuthority ga : user.getAuthorities()) {
			if (ga.getAuthority().equalsIgnoreCase(role)) {
				return true;
			}
		}

		return false;
	}

	/**
	 * 
	 * @param user
	 * @param roles
	 * @return
	 */
	public static boolean hasCustomRoles(AuthUser user, List<String> roles) {

		for (Authority auth : user.getCustomAuthorities()) {
			if (roles.contains(auth.getAuthority()))
				return true;
		}

		return false;
	}

	/**
	 * Return true if the logged in user belongs to a Faculty Wide Unit.
	 * 
	 * @return
	 */
	public static boolean isUserInFacultyWideUnit() {
		return getAuthUser().getDepartment().getInternalType() == Department.INTERNAL_TYPE_FACULTY_WIDE_UNIT;
	}

	/**
	 * Utility method to construct a unique value for the key field where the
	 * Department entity is not mapped to a HRIS Organization Unit -- eg.
	 * Faculty Wide Units, Custom Departments.
	 * 
	 * @return
	 */
	public static String generateDeptKey() {
		StringBuilder sb = new StringBuilder();

		sb.append(Utils.getUsername()).append("_")
				.append(System.currentTimeMillis());

		return sb.toString();
	}

	/**
	 * Null safe method to check if Primary Dept of logged in user is the same
	 * as the Primary Dept of <code>profile</code>.
	 * 
	 * @param profile
	 * @return
	 */
	public static boolean isUserInSamePrimaryDepartmentAsProfile(Profile profile) {
		boolean b = false;

		if (profile.getExtractedFields() != null
				&& profile.getExtractedFields().getPrimaryDepartment() != null
				&& getAuthUser()
						.getDepartment()
						.getName()
						.equals(profile.getExtractedFields()
								.getPrimaryDepartment())) {
			b = true;
		}

		return b;
	}

	/**
	 * Eliminate probing of server layer by returning identical error message
	 * for possible data based attacks.
	 */
	public static void throwAuthorizationException() {
		throw new AccessDeniedException(
				"Insufficient Authorization to perform the task.");
	}

	public static boolean isUserDepartmentAdmin() {
		return isUserInRole(Authority.ROLE_DEPARTMENT_ADMINISTRATOR);
	}

	public static boolean isUserAdmin() {
		return isUserInRole(Arrays.asList(
				Authority.ROLE_DEPARTMENT_ADMINISTRATOR,
				Authority.ROLE_HR_ADMINISTRATOR,
				Authority.ROLE_DC_SYSTEM_ADMINISTRATOR));
	}

	public static boolean isNonHRCustomAuthority(String authority) {
		if (authority.equals(Authority.ROLE_EDIT_DATA)
				|| authority.equals(Authority.ROLE_VIEW_SENSITIVE_DATA)
				|| authority.equals(Authority.ROLE_EMAIL_ACCESS_PERMISSION)
				|| authority.equals(Authority.ROLE_MANAGE_DEPARTMENT_DATA)
				|| authority.equals(Authority.ROLE_VIEW_STAFF)) {
			return true;
		}
		return false;
	}

	public static boolean isUserPermittedToGrantCustomAuthority(String authority) {
		if (isNonHRCustomAuthority(authority) && isUserAdmin()) {
			return true;
		}
		/**
		 * We do not create Dept Admin Role as a custom authority. Instead we
		 * add the user to the Dept Admins group.
		 */
		// if (authority.equals(Authority.ROLE_DEPARTMENT_ADMINISTRATOR)
		// && Utils.isUserInRole(Arrays.asList(
		// Authority.ROLE_DC_SYSTEM_ADMINISTRATOR,
		// Authority.ROLE_HR_ADMINISTRATOR))) {
		// return true;
		// }
		if ((authority.equals(Authority.ROLE_VIEW_HRIS_RESTRICTED) || authority
				.equals(Authority.ROLE_VIEW_HRIS_PERSONNEL_NUMBER))
				&& isUserInRole(Authority.ROLE_HR_ADMINISTRATOR)) {
			return true;
		}
		return false;
	}

	/**
	 * Simulate user input from a Date object. Useful for translating string
	 * tokens such as 'now' or 'one week ago'
	 * 
	 * @param date
	 * @return string formatted the way our HTTP client does
	 */
	public static String formatDateAsClientString(Date date) {
		String val = null;
		SimpleDateFormat formatter = new SimpleDateFormat(
				Constants.DATE_PICTURE);
		val = formatter.format(date);
		return val;
	}

	public static List<String> convertAuthoritiesToStrings(
			List<Authority> authorities) {
		if (authorities == null) {
			return null;
		}

		List<String> authorityStrings = new ArrayList<String>();
		Iterator<Authority> authorityIterator = authorities.iterator();
		while (authorityIterator.hasNext()) {
			Authority authority = authorityIterator.next();
			authorityStrings.add(authority.getAuthority());
		}
		return authorityStrings;
	}
	
	public static void preventLoggedInUse() {
		if(Utils.getAuthUser() != null) {
			throw new AccessDeniedException("This method is for background processes only");
		}
	}

}
